import request from '@/utils/request'

// 查询成交合同列表
export function listContract(query) {
  return request({
    url: '/transaction/contract/list',
    method: 'get',
    params: query
  })
}

// 查询成交合同详细
export function getContract(contractId) {
  return request({
    url: '/transaction/contract/' + contractId,
    method: 'get'
  })
}

// 新增成交合同
export function addContract(data) {
  return request({
    url: '/transaction/contract',
    method: 'post',
    data: data
  })
}

// 修改成交合同
export function updateContract(data) {
  return request({
    url: '/transaction/contract',
    method: 'put',
    data: data
  })
}

// 删除成交合同
export function delContract(contractId) {
  return request({
    url: '/transaction/contract/' + contractId,
    method: 'delete'
  })
}
