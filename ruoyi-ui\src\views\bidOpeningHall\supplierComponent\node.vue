<!-- 评审进度节点 -->
<template>
  <div class="end-item">
    <div class="end-name" :class="styleJudgment">{{ lable }}</div>
    <div class="end-icon" v-if="lable != '投标报价打分'">
      <div class="end-graphics" :class="styleJudgment"></div>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    lable: "",
    type: 0,
  },
  data() {
    //这里存放数据
    return {};
  },
  //监听属性 类似于data概念
  computed: {
    // 样式判断
    styleJudgment() {
      if (this.type == 0) {
        return "not-started";
      } else if (this.type == 1) {
        return "ongoing";
      } else if (this.type == 2) {
        return "completed";
      }
    },
  },
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },
  beforeCreate() { }, //生命周期 - 创建之前
  beforeMount() { }, //生命周期 - 挂载之前
  beforeUpdate() { }, //生命周期 - 更新之前
  updated() { }, //生命周期 - 更新之后
  beforeDestroy() { }, //生命周期 - 销毁之前
  destroyed() { }, //生命周期 - 销毁完成
  activated() { }, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.end-item {
  display: flex;
  justify-content: space-around;
  width: 20%;
  .end-name {
    display: flex;
    align-items: center;
    justify-content: center;

    // width: 110px;
    padding: 0 10px 0;
    height: 40px;
    border-radius: 5px;
  }
  .end-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 40px;
    .end-graphics {
      width: 60%;
      height: 50%;
      clip-path: polygon(0 0, 50% 0, 100% 50%, 50% 100%, 0 100%, 50% 50%);
    }
  }
  .not-started {
    background: #ededed;
    color: #000;
  }
  .ongoing {
    background: rgb(204, 156, 0);
    color: #fff;
  }
  .completed {
    background: #176adb;
    color: #fff;
  }
}
</style>