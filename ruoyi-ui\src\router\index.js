import Vue from "vue";
import Router from "vue-router";

Vue.use(Router);

/* Layout */
import Layout from "@/layout";
import CodePage from '@/views/codePage/index.vue';
/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect"),
      },
    ],
  },
  {
    path: '/qrcode-result/:qrcodeInfo',
    name: 'QrCodeResult',
    component: CodePage,
    meta: {
      // 新增一个自定义字段，标记该路由不需要登录验证
      requiresAuth: false
    }
  },
 /* {
    path: "/expertLogin",
    component: () => import("@/views/expertReview/login"),
    hidden: true,
  },*/

  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/home",
    component: () => import("@/views/home"),
    hidden: true,
  },
  {
    path: "/list",
    component: () => import("@/views/list"),
    hidden: true,
  },
  {
    path: "/information",
    component: () => import("@/views/information"),
    hidden: true,
  },
  {
    path: "/information2",
    component: () => import("@/views/information2"),
    hidden: true,
  },
  {
    path: "/bidOpeningHall",
    component: () => import("@/views/bidOpeningHall"),
    hidden: true,
  },
  {
    path: "/expertConfirm",
    component: () => import("@/views/expertConfirm"),
    hidden: true,
  },
  {
    path: "/suppliersRoom",
    component: () => import("@/views/bidOpeningHall/suppliersRoom"),
    hidden: true,
  },
  {
    path: "/agentRoom",
    component: () => import("@/views/bidOpeningHall/agentRoom"),
    hidden: true,
  },
  {
    path: "/expertLogin",
    component: () => import("@/views/expertReview/login"),
    hidden: true,
  },
  {
    path: "/expertHome",
    component: () => import("@/views/expertReview/home"),
    hidden: true,
  },
  {
    path: "/expertInfo",
    component: () => import("@/views/expertReview/info"),
    hidden: true,
  },
  {
    path: "/secondOffer",
    component: () => import("@/views/expertReview/secondOffer"),
    hidden: true,
  },
  {
    path: "/bidInquiry",
    component: () => import("@/views/expertReview/bidInquiry"),
    hidden: true,
  },
  {
    path: "/summaryConfirm",
    component: () => import("@/views/expertReview/summaryConfirm"),
    hidden: true,
  },
  {
    path: "/summary",
    component: () => import("@/views/expertReview/summary"),
    hidden: true,
  },
  {
    path: "/qualification",
    component: () => import("@/views/expertReview/qualification"),
    hidden: true,
  },
  {
    path: "/compliance",
    component: () => import("@/views/expertReview/compliance"),
    hidden: true,
  },
  {
    path: "/technical",
    component: () => import("@/views/expertReview/technical"),
    hidden: true,
  },
  {
    path: "/business",
    component: () => import("@/views/expertReview/business"),
    hidden: true,
  },
  {
    path: "/tenderOffer",
    component: () => import("@/views/expertReview/tenderOffer"),
    hidden: true,
  },
  {
    path: "/fileResponse",
    component: () => import("@/views/fileResponse/index2"),
    hidden: true,
  },
  {
    path: "/fileResponseCreate",
    component: () => import("@/views/fileResponse/index2"),
    hidden: true,
  },
  {
    path: "/docResponseEntInfo",
    component: () => import("@/views/fileResponse/edit"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/404",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "",
    component: Layout,
    redirect: "index",
    children: [
      {
        path: "index",
        component: () => import("@/views/index"),
        name: "Index",
        meta: { title: "首页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/bid/opening",
    component: Layout,
    hidden: true,
    permissions: ["bid:opening:add"],
    children: [
      {
        path: "add",
        component: () => import("@/views/bid/opening/add"),
        name: "bidOpeningAdd",
        meta: { title: "新增开标情况", activeMenu: "/bid/opening" },
      },
    ],
  },
  {
    path: "/bid/opening",
    component: Layout,
    hidden: true,
    permissions: ["bid:opening:query"],
    children: [
      {
        path: "detail/:bidOpeningId(\\d+)",
        component: () => import("@/views/bid/opening/detail"),
        name: "bidOpeningDetail",
        meta: { title: "查看开标情况", activeMenu: "/bid/opening" },
      },
    ],
  },
  {
    path: "/process/info",
    component: Layout,
    hidden: true,
    permissions: ["process:info:add"],
    children: [
      {
        path: "add/:processId(\\d+)",
        component: () => import("@/views/process/info/add"),
        name: "processInfoAdd",
        meta: { title: "新增全流程信息归档", activeMenu: "/process/info" },
      },
    ],
  },
  {
    path: "/bidder/notice",
    component: Layout,
    hidden: true,
    permissions: ["bidder:notice:add"],
    children: [
      {
        path: "add/:noticeId(\\d+)",
        component: () => import("@/views/bidder/notice/add"),
        name: "bidderNoticeAdd",
        meta: { title: "新增成交结果公告", activeMenu: "/bidder/notice" },
      },
    ],
  },
  {
    path: "/bidder/notice",
    component: Layout,
    hidden: true,
    permissions: ["bidder:notice:query"],
    children: [
      {
        path: "view/:noticeId(\\d+)",
        component: () => import("@/views/bidder/notice/view"),
        name: "bidderNoticeView",
        meta: { title: "查看成交结果公告", activeMenu: "/bidder/notice" },
      },
    ],
  },
  {
    path: "/process/info",
    component: Layout,
    hidden: true,
    permissions: ["process:info:edit"],
    children: [
      {
        path: "edit/:processId(\\d+)",
        component: () => import("@/views/process/info/edit"),
        name: "processInfoEdit",
        meta: { title: "修改全流程信息归档", activeMenu: "/process/info" },
      },
    ],
  },
  {
    path: "/bidder/advice",
    component: Layout,
    hidden: true,
    permissions: ["bidder:advice:query"],
    children: [
      {
        path: "detail/:adviceNoteId(\\d+)",
        component: () => import("@/views/bidder/advice/detail"),
        name: "bidderAdviceDetail",
        meta: { title: "查看成交通知书", activeMenu: "/bidder/advice" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:view"],
    children: [
      {
        path: "mainview/:noticeId(\\d+)",
        component: () => import("@/views/tender/notice/mainview"),
        name: "noticeMainView",
        meta: { title: "采购公告查看", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:view"],
    children: [
      {
        path: "view/:noticeId(\\d+)",
        component: () => import("@/views/tender/notice/view"),
        name: "noticeView",
        meta: { title: "采购公告查看", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:add"],
    children: [
      {
        path: "add/:noticeId(\\d+)",
        component: () => import("@/views/tender/notice/add"),
        name: "noticeAdd",
        meta: { title: "新增采购公告", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/filePreparation",
    component: Layout, // 这里根据你的布局组件进行设置
    hidden: true,
    permissions: ["tender:notice:add"],
    children: [
      {
        path: "index",
        component: () => import("@/views/filePreparation/index"),
        name: "filePreparation",
        meta: { title: "采购文件制作" },
      },
    ],
  },
  {
    path: "/filePreparationInfo",
    component: Layout, // 这里根据你的布局组件进行设置
    hidden: true,
    permissions: ["tender:notice:add"],
    children: [
      {
        path: "index",
        component: () => import("@/views/filePreparation/info"),
        name: "filePreparationInfo",
        meta: { title: "采购文件制作" },
      },
    ],
  },
  {
    path: "/filePreparationGoodsInfo",
    component: Layout, // 这里根据你的布局组件进行设置
    hidden: true,
    permissions: ["tender:notice:add"],
    children: [
      {
        path: "index",
        component: () => import("@/views/filePreparation_goods/info"),
        name: "filePreparationGoodsInfo",
        meta: { title: "采购文件制作" },
      },
    ],
  },
  {
    path: "/filePreparationServiceInfo",
    component: Layout, // 这里根据你的布局组件进行设置
    hidden: true,
    permissions: ["tender:notice:add"],
    children: [
      {
        path: "index",
        component: () => import("@/views/filePreparation_service/info"),
        name: "filePreparationServiceInfo",
        meta: { title: "采购文件制作" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:change"],
    children: [
      {
        // path: "change/:noticeId(\\d+)",
        path: "change/:noticeId?/:type",

        component: () => import("@/views/tender/notice/change"),
        name: "noticeChange",
        meta: { title: "变更采购公告", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:view"],
    children: [
      {
        path: "changeview/:noticeId(\\d+)",
        component: () => import("@/views/tender/notice/changeview"),
        name: "noticeChangeView",
        meta: { title: "变更公告查看", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/tender/notice",
    component: Layout,
    hidden: true,
    permissions: ["tender:notice:view"],
    children: [
      {
        path: "noticeview/:projectId(\\d+)",
        component: () => import("@/views/tender/notice/noticeview"),
        name: "noticeviews",
        meta: { title: "采购公告查看", activeMenu: "/tender/notice" },
      },
    ],
  },
  {
    path: "/tender/project",
    component: Layout,
    hidden: true,
    permissions: ["tender:project:add"],
    children: [
      {
        path: "add/:projectId(\\d+)",
        component: () => import("@/views/tender/project/add"),
        name: "projectAdd",
        meta: { title: "新增项目", activeMenu: "/tender/project" },
      },
    ],
  },
  {
    path: "/tender/project",
    component: Layout,
    hidden: true,
    permissions: ["tender:project:query"],
    children: [
      {
        path: "view/:projectId(\\d+)",
        component: () => import("@/views/tender/project/view"),
        name: "projectView",
        meta: { title: "查看项目", activeMenu: "/tender/project" },
      },
    ],
  },
  {
    path: "/tender/intention",
    component: Layout,
    hidden: true,
    permissions: ["tender:intention:add"],
    children: [
      {
        path: "add/:intentionId?/:isFormDisabled",
        component: () => import("@/views/tender/intention/add"),
        name: "intentionAdd",
        meta: { title: "新增采购意向", activeMenu: "/tender/intention" },
      },
    ],
  },
  {
    path: "/tender/intention",
    component: Layout,
    hidden: true,
    permissions: ["tender:intention:query"],
    children: [
      {
        path: "view/:intentionId(\\d+)",
        component: () => import("@/views/tender/intention/view"),
        name: "intentionView",
        meta: { title: "查看采购意向", activeMenu: "/tender/intention" },
      },
    ],
  },
  {
    path: "/tender/intention",
    component: Layout,
    hidden: true,
    permissions: ["tender:intention:query"],
    children: [
      {
        path: "pdftest",
        component: () => import("@/views/tender/intention/pdftest"),
        name: "pdftest",
        meta: { title: "查看成交结果公告", activeMenu: "/tender/intention" },
      },
    ],
  },
  /* {
    path: "/tender/intention",
    component: Layout,
    hidden: true,
    permissions: ["tender:intention:add"],
    children: [
      {
        path: "add/:projectId(\\d+)",
        component: () => import("@/views/tender/intention/add"),
        name: "intentionAdd",
        meta: { title: "新增采购意向", activeMenu: "/tender/intention" },
      },
    ],
  },*/
  {
    path: "/bidding/record",
    component: Layout,
    hidden: true,
    permissions: ["bidding:record:add"],
    children: [
      {
        path: "add/:biddingId?/:isFormDisabled",
        component: () => import("@/views/bidding/record/add"),
        name: "recordAdd",
        meta: { title: "上传响应附件", activeMenu: "/bidding/record" },
      },
    ],
  },
  {
    path: "/bid/evaluation",
    component: Layout,
    hidden: true,
    permissions: ["bid:evaluation:add"],
    children: [
      {
        path: "add/:bidEvaluationId?/:isFormDisabled",
        component: () => import("@/views/bid/evaluation/add"),
        name: "evaluationAdd",
        meta: { title: "评审记录新增", activeMenu: "/bid/evaluation" },
      },
    ],
  },
  {
    path: "/expert/apply",
    component: Layout,
    hidden: true,
    permissions: ["expert:apply:add"],
    children: [
      {
        path: "add/:applyId(\\d+)",
        component: () => import("@/views/expert/apply/add"),
        name: "applyAdd",
        meta: { title: "评标委员会", activeMenu: "/expert/apply" },
      },
    ],
  },
  {
    path: "/expert/result",
    component: Layout,
    hidden: true,
    permissions: ["expert:result:query"],
    children: [
      {
        path: "view/:applyId(\\d+)",
        component: () => import("@/views/expert/result/view"),
        name: "resultView",
        meta: { title: "查看专家抽取结果", activeMenu: "/expert/view" },
      },
    ],
  },
  {
    path: "/transaction/contract",
    component: Layout,
    hidden: true,
    permissions: ["transaction:contract:add"],
    children: [
      {
        path: "add/:contractId(\\d+)",
        component: () => import("@/views/transaction/contract/add"),
        name: "contractAdd",
        meta: { title: "新增成交合同", activeMenu: "/transaction/add" },
      },
    ],
  },
  {
    path: "/cancel/project",
    component: Layout,
    hidden: true,
    permissions: ["cancel:project:add"],
    children: [
      {
        path: "add/:cancelId",
        component: () => import("@/views/cancel/project/add"),
        name: "cancelProjectView",
        meta: { title: "新增取消采购项目", activeMenu: "/cancel/add" },
      },
    ],
  },
  {
    path: "/cancel/project",
    component: Layout,
    hidden: true,
    permissions: ["cancel:project:query"],
    children: [
      {
        path: "view/:cancelId",
        component: () => import("@/views/cancel/project/view"),
        name: "cancelProjectAdd",
        meta: { title: "查看取消采购项目", activeMenu: "/cancel/view" },
      },
    ],
  },
  {
    path: "/transaction/contract",
    component: Layout,
    hidden: true,
    permissions: ["transaction:contract:query"],
    children: [
      {
        path: "view/:contractId(\\d+)",
        component: () => import("@/views/transaction/contract/view"),
        name: "contractView",
        meta: { title: "查看成交合同", activeMenu: "/transaction/view" },
      },
    ],
  },
  {
    path: "/ent",
    component: Layout,
    hidden: true,
    permissions: ["ent:info:edit"],
    children: [
      {
        path: "audit/:entId(\\d+)",
        component: () => import("@/views/ent/audit/index"),
        name: "entAudit",
        meta: { title: "企业申请", activeMenu: "/transaction/view" },
      },
    ],
  },
  {
    path: "/fileResponse",
    component: Layout,
    hidden: true,
    permissions: ["bidding:record:add"],
    children: [
      {
        path: "edit/:id(\\d+)",
        component: () => import("@/views/fileResponse/edit"),
        name: "fileresponseEdit",
        meta: { title: "磋商工程响应文件编制", activeMenu: "/fileResponse" },
      },
    ],
  },
  {
    path: "/fileResponse",
    component: Layout,
    hidden: true,
    permissions: ["bidding:record:add"],
    children: [
      {
        path: "editgoods/:id(\\d+)",
        component: () => import("@/views/fileResponse/edit_goods"),
        name: "fileresponseGoodsEdit",
        meta: { title: "磋商货物响应文件编制", activeMenu: "/fileResponse" },
      },
    ],
  },
  {
    path: "/fileResponse",
    component: Layout,
    hidden: true,
    permissions: ["bidding:record:add"],
    children: [
      {
        path: "editService/:id(\\d+)",
        component: () => import("@/views/fileResponse/edit_service"),
        name: "fileresponseInquiryPriceEdit",
        meta: { title: "磋商服务响应文件编制", activeMenu: "/fileResponse" },
      },
    ],
  },
  {
    path: "/fileResponse",
    component: Layout,
    hidden: true,
    permissions: ["bidding:record:add"],
    children: [
      {
        path: "inquiryPrice/:id(\\d+)",
        component: () => import("@/views/fileResponse/inquiryPrice"),
        name: "inquiryPriceGoodsDocEdit",
        meta: { title: "询价货物响应文件编制", activeMenu: "/fileResponse" },
      },
    ],
  },
];
// 防止连续点击多次路由报错
let routerPush = Router.prototype.push;
let routerReplace = Router.prototype.replace;
// push
Router.prototype.push = function push(location) {
  return routerPush.call(this, location).catch((err) => err);
};
// replace
Router.prototype.replace = function push(location) {
  return routerReplace.call(this, location).catch((err) => err);
};
console.log(constantRoutes);
export default new Router({
  mode: "history", // 去掉url中的#
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
});
