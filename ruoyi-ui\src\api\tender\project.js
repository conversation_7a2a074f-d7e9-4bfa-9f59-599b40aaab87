import request from '@/utils/request'

// 初始化新项目的用户评分办法因素
export function initNewProjectUitem(projectId) {
  return request({
    url: '/tender/project/initNewProjectUitem/'+ projectId,
    method: 'post'
  })
}

// 查询采购项目信息列表
export function listProject(query) {
  return request({
    url: '/tender/project/list',
    method: 'get',
    params: query
  })
}
export function listProjectByExtractionType(query) {
  return request({
    url: '/tender/project/getListByExtractionType',
    method: 'get',
    params: query
  })
}

export function supplierLookProdect(query) {
  return request({
    url: '/tender/project/supplierLookProdect',
    method: 'get',
    params: query
  })
}

export function supplierViewProdect(query) {
  return request({
    url: '/tender/project/supplierViewProdect',
    method: 'get',
    params: query
  })
}

export function supplierisWinProdect(query) {
  return request({
    url: '/tender/project/supplierisWinProdect',
    method: 'get',
    params: query
  })
}

// 查询采购项目信息详细
export function getProjectInfo2(projectId) {
  return request({
    url: '/tender/project/getInfo?projectId='+projectId,
    method: 'get'
  })
}
// 查询采购项目信息详细
export function getProjectCodePage(projectId) {
  return request({
    url: '/tender/project/codePage/' + projectId,
    method: 'get'
  })
}
// 查询采购项目信息详细
export function getProject(projectId) {
  return request({
    url: '/tender/project/' + projectId,
    method: 'get'
  })
}

// 新增采购项目信息
export function addProject(data) {
  return request({
    url: '/tender/project',
    method: 'post',
    data: data
  })
}

// 修改采购项目信息
export function updateProject(data) {
  return request({
    url: '/tender/project',
    method: 'put',
    data: data
  })
}

// 删除采购项目信息
export function delProject(projectId) {
  return request({
    url: '/tender/project/' + projectId,
    method: 'delete'
  })
}
