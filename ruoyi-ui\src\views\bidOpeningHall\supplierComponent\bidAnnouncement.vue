<!-- 供应商唱标 -->
<template>
  <div class="bidAnnouncement">
    <el-table
      :data="bidAnnouncement"
      border
      style="width: 100%"
      :header-cell-style="headStyle"
      :cell-style="cellStyle"
    >
      <el-table-column
        type="index"
        label="序号"
        width="100"
      >
      </el-table-column>
      <el-table-column
        prop="busiBidderInfo.bidderName"
        label="投标供应商"
      >
      </el-table-column>
      <el-table-column
        prop="bidAmount"
        label="投标报价"
      >
      </el-table-column>
      <el-table-column
        prop="busiBidderInfo.bidContactPerson"
        label="负责人"
      >
      </el-table-column>
      <el-table-column
        prop="busiBidderInfo.legalPerson"
        label="法人"
      >
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { dataList } from "@/api/onlineBidOpening/info";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      bidAnnouncement: [],
      headStyle: {
        "text-align": "center",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
      },
      cellStyle: {
        "text-align": "center",
        height: "90px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 初始化投标人公示+标书解密列表+唱标列表
    initdataList() {
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.bidAnnouncement = response.data;
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.initdataList();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.bidAnnouncement {
  padding: 64px 25px;
}
</style>