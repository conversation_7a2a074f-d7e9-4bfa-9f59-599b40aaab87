import request from '@/utils/request'

// 查询专家组列表
export function listGroup(query) {
  return request({
    url: '/expert/group/list',
    method: 'get',
    params: query
  })
}

// 查询专家组详细
export function getGroup(groupId) {
  return request({
    url: '/expert/group/' + groupId,
    method: 'get'
  })
}

// 新增专家组
export function addGroup(data) {
  return request({
    url: '/expert/group',
    method: 'post',
    data: data
  })
}

// 修改专家组
export function updateGroup(data) {
  return request({
    url: '/expert/group',
    method: 'put',
    data: data
  })
}

// 删除专家组
export function delGroup(groupId) {
  return request({
    url: '/expert/group/' + groupId,
    method: 'delete'
  })
}
