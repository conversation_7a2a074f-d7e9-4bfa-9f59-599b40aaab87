import request from '@/utils/request'

// 查询专家抽取回避列表
export function listEvade(query) {
  return request({
    url: '/expert/evade/list',
    method: 'get',
    params: query
  })
}

// 查询专家抽取回避详细
export function getEvade(evadeId) {
  return request({
    url: '/expert/evade/' + evadeId,
    method: 'get'
  })
}

// 新增专家抽取回避
export function addEvade(data) {
  return request({
    url: '/expert/evade',
    method: 'post',
    data: data
  })
}

// 修改专家抽取回避
export function updateEvade(data) {
  return request({
    url: '/expert/evade',
    method: 'put',
    data: data
  })
}

// 删除专家抽取回避
export function delEvade(evadeId) {
  return request({
    url: '/expert/evade/' + evadeId,
    method: 'delete'
  })
}
