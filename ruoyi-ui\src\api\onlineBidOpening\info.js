import request from "@/utils/request";

// 开标大厅首页list接口
export function list(query) {
  return request({
    url: "/tender/notice/duringBidOpeninglist",
    method: "get",
    params: query,
  });
}
// 根据项目id获取开标室信息
export function bidInfo(query) {
  return request({
    url: "/tender/notice/duringBidOpeningInfo",
    method: "get",
    params: query,
  });
}

// 供应商签到接口
export function signIn(data) {
  return request({
    url: "/bidding/info/signIn",
    method: "post",
    data: data,
  });
}

// 投标人公示、标书解密列表
export function dataList(projectId) {
  return request({
    url: `/bidding/info/bidderAnnouncement/${projectId}`,
    method: "get",
  });
}

// 保存操作记录
export function operationRecord(data) {
  return request({
    url: "/operation/record",
    method: "post",
    data: data,
  });
}

// 供应商解密
export function suppliersDecryption(data) {
  return request({
    url: "/bidding/info/updateBidderInfo",
    method: "post",
    data: data,
  });
}
// 开标大厅聊天记录获取
export function chatHistory(projectId) {
  return request({
    url: `/message/record/historyMessages/${projectId}`,
    method: "get",
  });
}
// 查询当前开标室所处的状态
export function getProjectStatus(data) {
  return request({
    url: `/operation/record/getProjectStatus`,
    method: "post",
    data: data,
  });
}
// 打印开标结果

export function exportBidOpeningRecords(projectId) {
  return request({
    url: "/bidding/info/exportBidOpeningRecords?projectId="+projectId,
    method: "get"
  });
}


// http://localhost:8080/tender/project//openRecord
