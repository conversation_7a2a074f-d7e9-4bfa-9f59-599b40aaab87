<!-- 供应商开标结束 -->
<template>
  <div class="end">
    <div class="end-line-one">
      <div class="closingRemarks">
        <div class="end-headline">
          开标已结束
        </div>
        <div class="end-content">
          评审过程中可能存在询标或二次报价等操作，请在此页面等待评审结束
        </div>
      </div>
    </div>
    <div class="end-line-two"></div>
    <div class="end-line-three">
      <div class="end-title">评审专家评审进度</div>
      <div class="end-process">
        <node v-for="(item,index) in progress" :key="index" :lable="getItemLabel(item)" :type="item.status"></node>
      </div>
    </div>
    <!-- 新增这个属性 -->
    <el-dialog :visible.sync="bidInquiryVisible" width="60%" :close-on-click-modal="false" :show-close="false" >
      <div>
        <div class="exper-title">专家询标 </div>
        <div style="display:flex;padding:15px">
          <div style="margin-right:5px;width:60%">
            <div class="exper-title" style="height: 40px;background: #1c57a7;">询标内容</div>
            <div style="height:320px">
              <el-input :disabled="true" class="text" type="textarea" :rows="15" placeholder="询标内容" v-model="question">
              </el-input>
            </div>
          </div>
          <div style="margin-left:5px;width:40%">
            <div class="exper-title" style="height: 40px;background: #1c57a7;">回复内容 (<el-statistic
                @finish="hilarity"
                :value="serverTime"
                format="HH:mm:ss"
                time-indices
                title="回复倒计时"
            >
            </el-statistic>)</div>
            <div style="height:320px">
              <el-input class="text" type="textarea" :rows="12" placeholder="请输入回复内容" v-model="answer">
              </el-input>
              <div style="display:flex;justify-content: center;align-items: center;padding: 15px;">
                <div style="width:50%;display:flex;justify-content: center;align-items: center">
                  <div v-if="replyFile == ''">
                    <FileUploadSpecial @input="uploadFile($event)" :fileType="['pdf']" :isUpdate="false"></FileUploadSpecial>
                  </div>
                  <div style="display:flex" v-else>
                    <el-button style="margin-right:10px" class="quote-button" @click="viewFile(replyFile)">
                      查看文件
                    </el-button>
                    <FileUploadSpecial @input="uploadFile($event)" :fileType="['pdf']" :isUpdate="true"></FileUploadSpecial>
                  </div>

                </div>
                <div style="width:50%;display:flex;justify-content: center;align-items: center">
                  <el-button class="quote-button" @click="messageReply">回 复</el-button>
                </div>

              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import node from "./node.vue";
import { approvalProcess,inquiringBidList } from "@/api/expert/review";
import { getProject } from "@/api/tender/project";



import webSocketFactory from "@/utils/WebSocketFactory";

const expertList = JSON.parse(localStorage.getItem("expertList"));
const userInfo = JSON.parse(localStorage.getItem("userInfo"));
const baseUrl = process.env.VUE_APP_BASE_API;
const socketUrl = baseUrl.replace("http", "ws");
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { node },
  data() {
    //这里存放数据
    return {
      serverTime:Date.now() + 0,
      progress: [
        {
          itemName: "资格性评审",
          status: 0,
        },
        {
          itemName: "符合性评审",
          status: 0,
        },
        {
          itemName: "技术标评审",
          status: 0,
        },
        {
          itemName: "商务标评审",
          status: 0,
        },
        {
          itemName: "投标报价打分",
          status: 0,
        },
      ],
      bidInquiryVisible: false,
      question: "",
      answer: "",
      replyFile: "",
      expertMessage: {},
      endTime: "",
      num: "0",
      projectInfo:{}
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
  hilarity(){
      //倒计时截止提示
      this.bidInquiryVisible = false;
      this.serverTime = Date.now()+0;
    },

    getItemLabel(item) {
      if (item.itemName === "投标报价打分" && this.projectInfo.tenderMode != 3) {
        return "投标报价";
      }
      return item.itemName;
    },
    // 获取专家评审进度
    getExpertReviewProgress() {
      approvalProcess(this.$route.query.projectId, expertList[0].resultId).then(
        (response) => {
          if (response.code == 200 && response.data.scoringMethodUinfo.scoringMethodItems) {
            console.log("response.data : ", response.data);
            
           this.progress = response.data.scoringMethodUinfo.scoringMethodItems;
            const existingItemNames = response.data.scoringMethodUinfo.scoringMethodItems.map(item => item.itemName);
            console.log("existingItemNames : ", existingItemNames);
            this.progress = this.progress.filter(item => existingItemNames.includes(item.itemName));

            console.log("this.progress : ", this.progress);
            const evalProjectEvaluationProcess = this.progress.find((item) => {
              return item.itemName == "投标报价打分";
            }).evalProjectEvaluationProcess;
            console.log("evalProjectEvaluationProcess", evalProjectEvaluationProcess);
            
            if (evalProjectEvaluationProcess) {
              let startTime = new Date(
                evalProjectEvaluationProcess.startTime.replace(" ", "T") + "Z"
              );
              // 将 Date 对象加上30秒
              startTime.setMinutes(startTime.getMinutes() + evalProjectEvaluationProcess.minutes);
              this.endTime = startTime
                .toISOString()
                .replace("T", " ")
                .replace("Z", "")
                .split(".")[0];
              // 将更新后的时间转换回字符串格式
              this.num = evalProjectEvaluationProcess.num;
              this.$emit("sendData", {
                startTime: evalProjectEvaluationProcess.startTime,
                endTime: this.endTime,
                num: this.num,
              });
            }
          } else {

          }
        }
      );

    },
    // 上传文件
    uploadFile(date) {
      let dateArray = date.split(",");
      let lastElement = dateArray[dateArray.length - 1];
      this.replyFile = baseUrl + lastElement;
    },
    // 查看文件
    viewFile(fileUrl) {
      window.open(fileUrl, "_blank");
    },
    // 回复
    messageReply() {
      if (this.answer === "" && this.replyFile === "") {
        // 如果 this.answer 和 this.replyFile 都为空
        this.$message.warning("请输入回复文字");
      } else if (this.answer === "") {
        // 如果只有 this.answer 为空
        this.$message.warning("请输入回复文字");
      } else if (this.replyFile === "") {
        // 如果只有 this.replyFile 为空
        this.$message.warning("请上传回复文件");
      } else {
        this.expertMessage.evalInquiringBidInfo.replyContent = this.answer;
        this.expertMessage.evalInquiringBidInfo.replyFile = this.replyFile;
        webSocketFactory.send(
          userInfo.entId,
          JSON.stringify(this.expertMessage)
        );
        this.bidInquiryVisible = false;
        this.serverTime = Date.now() + 0;
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    // 创建 WebSocket 连接,每个专家创建一条
    var projctID=this.$route.query.projectId;
    this.projectInfo=getProject(projctID);
    console.log(this.projectInfo);
    // expertList.map((expertInfo) => {
      webSocketFactory.createConnection(
        userInfo.entId,
        `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${userInfo.entId}/${this.$route.query.projectId}/1`
      );
      webSocketFactory.on(userInfo.entId, "messageReceived", (payload) => {
        console.log(123213,payload)
        if ("replyContent" in payload.evalInquiringBidInfo) {
          this.bidInquiryVisible = false;
        } else {
          this.bidInquiryVisible = true;
          this.expertMessage = payload;
          this.question = payload.message;
          this.serverTime = Date.now() + 1000 * 60 * 20
        }
      });

    //   var data={
    //     "projectId":this.$route.query.projectId,
    //     "entId": userInfo.entId
    //   }
    // console.log(data)
    //   inquiringBidList(data).then(
    //     (response) => {
    //       console.log(response);
    //       const lastItem = response.data[response.data.length - 1];
    //       if (lastItem.replyContent == null || lastItem.replyContent == '') {
    //         this.bidInquiryVisible = true;
    //         this.expertMessage.evalInquiringBidInfo=lastItem
    //         this.question = lastItem.inquiringContent;
    //       } else {
    //         this.bidInquiryVisible = false;
    //       }
    //   });
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() { },
  beforeCreate() { }, //生命周期 - 创建之前
  beforeMount() { }, //生命周期 - 挂载之前
  beforeUpdate() { }, //生命周期 - 更新之前
  updated() { }, //生命周期 - 更新之后
  beforeDestroy() { }, //生命周期 - 销毁之前
  destroyed() {
    expertList.map((expertInfo) => {
      webSocketFactory.disconnect(expertInfo.xm);
    });
  }, //生命周期 - 销毁完成
  activated() { }, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.end {
  ::v-deep .el-statistic {
    width: auto;
    color: #FFFFFF;

    .head {
      color: #FFFFFF;
    }
    .con {
      color:red;
    }

  }

  .end-line-one {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 400px;

    color: #176adb;
    letter-spacing: 0;
    text-align: center;
    .closingRemarks {
      .end-headline {
        font-weight: 700;
        font-size: 35px;
        margin-bottom: 15px;
      }
      .end-content {
        font-weight: 500;
        font-size: 20px;
      }
    }
  }
  .end-line-two {
    width: 100%;
    height: 10px;
    background: #f5f5f5;
  }
  .end-line-three {
    color: #ffffff;
    letter-spacing: 0;
    text-align: center;
    .end-title {
      display: flex;
      align-items: center;
      justify-content: center;

      width: 260px;
      height: 55px;
      background: #176adb;

      font-family: SourceHanSansSC-Medium;
      font-weight: 500;
      font-size: 22px;
    }
    .end-process {
      display: flex;
      justify-content: space-between;
      padding: 30px;
    }
  }
}
.quote-button {
  width: 100px;
  background: #176adb;

  color: #fff;
  font-weight: 700;

  border-radius: 5px;
}
.exper-title {
  height: 45px;
  background: #176adb;
  display: flex;
  justify-content: center;

  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 16px;
  color: #ffffff;
  letter-spacing: 0;
  align-items: center;
}
.expert-title-second {
  height: 40px;
  background: #1c57a7;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
