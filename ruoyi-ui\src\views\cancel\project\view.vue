<template>
  <div
    class="tender"
    v-loading="loading"
  >
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>项目信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >采购项目</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ form.project.projectName }}
                  </div></td>
                </tr>
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >项目编号</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ form.project.projectCode }}
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
      </el-card>
    </el-col>

<el-col :span="24" class="card-box" >
  <el-card>
    <div slot="header">
      <span><i class="el-icon-document"></i>取消原因</span>
    </div>
    <div style="font-size: 14px;text-indent: 2em;" v-html="form.cancelReason">
    </div>
  </el-card>
</el-col>

    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i> 附件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr
                v-for="dict in dict.type.cancel_project_attachment"
                :key="dict.label"
              >
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;" v-if="dict.raw.isEquals==1">*</strong>{{ dict.label }}
                  </div>
                </td>
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    <template>
                      <FileUpload
                        :value="getImgPath(dict)"
                        :fileType="['pdf', 'doc', 'docx']"
                        :isShowTip="false"
                        :showOnly=true
                      ></FileUpload>
                    </template>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    
    <el-col :span="24" class="card-box" >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i>审核流程</span>
        </div>
        <div >
      <el-table v-loading="loading" :data="form.auditProcessList" style="width: 100%;margin: auto;">
        <el-table-column label="操作人员"  min-width="6" align="center" prop="operatorName" />
        <el-table-column label="操作时间"  min-width="2" align="center" prop="auditTime" />
        <el-table-column label="操作"  min-width="1" align="center" prop="auditResultName" />
        <el-table-column label="操作内容"  min-width="1" align="center" prop="auditRemark" />
      </el-table>
        </div>
      </el-card>
    </el-col>

    <div style="text-align: center">
      <el-button  v-if="form.audit" type="success" @click="processAudit(1)">审核通过</el-button>
      <el-button  v-if="form.audit" type="danger" @click="processAudit(0)">退    回</el-button>
      <el-button  @click="close()"  v-show="!isFormDisabled" >关闭</el-button>
    </div>
    
    <!-- 添加或修改成交通知书对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="auditOpen" width="50%" append-to-body>
          <el-input type="textarea" :rows="3" v-model="form.auditRemark" placeholder="请输入审核意见" />
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="audit">确 定</el-button>
        <el-button  @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { listProject, getProject, delProject, approveRequest, updateProject,listTenderProject } from "@/api/cancel/project";
import { formatDate } from "@/utils/index";
import {listByBusi} from "@/api/audit/info";

export default {
  components: {},
  dicts: ["cancel_project_attachment"],
  props: [],
  data() {
    return {
      isFormDisabled:false,
      // 遮罩层
      loading: true,
      datetime:"",
      projectIdOptions: [],
      bidOption: [],
      title:'',
      auditOpen:false,
      form: {
        cancelId: null,
        projectId: null,
        cancelReason: null,
        cancelDate: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        project:{},
        auditProcessList:[]
      },
      fileList: "",
      attachmentsMap: {},
      bidderNotice: []
    };
  },
  computed: {},
  watch: {},
  created() {
    this.checkcancelId();
  },
  mounted() {
  },
  methods: {
    checkcancelId() {
      const cancelId = this.$route.params.cancelId;
      if(cancelId != undefined){
        this.loadIntentionDetail(cancelId);
      }

    },
    loadIntentionDetail(cancelId) {
      // 加载已有意图详情的逻辑
      getProject(cancelId).then(response => {
        this.loading = false;
        console.info(response)
        this.form = response.data;
        if (typeof this.form.projectId === 'string') {
          this.form.projectId = parseInt(this.form.projectId, 10);
        }
        // 使用reduce方法将attachments数组收集成一个映射
        this.attachmentsMap = response.data.attachments.reduce(
          (accumulator, attachment) => {
            // 如果accumulator中还没有这个fileType的键，则创建一个新数组
            if (!accumulator[attachment.fileType]) {
              accumulator[attachment.fileType] = [];
            }
            // 将当前attachment添加到对应fileType的数组中
            accumulator[attachment.fileType].push(attachment);
            return accumulator;
          },
          {}
        );
      });
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    processAudit(auditResult){
      this.form.auditResult = auditResult;
      if(auditResult==1){
        this.title = "通过意见";
        this.form.auditRemark = "通过";
      }else{
        this.title = "退回意见";
        this.form.auditRemark = "";
      }
      this.auditOpen = true;
    },
    audit(){
      this.loading = true;
      approveRequest(this.form).then( (result) => {
        if(result.code==200){
        this.$message({
          message: '提交审核成功',
          type: 'success'
        });
          this.$tab.closePage();
        }else{
          this.$message.error("提交审核失败，请联系平台");
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
