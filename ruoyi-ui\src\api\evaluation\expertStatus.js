import request from "@/utils/request";

// 添加专家评审进度信息
export function addEvalExpertScoreInfo(data) {
  return request({
    url: "/evalExpertScoreInfo/addEvalExpertScoreInfo",
    method: "post",
    data: data,
  });
}
// 查询专家评审进度信息
export function getEvalExpertScoreInfo(query) {
  return request({
    url: "/evalExpertScoreInfo/getEvalExpertScoreInfo",
    method: "get",
    params: query,
  });
}
// 修改专家评审节点状态
export function editEvalExpertScoreInfo(data) {
  return request({
    url: "/evalExpertScoreInfo/editEvalExpertScoreInfo",
    method: "post",
    data: data,
  });
}
// 专家组长点击重新评审
export function reEvaluationTwo(query) {
  return request({
    url: "/evalExpertScoreInfo/reEvaluation",
    method: "get",
    params: query,
  });
}
