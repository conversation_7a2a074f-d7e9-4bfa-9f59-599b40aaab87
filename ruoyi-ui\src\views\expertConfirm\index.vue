<template>
  <div>

    <Bidheadfour @readIdCard="readIdCardResult"></Bidheadfour>
    <div class="bidOpeningHall">
      <el-card class="box-card">
        <div
          class="body"
          style="align-content: center;justify-content: center;"
        >
          <div style="width:700px">
            <el-row>
              <el-col :span="12">
                <el-row>
                  <el-col :span="12">
                    <div class="title">
                      <p>证件类型</p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.CardTypeName=='' || expertInfo.CardTypeName==null"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>
                          {{expertInfo.CardTypeName}}
                        </span>

                      </p>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="title">
                      <p>姓名</p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.name=='' || expertInfo.name==null"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>
                          {{expertInfo.name}}
                        </span>
                      </p>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="title">
                      <p>性别</p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.sex=='' || expertInfo.sex==null"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>{{expertInfo.sex}}
                        </span>
                      </p>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="title">
                      <p>
                        民族
                      </p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.nation=='' || expertInfo.nation==null"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>
                          {{expertInfo.nation}}
                        </span>
                      </p>
                    </div>
                  </el-col>
                </el-row>
                <el-row>
                  <el-col :span="12">
                    <div class="title">
                      <p>身份证号</p>
                    </div>
                  </el-col>
                  <el-col :span="12">
                    <div class="info">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.cardno=='' || expertInfo.cardno==null"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>
                          {{expertInfo.cardno}}
                        </span>
                      </p>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
              <el-col :span="12">
                <div style="display: flex;justify-content: center;align-items: center;">
                  <div style="border: 1px solid black;width:70%;height:250px">
                    <img
                      v-if="expertInfo.photobase64 =='' || expertInfo.photobase64 ==null"
                      style="width:100%;height:100%"
                      src="@/assets/avatar/avatar.png"
                      alt="tou"
                    >
                    <img
                      v-else
                      style="width:100%;height:100%"
                      :src="'data:image/png;base64,'+expertInfo.photobase64"
                      alt="404"
                    >
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-row>
                  <el-col :span="6">
                    <div class="title">
                      <p>项目信息</p>
                    </div>
                  </el-col>
                  <el-col :span="18">
                    <div style="color: #000;font-weight: bold;letter-spacing: 3px;line-height:1.8">
                      <p>
                        <el-skeleton-item
                          v-if="expertInfo.projectInfos == [] || expertInfo.projectInfos==undefined || expertInfo.projectInfos==null || expertInfo.projectInfos.length===0"
                          variant="text"
                          width="100%"
                        />
                        <span v-else>
                            <span  v-for="(item, index) in expertInfo.projectInfos" :key="index">
                              {{ item.projectName }}
                              <br>
                            </span>
                        </span>
                      </p>
                    </div>
                  </el-col>
                </el-row>
              </el-col>
            </el-row>
          </div>

        </div>
      </el-card>
    </div>

    <Foot></Foot>
  </div>
</template>

<script>
import {
  formatDateOption,
} from "@/utils/index";
import {allProject} from "@/api/expert/review.js";
export default {
  data() {
    return {
      expertInfo: {
        CardTypeName: "",
        name: "",
        sex: "",
        nation: "",
        cardno: "",
        projectInfos: [],
        photobase64: "",
      },
    };
  },
  methods: {
    readIdCardResult(data){
      console.log(123,data);
      data.projectInfos = [];
      this.expertInfo = data;
      this.getProjectByExpertCode(data.cardno);
    },
    getProjectByExpertCode(expertCode){
      allProject({zjhm:expertCode}).then((result) => {
        if(result.code === 200){
          this.expertInfo.projectInfos = result.data;
          if(this.expertInfo.projectInfos == [] || this.expertInfo.projectInfos==undefined || this.expertInfo.projectInfos==null || this.expertInfo.projectInfos.length===0){
            this.expertInfo.projectInfos = [];
            this.expertInfo.projectInfos.push({projectName: "今日暂无评审项目"});
          }
          console.log(12341234,this.expertInfo.projectInfos);
        }else{
          this.$message.error(result.message);
        }
      });
    },
    // 格式化开标时间显示
    formatBidOpeningTime(time) {
      return formatDateOption(time, "time");
    },
    // 判断当前项目的开标状态
    bidOpeningStatus(bidOpeningTime, bidOpeningEndTime) {
      const startTime = new Date(bidOpeningTime);
      const endTime = new Date(bidOpeningEndTime);

      // 获取当前时间
      const currentTime = new Date();

      // 判断当前时间是否在两个时间之间
      if (currentTime < startTime) {
        return "0";
      } else if (currentTime >= startTime && currentTime < endTime) {
        return "1";
      } else if (currentTime >= endTime) {
        return "2";
      }
    },
    // 携带项目Id跳转到开标室
    gotobidOpeningRoom(projectId) {
      console.log(this.$store.getters.roles);
      let role = true;
      if (
        this.$store.getters.roles.includes("agency") ||
        this.$store.getters.roles.includes("purchaser")
      ) {
        role = false;
      } else if (this.$store.getters.roles.includes("supplier")) {
        role = true;
      }
      if (role) {
        this.$router.push({
          path: "/suppliersRoom",
          query: {
            projectId: projectId,
          },
        });
      } else {
        this.$router.push({
          path: "/agentRoom",
          query: {
            projectId: projectId,
          },
        });
      }
    },
  },
  mounted() {},
};
</script>

<style scoped lang="scss">
.bidOpeningHall {
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.box-card {
  height: 100%;
  width: 70%;
  padding: 20px;
}
::v-deep .el-card__header {
  padding-top: 0;
}

.body {
  display: flex;
  flex-wrap: wrap;
  min-height: 530px;
}
.title {
  width: 90px;
  color: #176adb;
  font-weight: bold;
  text-align: justify;
  text-align-last: justify;

  p {
  }
}
.info {
  width: 100px;
  color: #000;
  font-weight: bold;
}
</style>