import request from '@/utils/request'

// 查询地址信息列表
export function listInfo(query) {
  return request({
    url: '/area/info/list',
    method: 'get',
    params: query
  })
}

// 查询地址信息详细
export function getInfo(areaId) {
  return request({
    url: '/area/info/' + areaId,
    method: 'get'
  })
}

// 新增地址信息
export function addInfo(data) {
  return request({
    url: '/area/info',
    method: 'post',
    data: data
  })
}

// 修改地址信息
export function updateInfo(data) {
  return request({
    url: '/area/info',
    method: 'put',
    data: data
  })
}

// 删除地址信息
export function delInfo(areaId) {
  return request({
    url: '/area/info/' + areaId,
    method: 'delete'
  })
}
