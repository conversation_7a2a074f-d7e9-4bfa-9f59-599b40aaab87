<template>
  <div class="tender" v-loading="loading">
    
    <el-col :span="24" class="card-box" style="margin-top: 20px;">
      <el-card>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">公告名称：</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.winningBidderNotice.noticeName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">项目名称：</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.projectName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">项目编号：</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.projectCode }}</div>
                </td>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">公告时间：</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.winningBidderNotice.noticeStartTime }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
      </el-col>
      
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i> 公告内容</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="6" class="el-table__cell is-leaf">
                  <div class="cell" v-html="formData.winningBidderNotice.noticeContent"></div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr v-for="dict in dict.type.busi_bidder_notice_attachment" :key="dict.label" >
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <div class="cell" >
                      <strong style="color:red;" v-if="dict.raw.isEquals==1">*</strong>{{ dict.label }}
                    </div></div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <template>
                      <FileUpload
                            :value="getFiles(dict)"
                          :fileType="['pdf', 'doc', 'docx']"
                          :isShowTip="false"
                          :showOnly="true"
                    ></FileUpload>
                    </template>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
      <div style="margin:auto;">
        <el-button type="primary" @click="closeCard">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { listProject } from "@/api/tender/project";
import { listInfo } from "@/api/bidding/info";
import { addNotice, updateNotice,view } from "@/api/bidder/notice";


export default {
  components: {},
  dicts: [
    "busi_bidder_notice_attachment",
  ],
  props: [ ],
  data() {
    return {
      // 遮罩层
      loading: true,
      formData: {
        winningBidderNotice: {attachmentMap:[]},
        tenderProject: {},
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getInfo();
  },
  methods: {
    getFiles(dict) {
        return this.formData.winningBidderNotice.attachmentMap[parseInt(dict.value)]
    },
    getInfo() {
      view(this.$route.params.noticeId).then((response) => {
        if(response.code==200){
        this.formData = response.data;
        this.loading = false;
        }
      });
    },
    closeCard(){
      this.$tab.closePage();
    }
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
