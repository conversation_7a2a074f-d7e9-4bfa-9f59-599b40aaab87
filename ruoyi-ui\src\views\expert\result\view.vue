<template>
  <div
    class="tender"
    v-loading="loading"
  >
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>专家组</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <el-table
            :cell-style="{ 'text-align': 'center' }"
            :header-cell-style="{ 'text-align': 'center' }"
            :data="formData.expertGroup"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="groupName"
              label="专家组名称"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="groupAddress"
              label="评审品目"
            >
            </el-table-column>
            <el-table-column
              prop="expertNumber"
              label="专家数量"
            >
            </el-table-column>
            <!-- <el-table-column
              label="操作"
              width="100"
            >
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  @click="redraw(scope.row)"
                  v-hasPermi="['expert:result:query']"
                >重新抽取</el-button>
              </template>
            </el-table-column> -->
          </el-table>
        </div>
      </el-card>
    </el-col>
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>回避条件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <el-table
            :cell-style="{ 'text-align': 'center' }"
            :header-cell-style="{ 'text-align': 'center' }"
            :data="formData.expertEvade"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="evadeName"
              label="单位/专家名称"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="evadeReason"
              label="回避原因"
            >
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-col>
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>专家列表</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <el-table
            :cell-style="{ 'text-align': 'center' }"
            :header-cell-style="{ 'text-align': 'center' }"
            :data="formData.expertList"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="id"
              label="专家id"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="xm"
              label="专家姓名"
              width="180"
            >
            </el-table-column>
            <el-table-column
              prop="zjhm"
              label="专家证件号"
            > </el-table-column>
            <el-table-column
              label="操作"
              width="100"
            >
              <template slot-scope="scope">
                <el-button
                  v-if="scope.row.isOwner !== 1"
                  size="mini"
                  type="text"
                  @click="redraw(scope.row)"
                  v-hasPermi="['expert:result:query']"
                >重新抽取</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-col>
    <div style="text-align: center">
      <el-button
        type="primary"
        @click="closeCard"
      >关闭</el-button>
    </div>
  </div>
</template>
<script>
import { listResult, redrawExpert } from "@/api/expert/result";
import { listGroup } from "@/api/expert/group";
import { listEvade } from "@/api/expert/evade";

export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      formData: {},
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      // 保存两个接口请求的 Promise
      const promise1 = listGroup({ applyId: this.$route.params.applyId }).then(
        (response) => {
          if (response.code === 200) {
            this.formData.expertGroup = response.rows;
          }
        }
      );
      const promise2 = listResult({ applyId: this.$route.params.applyId }).then(
        (response) => {
          if (response.code === 200) {
            this.formData.expertList = response.rows;
          }
        }
      );
      const promise3 = listEvade({ applyId: this.$route.params.applyId }).then(
        (response) => {
          if (response.code === 200) {
            this.formData.expertEvade = response.rows;
          }
        }
      );

      // 使用 Promise.all 等待两个接口请求都完成
      Promise.all([promise1, promise2, promise3])
        .then(() => {
          // 两个接口都请求完成后将 this.loading 设置为 false
          this.loading = false;
        })
        .catch((error) => {
          // 处理 Promise.all 中任何一个 Promise 请求失败的情况
          console.error(error);
        });
    },
    redraw(row) {
      this.$confirm("是否确定重新抽取专家?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          let data = {};
          if (row.id) {
            data = {
              applyId: row.applyId,
              expIds: row.id,
              projectExpertId: row.thirtyId,
            };
          } else {
            // 根据组id获取专家列表中的有相同组id的一个专家
            const expert = this.formData.expertList.find((expert) => {
              return expert.groupId == row.groupId;
            });
            data = {
              applyId: row.applyId,
              projectExpertId: expert.thirtyId,
              thirtyId: expert.thirtyId,
            };
          }

          redrawExpert(data).then((response) => {
            if (response.code === 200) {
              this.$message({
                type: "success",
                message: response.msg,
              });
              // 刷新页面
              this.$tab.refreshPage();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消",
          });
        });
    },
    closeCard() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
