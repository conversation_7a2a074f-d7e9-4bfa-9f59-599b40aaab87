<template>
  <div>
    <div class="item">
      <div class="item-title">项目信息:</div>
      <div class="item-content">{{ projectInfo.projectName }}</div>
    </div>
    <div class="item">
      <div class="item-title">投标报价:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[2].value"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">投标报价(大写):</div>
      <div class="item-content">
        <el-input :value="formatAmount(inputExtTable[2].value)"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">项目负责人:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[0].value"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">法人或授权人:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[1].value"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">工期:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[5].value"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">质保期:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[4].value"></el-input>
      </div>
    </div>
    <div class="item">
      <div class="item-title">质量标准:</div>
      <div class="item-content">
        <el-input v-model="inputExtTable[3].value"></el-input>
      </div>
    </div>
    <!-- <div class="item">
      <div class="item-title">项目编号:</div>
      <div class="item-content">{{ projectInfo.projectCode }}</div>
    </div>
    <div class="item">
      <div class="item-title">响应截止时间:</div>
      <div class="item-content">{{ projectInfo.deadLine }}</div>
    </div>
    <div class="item">
      <div class="item-title">采购方式:</div>
      <dict-tag
        :options="dict.type.busi_tender_mode"
        :value="projectInfo.tenderMode"
      />
    </div>
    <div class="item">
      <div class="item-title">采购人:</div>
      <div class="item-content">{{ projectInfo.tendererName }}</div>
    </div>
    <div class="item">
      <div class="item-title">采购人联系方式:</div>
      <div class="item-content">{{ projectInfo.tendererPhone }}</div>
    </div>
    <template v-if="projectInfo.agencyName">
      <div class="item">
        <div class="item-title">代理机构:</div>
        <div class="item-content">
          {{ projectInfo.agencyName }}
        </div>
      </div>
      <div class="item">
        <div class="item-title">代理机构联系方式:</div>
        <div class="item-content">
          {{ projectInfo.agencyPhone }}
        </div>
      </div>
    </template> -->

    <!-- <el-table
      :data="inputExtTable"
      border
      style="width: 80%; margin-left: 10%"
    >
      <el-table-column
        label="序号"
        width="100"
        align="center"
      >
        <template slot-scope="scope">
          {{ scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="openBid"
        label="开标项"
        width="180"
      >
      </el-table-column>
      <el-table-column
        prop="defaultValue"
        label="默认项"
      > </el-table-column>
      <el-table-column
        prop="remark"
        label="备注"
      > </el-table-column>
      <el-table-column
        label="内容"
        align="center"
      >
        <template slot-scope="scope">
          <el-input
            :key="scope.$index"
            v-model="inputExtTable[scope.$index].value"
            :placeholder="scope.row.defaultValue"
          ></el-input>
        </template>
      </el-table-column>
    </el-table> -->

    <div class="item">
      <div>
        <el-button class="item-button" style="background-color: rgba(142, 181, 226, 1)" @click="save">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { convertToChineseCurrency } from "@/utils/amount";
export default {
  dicts: ["busi_tender_mode"],
  data() {
    return {
      uinfo: {},
      uitem: {},
      extUitem: {},
      projectInfo: {},
      inputExtTable: [],
    };
  },
  mounted() { },
  methods: {
    // TODO 初始化信息
    init(projectId, projectInfo, uitem, uinfo, extUitem) {
      this.projectInfo = projectInfo;
      this.uinfo = uinfo;
      this.uitem = uitem;
      this.extUitem = extUitem;
      this.projectInfo = JSON.parse(this.uitem.itemContent);
      this.inputExtTable = JSON.parse(extUitem.itemContent);
      console.log("this.inputExtTable", this.inputExtTable);
      let localInput = localStorage.getItem(
        `fileResponseBasicInfo_${this.uitem.entFileItemId}`
      );
      if (localInput) {
        this.inputExtTable = JSON.parse(localInput);
      }
      let projectInfoJsonStr = localStorage.getItem(
        `fileResponseProjectInfo_${projectId}`
      );
      if (projectInfoJsonStr) {
        this.projectInfo = JSON.parse(projectInfoJsonStr);
      }
    },
    isNotEmpty(value) {
      value = value ? value : "";
      // 去掉两边空格所有空格
      value = value.replace(/^\s+|\s+$/g, "");
      // 去掉所有空格和回车换行符
      value = value.replace(/\s+/g, " ");
      // 去掉所有中文空格
      value = value.replace(/[\u3000]/g, "");
      return (
        value !== null &&
        value !== undefined &&
        value !== "" &&
        value !== "" &&
        value !== 0 &&
        value !== false
      );
    },
    //保存
    save() {
      this.inputExtTable.forEach((element) => {
        if (!element.value) {
          element.value = element.defaultValue;
        }
      });
      let isSave = this.inputExtTable.every((element) =>
        this.isNotEmpty(element.value)
      );
      if (!isSave) {
        this.$message.warning("请完善信息后提交");
        return;
      }
      localStorage.setItem(
        `fileResponseBasicInfo_${this.uitem.entFileItemId}`,
        JSON.stringify(this.inputExtTable)
      );
      this.$message.success("保存成功");
      this.projectInfo["bidInformation"] = this.inputExtTable;
      this.$emit("saveSuccess", this.projectInfo);
    },
    // 转换大写的报价金额
    formatAmount(amount) {
      return convertToChineseCurrency(amount);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 150px;
    margin-right: 20px;
    text-align: left;
  }
  .item-content {
    min-width: 100px;
  }
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
