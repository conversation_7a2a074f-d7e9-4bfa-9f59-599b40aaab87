<template>
  <div>
    <el-container style="height:100vh">
      <el-header height="100px">
        <div class="title"><svg-icon
            style="margin-right:10px"
            icon-class="pingshenzhuanjiadenglu"
          />专家评审系统</div>
      </el-header>
      <el-main>
        <div style="justify-content: flex-end;display: flex;align-items: center;height: 100%;">
          <div class="left">
          </div>
          <div class="right">
            <div class="block">
              <div class="lable">
                <div class="character">身份证号</div>
              </div>

              <div class="item"><el-input
                  v-model="idCard"
                  placeholder="请输入身份证号"
                >
                  <template slot="prepend"><span style="color:black;background-color:#F5F5F5">身份证号</span></template></el-input></div>
              <div class="item">
                <el-button
                  class="item-button"
                  style="background-color:#0159B9;width:100%"
                  @click="logIn"
                >登录</el-button>
              </div>

            </div>

          </div>
        </div>

      </el-main>
      <el-footer height="110px">
        <div>
          站主办方:鹤壁市政府采购协会 版权所有:鹤壁市政府采购协会 豫ICP备2020025475号-2
        </div>
      </el-footer>
    </el-container>
  </div>

</template>

<script>
import { expertLogin, expertResultLogin } from "@/api/expert/review";
import Cookies from "js-cookie";

export default {
  name: "expertLogin",
  data() {
    return {
      idCard: "",
    };
  },
  methods: {
    // 因为有些接口必须得登陆状态才能用，所以听从建议，这边登陆的时候默认登录admin账号

    logIn() {
      /*expertLogin({ username: this.idCard }).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertHome",
            query: { zjhm: response.data.username },
          });
        } else {
          this.$message.warning(response.msg);
        }
      });*/
      const loginForm = {
        username: "cgxhzj",
        password: "123456",
        thirdPartySecret: "65a8e27d8879283831b664bd8b7f0ad4",
      };
      Cookies.remove("username");
      Cookies.remove("password");
      Cookies.remove("rememberMe");
      this.$store
        .dispatch("Login", loginForm)
        .then(() => {
          expertResultLogin({ zjhm: this.idCard }).then((response) => {
            if (response.code == 200) {
              console.log(response);
              this.$router.push({
                path: "/expertHome",
                query: { zjhm: response.data[0].zjhm },
              });
            } else {
              this.$message.warning(response.msg);
            }
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;

  display: flex;
  .title {
    font-family: SourceHanSansSC-Heavy;
    font-weight: 900;
    font-size: 30px;
    color: #176adb;
    letter-spacing: 0;
    margin-left: 150px;
  }
}
.el-main {
  background-image: url("../../assets/images/expert-background.png");
  .left {
    width: 50%;
  }
  .right {
    width: 50%;
    height: 100%;

    display: flex;
    align-items: center;
    justify-content: center;
    .block {
      width: 500px;
      height: 300px;
      background-color: #fff;
      padding: 30px 50px;
      .lable {
        height: 30px;
        display: flex;
        justify-content: center;
        align-items: center;
        .character {
          font-family: SourceHanSansSC-Medium;
          font-weight: 500;
          font-size: 20px;
          color: #0159b9;
          letter-spacing: 0.76px;
          border-bottom: #176adb 2px solid;
        }
      }
    }
  }
}
.el-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  div {
    font-family: PingFang-SC-Medium;
    font-weight: 500;
    font-size: 18px;
    color: #666666;
    letter-spacing: 0.46px;
    text-align: center;
  }
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  font-size: 18px;
  margin-top: 50px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  height: 48px;
  color: #fff;
  &:hover {
    background-color: #024a99;
    color: #fff;
  }
}
</style>
