// src/utils/WebSocketFactory.js

class WebSocketFactory {
  constructor() {
    this.sockets = {};
    this.retryIntervals = {};
  }

  createConnection(socketName, url) {
    if (this.sockets[socketName]) {
      throw new Error(`WebSocket 连接 ${socketName} 已存在`);
    }

    const socket = new WebSocket(url);
    this.sockets[socketName] = {
      socket,
      listeners: {},
      retryCount: 0, // 重试次数
    };

    socket.onopen = () => {
      console.log(`WebSocket 连接 ${socketName} 已打开`);
      this.sockets[socketName].retryCount = 0; // 连接成功后重置重试次数
    };

    socket.onmessage = (event) => {
      if (event.data == "ping" || event.data == `连接成功`) {
        return;
      } else {
        const data = JSON.parse(event.data);
        this.trigger(socketName, "messageReceived", data);
      }
    };

    socket.onclose = (event) => {
      console.log(`WebSocket 连接 ${socketName} 已关闭`);
      if (!event.wasClean) {
        this.reconnect(socketName, url);
      }
    };

    socket.onerror = (error) => {
      console.error(`WebSocket 连接 ${socketName} 错误:`, error);
    };

    return socket;
  }

  send(socketName, data) {
    const socket = this.sockets[socketName]?.socket;
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(data);
    } else {
      console.error(`WebSocket 连接 ${socketName} 未连接，无法发送数据`);
    }
  }

  on(socketName, eventType, callback) {
    if (!this.sockets[socketName]) {
      throw new Error(`WebSocket 连接 ${socketName} 不存在`);
    }

    if (!this.sockets[socketName].listeners[eventType]) {
      this.sockets[socketName].listeners[eventType] = [];
    }
    this.sockets[socketName].listeners[eventType].push(callback);
  }

  trigger(socketName, eventType, payload) {
    const listeners = this.sockets[socketName]?.listeners[eventType];
    if (listeners) {
      listeners.forEach((callback) => callback(payload));
    }
  }

  disconnect(socketName) {
    const socket = this.sockets[socketName]?.socket;
    if (socket) {
      socket.close();
      clearInterval(this.retryIntervals[socketName]);
      delete this.sockets[socketName];
    }
  }

  reconnect(socketName, url) {
    if (this.sockets[socketName].retryCount < 5) {
      // 假设最大重试次数为5次
      this.sockets[socketName].retryCount += 1;
      console.log(
        `尝试重新连接 ${socketName}, 重试次数: ${this.sockets[socketName].retryCount}`
      );

      // 指数退避算法，增加重试间隔
      const retryInterval = Math.min(
        1000 * Math.pow(2, this.sockets[socketName].retryCount),
        30000
      );
      this.retryIntervals[socketName] = setTimeout(() => {
        this.createConnection(socketName, url);
      }, retryInterval);
    } else {
      console.error(`WebSocket 连接 ${socketName} 重试次数已达上限，放弃重连`);
    }
  }
}

const webSocketFactory = new WebSocketFactory();

export default webSocketFactory;
