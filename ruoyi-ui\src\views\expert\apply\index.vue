<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectId">
<!--        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />-->
        <el-select
          ref="selectProject"
          v-model="queryParams.projectId"
          placeholder="请选择"
          clearable
          :style="{ width: '100%' }"
          @clear="clear()"
        >
          <el-option
            v-for="(item, index) in projectIdOptions"
            :key="index"
            :label="item.projectName"
            :value="item.projectId"
            :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="0随机1自主" prop="applyMethod">
        <el-select v-model="queryParams.applyMethod" placeholder="请选择0随机1自主" clearable>
          <el-option
            v-for="dict in dict.type.busi_apply_method"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态0待提交1已提交2已抽取" prop="applyStatus">
        <el-select v-model="queryParams.applyStatus" placeholder="请选择处理状态0待提交1已提交2已抽取" clearable>
          <el-option
            v-for="dict in dict.type.expert_apply_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="抽取专家人数" prop="expertNumber">
        <el-input
          v-model="queryParams.expertNumber"
          placeholder="请输入抽取专家人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['expert:apply:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">-->
<!--        <el-button-->
<!--          type="success"-->
<!--          plain-->
<!--          icon="el-icon-edit"-->
<!--          size="mini"-->
<!--          :disabled="single"-->
<!--          @click="handleUpdate"-->
<!--          v-hasPermi="['expert:apply:edit']"-->
<!--        >修改</el-button>-->
<!--      </el-col>-->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['expert:apply:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['expert:apply:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="applyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="申请id" align="center" prop="applyId" />
      <!-- <el-table-column label="0随机1自主" align="center" prop="applyMethod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_apply_method" :value="scope.row.applyMethod"/>
        </template>
      </el-table-column> -->
      <el-table-column label="项目名称" align="center" prop="projectId" />
      <el-table-column label="项目名称" align="center" prop="project.projectName" />

      <!-- <el-table-column label="处理状态0待提交1已提交2已抽取" align="center" prop="applyStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.expert_apply_status" :value="scope.row.applyStatus"/>
        </template>
      </el-table-column> -->
      <el-table-column label="抽取申请类型" align="center" width="120">
        <template slot-scope="scope">
          <span v-if="scope.row.extractionType == 0">论证(建项目)</span>
          <span v-else-if="scope.row.extractionType == 1">评审（评审项目）</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="抽取专家人数" align="center" prop="expertNumber" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
<!--          <el-button-->
<!--            size="mini"-->
<!--            type="text"-->
<!--            icon="el-icon-edit"-->
<!--            @click="handleUpdate(scope.row)"-->
<!--            v-hasPermi="['expert:apply:edit']"-->
<!--          >修改</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleInfo(scope.row)"
            v-hasPermi="['expert:apply:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['expert:apply:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专家抽取申请对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="处理状态0待提交1已提交2已抽取" prop="applyStatus">
          <el-select v-model="form.applyStatus" placeholder="请选择处理状态0待提交1已提交2已抽取">
            <el-option
              v-for="dict in dict.type.expert_apply_status"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="0随机1自主" prop="applyMethod">
          <el-radio-group v-model="form.applyMethod">
            <el-radio
              v-for="dict in dict.type.busi_apply_method"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="抽取专家人数" prop="expertNumber">
          <el-input v-model="form.expertNumber" placeholder="请输入抽取专家人数" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listApply, getApply, delApply, addApply, updateApply } from "@/api/expert/apply";
import {listProject} from "@/api/tender/project";

export default {
  name: "Apply",
  dicts: ['expert_apply_status','busi_apply_method'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 专家抽取申请表格数据
      applyList: [],
      projectIdOptions: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        applyStatus: null,
        expertNumber: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询专家抽取申请列表 */
    getList() {
      this.loading = true;
      listProject().then((response) => {
        this.projectIdOptions = response.rows;
      });
      listApply(this.queryParams).then(response => {
        this.applyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        applyId: null,
        projectId: null,
        applyStatus: null,
        expertNumber: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.applyId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // this.open = true;
      // this.title = "添加专家抽取申请";
      this.$router.push('/expert/apply/add/0')
    },
    /** 查看按钮操作 */
    handleInfo(row) {
      this.reset();
      // this.open = true;
      // this.title = "添加专家抽取申请";
      const applyId = row.applyId || this.ids
      this.$router.push('/expert/result/view/' + row.applyId)
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const applyId = row.applyId || this.ids
      getApply(applyId).then(response => {
        // this.form = response.data;
        this.$router.push('/expert/apply/add/' + row.applyId)
        // this.open = true;
        // this.title = "修改专家抽取申请";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.applyId != null) {
            updateApply(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addApply(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const applyIds = row.applyId || this.ids;
      this.$modal.confirm('是否确认删除专家抽取申请编号为"' + applyIds + '"的数据项？').then(function() {
        return delApply(applyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('expert/apply/export', {
        ...this.queryParams
      }, `apply_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
