import request from '@/utils/request'

// 查询专家组长投票记录列表
export function listLeaderVoteRecord(query) {
  return request({
    url: '/expert/leaderVoteRecord/list',
    method: 'get',
    params: query
  })
}

// 查询专家组长投票记录详细
export function getLeaderVoteRecord(voteRecordId) {
  return request({
    url: '/expert/leaderVoteRecord/' + voteRecordId,
    method: 'get'
  })
}

// 新增专家组长投票记录
export function addLeaderVoteRecord(data) {
  return request({
    url: '/expert/leaderVoteRecord',
    method: 'post',
    data: data
  })
}

// 修改专家组长投票记录
export function updateLeaderVoteRecord(data) {
  return request({
    url: '/expert/leaderVoteRecord',
    method: 'put',
    data: data
  })
}

// 删除专家组长投票记录
export function delLeaderVoteRecord(voteRecordId) {
  return request({
    url: '/expert/leaderVoteRecord/' + voteRecordId,
    method: 'delete'
  })
}
