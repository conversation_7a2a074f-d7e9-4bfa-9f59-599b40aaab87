import request from '@/utils/request'

// 查询多级数据列表
export function listData(query) {
  return request({
    url: '/tree/data/list',
    method: 'get',
    params: query
  })
}

// 查询多级数据列表 （树形结构）
export function listWithTreeData(query) {
  return request({
    url: '/tree/data/listWithTree',
    method: 'get',
    params: query
  })
}

// 查询多级数据详细
export function getData(id) {
  return request({
    url: '/tree/data/' + id,
    method: 'get'
  })
}

// 新增多级数据
export function addData(data) {
  return request({
    url: '/tree/data',
    method: 'post',
    data: data
  })
}

// 修改多级数据
export function updateData(data) {
  return request({
    url: '/tree/data',
    method: 'put',
    data: data
  })
}

// 删除多级数据
export function delData(id) {
  return request({
    url: '/tree/data/' + id,
    method: 'delete'
  })
}
