<template>
  <div>

    <Head></Head>
    <div class="bidOpeningHall">
      <el-card class="box-card">
        <div v-if="list.length==0" class="body" style="align-content: center;justify-content: center">
          <el-empty description="今日无开标项目"></el-empty>
        </div>
        <div v-else class="body">
          <div v-for="(item,index) in list" :key="index" class="item" @click="gotobidOpeningRoom(item.projectId)" style="cursor: pointer;">
            <el-card class="box-card" style="width: 100%;" shadow="hover">
              <div slot="header" class="clearfix">
                <span>{{item.project.projectName}}</span>
              </div>
              <div>
                <div style="text-align:center;padding-top:18px;padding-bottom:18px;font-size:17px;font-weight: 600;">
                  <div style="display:flex;align-items: center;padding-bottom:10px;font-size:17px">
                    <div style="width:50%">开标时间：</div>
                    <div style="width:50%;color:#176ADB">{{ formatBidOpeningTime(item.bidOpeningTime) }}</div>
                  </div>
                  <div style="display:flex;justify-content: center;align-items: center;">
                    <div style="width:50%">开标状态：</div>
                    <div v-if="bidOpeningStatus(item.bidOpeningTime,item.bidOpeningEndTime) == '0'" style="width:50%;background: #176ADB;color:#FFFFFF;padding:5px 20px">{{role? "待签到":"未开标"}}</div>
                    <div v-if="bidOpeningStatus(item.bidOpeningTime,item.bidOpeningEndTime) == '1'" style="background: #176ADB;color:#FFFFFF;padding:5px 20px">开标中</div>
                    <div v-if="bidOpeningStatus(item.bidOpeningTime,item.bidOpeningEndTime) == '2'" style="background: #176ADB;color:#FFFFFF;padding:5px 20px">开标结束</div>
                  </div>
                </div>

                <div class="time">
                  <el-statistic class="word" format="倒计时: HH 时 mm 分 ss 秒" :value="new Date(item.bidOpeningTime)" time-indices title=""></el-statistic>
                </div>
              </div>
            </el-card>
          </div>
        </div>
      </el-card>
    </div>

    <Foot></Foot>
  </div>
</template>

<script>
import {
  formatDateOption,
  getTodayStartWithDate,
  getTodayEndWithDate,
} from "@/utils/index";
import { list } from "@/api/onlineBidOpening/info";
export default {
  data() {
    return {
      list: [],
      role: true,
    };
  },
  methods: {
    // 格式化开标时间显示
    formatBidOpeningTime(time) {
      return formatDateOption(time, "time");
    },
    // 判断当前项目的开标状态
    bidOpeningStatus(bidOpeningTime, bidOpeningEndTime) {
      const startTime = new Date(bidOpeningTime);
      const endTime = new Date(bidOpeningEndTime);

      // 获取当前时间
      const currentTime = new Date();

      // 判断当前时间是否在两个时间之间
      if (currentTime < startTime) {
        return "0";
      } else if (currentTime >= startTime && currentTime < endTime) {
        return "1";
      } else if (currentTime >= endTime) {
        return "2";
      }
    },
    // 携带项目Id跳转到开标室
    gotobidOpeningRoom(projectId) {
      if (this.role) {
        this.$router.push({
          path: "/suppliersRoom",
          query: {
            projectId: projectId,
          },
        });
      } else {
        this.$router.push({
          path: "/agentRoom",
          query: {
            projectId: projectId,
          },
        });
      }
    },
  },
  mounted() {
    if (
      this.$store.getters.roles.includes("agency") ||
      this.$store.getters.roles.includes("purchaser")
    ) {
      this.role = false;
    } else if (this.$store.getters.roles.includes("supplier")) {
      this.role = true;
    }
    list({
      bidOpeningTime: getTodayStartWithDate(),
      bidOpeningEndTime: getTodayEndWithDate(),
    }).then((response) => {
      if (response.code == 200) {
        this.list = response.data;
      } else {
        this.$modal.msgwarning(response.msg);
      }
    });
  },
};
</script>

<style scoped lang="scss">
.bidOpeningHall {
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.box-card {
  height: 100%;
  width: 70%;
  padding: 20px;
}
::v-deep .el-card__header {
  padding-top: 0;
}
.clearfix {
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: center;
  font-weight: 700;
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 用省略号表示溢出内容 */
  display: -webkit-box; /* 必须设置为弹性盒模型 */
  -webkit-box-orient: vertical; /* 设置弹性盒的方向为垂直 */
  -webkit-line-clamp: 2; /* 限制显示两行文本 */
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}
.clearfix:after {
  clear: both;
}
.body {
  display: flex;
  flex-wrap: wrap;
  min-height: 500px;
}
.item {
  width: 24%;
  margin-top: 15px;
  height: 265px;
}
.item:not(:nth-child(4n)) {
  margin-right: calc(4% / 3);
}

.time {
  margin-top: 20px;
  .word {
    ::v-deep .con .number {
      font-size: 22px;
      font-weight: 700;
      color: #176adb;
      line-height: 0;
      letter-spacing: 0;
      text-align: left;
      line-height: 17px;
      white-space: nowrap;
    }
  }
}
</style>