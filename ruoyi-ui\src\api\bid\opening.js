import request from "@/utils/request";

// 查询开标记录列表
export function listOpening(query) {
  return request({
    url: "/bid/opening/list",
    method: "get",
    params: query,
  });
}

// 查询开标记录详细
export function getOpening(bidOpeningId) {
  return request({
    url: "/bid/opening/" + bidOpeningId,
    method: "get",
  });
}

// 新增开标记录
export function addOpening(data) {
  return request({
    url: "/bid/opening",
    method: "post",
    data: data,
  });
}

// 修改开标记录
export function updateOpening(data) {
  return request({
    url: "/bid/opening",
    method: "put",
    data: data,
  });
}

// 删除开标记录
export function delOpening(bidOpeningId) {
  return request({
    url: "/bid/opening/" + bidOpeningId,
    method: "delete",
  });
}

// 查询能够新建开标记录的项目列表
export function openingProject(query) {
  return request({
    url: "/bid/opening/openingProject",
    method: "get",
    params: query,
  });
}

// 新增开标记录
export function saveWithRecord(data) {
  return request({
    url: "/bid/opening/saveWithRecord",
    method: "post",
    data: data,
  });
}

// 新增开标记录
export function getSystemTime() {
  return request({
    url: "/bid/opening/getSystemTime",
    method: "get"
  });
}

// 流标
export function failureOfBid(data) {
  return request({
    url: "/bidder/notice/abortiveTenderNotice",
    method: "post",
    data: data,
  });
}
