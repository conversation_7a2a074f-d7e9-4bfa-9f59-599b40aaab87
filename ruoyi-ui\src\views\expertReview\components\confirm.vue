<template>
  <div
    class="confirm"
    v-loading="loading"
  >
    <div class="titel">
      项目名称：{{ project.projectName }}
    </div>
    <div>
      <div style="display:flex;    justify-content: center;margin-bottom:10px">
        <el-form
          style="width:600px;"
          label-position="left"
          ref="form"
          :model="form"
          label-width="100px"
        >
          <el-form-item label="专家姓名：">
            <el-col :span="21">
              <div style="text-align: left;color:#176ADB;font-weight: 700;">{{ form.xm }}</div>
            </el-col>
          </el-form-item>
          <el-form-item label="证件号：">
            <el-col :span="21">
              <div style="text-align: left;color:#176ADB;font-weight: 700;">{{ form.zjhm }}</div>
            </el-col>
          </el-form-item>
          <el-form-item label="联系电话：">
            <el-col :span="21">
              <el-input v-model="form.sjhm"></el-input>
            </el-col>
          </el-form-item>
          <el-form-item
            label="银行卡号："
            v-if="!(form.yhzh == null)"
          >
            <el-col :span="21">
              <el-input v-model="form.yhzh"></el-input>
            </el-col>
          </el-form-item>
          <el-form-item
            label="开户行："
            v-if="!(form.khh == null)"
          >
            <el-col :span="21">
              <el-input v-model="form.khh"></el-input>
            </el-col>
          </el-form-item>
        </el-form>
      </div>
      <div class="tips">请确认联系电话、银行卡号、开户行信息是否正确。如有错误可进行修改，修改后会同步更新专家基本信息。</div>
      <div style="text-align:center"><el-button
          class="item-button"
          @click="onSubmit"
        >确认</el-button></div>
    </div>
  </div>
</template>

<script>
import {
  expertAccout,
  editExpertInfo,
  expertInfoById,
} from "@/api/expert/review";
import { updateNode } from "@/api/evaluation/expertNodeInfo";
export default {
  name: "comfirm",
  props: { project: {} },
  data() {
    return {
      form: {
        xm: "张三",
        sjhm: "12345678912",
        zjhm: "123456789123456789",
        yhzh: "123456789123456789",
        khh: "鹤壁市工商银行",
      },
      loading: true,
    };
  },
  methods: {
    init() {
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          // 通过身份证号过滤
          const item = response.data.find((item) => {
            return item.zjhm == this.$route.query.zjhm;
          });
          localStorage.setItem("expertInfo", JSON.stringify(item));
          setTimeout(() => {}, 1000);
          expertAccout(item.resultId).then((response) => {
            if (response.code == 200) {
              if (item.isOwner) {
                this.form.id = response.data.id;
                this.form.xm = response.data.xm;
                this.form.sjhm = response.data.phone;
                this.form.zjhm = response.data.zjhm;
                this.form.yhzh = null;
                this.form.khh = null;
              } else {
                this.form = response.data.zhuanJiaInfoVo;
              }
            } else {
              this.$message.warning(response.msg);
            }
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
      this.loading = false;
    },
    onSubmit() {
      let evalExpertEvaluationInfoId = localStorage.getItem(
        "evalExpertEvaluationInfoId"
      );
      console.info("evalExpertEvaluationInfoId", evalExpertEvaluationInfoId);
      // 需要判断非空吗
      const data = {
        id: this.form.id,
        khh: this.form.khh,
        sjhm: this.form.sjhm,
        xm: this.form.xm,
        yhzh: this.form.yhzh,
        zjhm: this.form.zjhm,
        evalExpertEvaluationInfoId: evalExpertEvaluationInfoId,
      };
      editExpertInfo(data).then((response) => {
        if (response.code == 200) {
          updateNode({
            evalExpertEvaluationInfoId: localStorage.getItem(
              "evalExpertEvaluationInfoId"
            ),
            evalNode: 2,
          }).then((result) => {
            if (result.code == 200) {
              this.$emit("send", "reviewDiscipline");
            }
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.confirm {
  padding: 20px;
}
.titel {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  letter-spacing: 0;
  text-align: center;
  padding: 20px 0;
}
.tips {
  color: #176adb;
  font-size: 14px;
  line-height: 1.5;
  text-align: center;

  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 16px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  font-size: 16px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.el-form-item {
  margin-bottom: 15px;
}
</style>