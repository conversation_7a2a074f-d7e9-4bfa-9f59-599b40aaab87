<!-- 专家复核表格 -->
<template>
  <div>
    <el-table
      :data="tableData"
      style="width: 100%"
      :cell-style="cellStyle"
      :header-cell-style="tableHeaderClass"
    >
      <el-table-column
        header-align="center"
        :label="label"
      >
        <el-table-column
          prop="factor"
          width="300"
        >
        </el-table-column>
        <el-table-column
          v-for="(item, index) in column"
          :key="index"
          :prop="item"
          :label="item"
        >
          <template slot-scope="scope">
            <div style="display: flex;justify-content: center;align-items: center;color:#176ADB">
              <div v-if="label == '资格性评审' || label == '符合性评审'">
                <span v-if="scope.row[item].evaluationResult == 0 || scope.row[item].evaluationResult == null || scope.row[item].evaluationResult==undefined">未通过</span>
                <span v-else>通过</span>
              </div>
              <div v-else>
                <span>{{ scope.row[item].evaluationResult }}</span>
              </div>
              <div
                style="cursor: pointer;margin-left:10px"
                @click="edit(item,scope.row)"
              >
                <svg-icon
                  icon-class="edit"
                  class-name="edit"
                />
              </div>

            </div>
          </template>
        </el-table-column>
      </el-table-column>
    </el-table>
    <el-dialog
      title="评审内容修改"
      :visible.sync="dialogFormVisible"
    >
      <div class="content">
        <div class="item">
          <div class="lable">供应商：</div> {{ form.supplier }}
        </div>
        <div class="item">
          <div class="lable">评审因素：</div> {{ form.factors }}
        </div>
        <div
          class="item"
          v-if="label == '资格性评审' || label == '符合性评审'"
        >
          <div class="lable">修改值：</div>
          <el-switch
            v-model="form.modified"
            active-value="1"
            inactive-value="0"
          >
          </el-switch>
        </div>
        <div
          class="item"
          v-else
        >
          <div class="lable">修改值：</div>
          <el-select
            v-model="form.modified"
            placeholder="请选择"
          >
            <el-option
              v-for="(score , index) in form.scoreLevel.split(',')"
              :key="index"
              :label="score"
              :value="score"
            >
            </el-option>
          </el-select>
        </div>
        <div class="item">
          <div class="lable">修改原因：</div>
          <div style="width:80%">
            <el-input
              type="textarea"
              :rows="2"
              placeholder="请输入内容"
              v-model="form.reason"
            >
            </el-input>
          </div>

        </div>
      </div>

      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="confirm"
        >确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { updateDetail } from "@/api/evaluation/detail";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      form: {
        supplier: "",
        factors: "",
        modified: "",
        reason: "",
        scoreLevel: "",
      },
      dialogFormVisible: false,
      headStyleOne: {
        "font-family": "SourceHanSansSC-Bold",
        "font-weight": "700",
        "font-size": "18px",
        background: "#fff",
        color: "#333333",
      },
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  props: {
    label: {
      type: String,
      default: "",
    },
    tableData: {
      type: Array,
      default: [],
    },
    column: {
      type: Array,
      default: [],
    },
  },

  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 编辑
    edit(item, row) {
      this.form.supplier = item;
      this.form.factors = row["factor"];
      this.form.modified = row[item].evaluationResult;
      this.form.reason = "";
      this.form.expertEvaluationId = row[item].expertEvaluationId;
      this.form.scoreLevel = row[item].scoreLevel;
      this.dialogFormVisible = true;
    },
    // 确认
    confirm() {
      const data = {
        expertEvaluationId: this.form.expertEvaluationId,
        evaluationResult: this.form.modified,
        evaluationRemark: this.form.reason,
      };
      updateDetail(data).then((response) => {
        if (response.code == 200) {
          this.dialogFormVisible = false;
          this.$message.success("修改成功");
          this.$emit("update", "更新");
        }
      });
    },

    tableHeaderClass({ row, column, rowIndex, columnIndex }) {
      return rowIndex == 0 && columnIndex == 0
        ? this.headStyleOne
        : this.headStyle;
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style>
.custom-header {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 18px;
  background: #fff;
  color: red;
}
.content {
  width: 80%;
}
.item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.lable {
  width: 120px;
}
</style>