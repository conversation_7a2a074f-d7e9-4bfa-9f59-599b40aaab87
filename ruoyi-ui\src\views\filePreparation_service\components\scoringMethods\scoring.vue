<template>
  <div class="container">
    <div class="transfer">
      <div style="display: flex; justify-content: space-between; align-items: center;">
        <span>投标报价打分分值范围为 <strong>10</strong> - <strong>30</strong>分</span>
        <el-button class="item-button" style="background-color:#fff;height: 40px;color:#333;" @click="saveUinfo()">保存</el-button>
      </div>
      <div class="text">
        满足磋商文件要求且最后报价最低的供应商的价格为评标基准价， 其价格分为满分 【 <el-input placeholder="请输入分值" v-model="value" style="width:100px"></el-input>
        】分。其他有效供应商的报价分按照下列公式计算：投标报价得分＝（评标基准价/最后投标报价）×【 <el-input placeholder="请输入分值" v-model="value" style="width:100px"></el-input> 】
      </div>
      <div class="text">
        价格扣除：1.根据《政府采购促进中小企业发展管理办法》（财库[2020]46号）、《关于进一步加大政府采购支持中小企业力度的通知》（财库〔2022〕19号）、《关于政府采购支持监狱企业发展有关问题的通知》（财库〔2014〕68 号）、《财政部 民政部 中国残疾人联合会关于促进残疾人就业政府采购政策的通知》（财库〔2017〕141号）的要求：对小型、微型企业及监狱企业、残疾人福利性单位服务的价格给予20%的扣除，用扣除后的价格参与价格评审，参与评审价格=有效投标报价×（1-20%）。供应商应当出具所提供服务企业的《中小企业声明函》（供应商所投产品既有中小企业，又有大型企业生产的，不享受该优惠），否则不予价格扣除计算。。
      </div>
      <div class="text">
        2.未尽事宜详见第二章“投标人须知前附表”第1.12项规定;
      </div>
      <div class="text">
        3.如为专门面向中小企业采购的项目应删除上述内容。
      </div>
    </div>
  </div>

</template>

<script>
import { saveInfo as saveUinfo } from "@/api/method/uinfo";
export default {
  data() {
    return {
      value: "",
      projectInfo: {},
      itemInfos: [
        {
          score: 30,
        },
      ],
      uInfo: {},
      tempItem: {},
      tips: ""
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfos, uinfo, tempItem, activeParams, extData) {
      this.projectInfo = projectInfo;
      if (itemInfos && itemInfos.length > 0) {
        this.value = itemInfos[0].score;
        this.itemInfos = itemInfos;
      }
      if (uinfo && uinfo.entMethodId) {
        this.uInfo = uinfo;
      }
      this.tempItem = tempItem;
    },
    saveUinfo() {
      if (!this.value) {
        this.$message.warning("请输入分值");
        return;
      }
      this.itemInfos = this.itemInfos.map((item) => {
        return {
          entMethodId: this.uInfo.entMethodId,
          entMethodItemId: item.entMethodItemId,
          scoringMethodId: this.tempItem.scoringMethodId,
          scoringMethodItemId: this.tempItem.scoringMethodItemId,
          itemRemark: this.tempItem.itemRemark,
          itemName: this.tempItem.itemName,
          score: this.value,
        };
      });
      const postData = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodId: this.tempItem.scoringMethodId,
        projectId: this.projectInfo.projectId,
        scoringMethodUitems: this.itemInfos,
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}
.transfer {
  width: 80%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
  .text {
    color: rgba(80, 80, 80, 1);
    font-size: 16px;
    line-height: 150%;
    text-align: left;
    margin-bottom: 20px;
  }
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
