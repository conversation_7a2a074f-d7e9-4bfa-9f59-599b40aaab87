import request from "@/utils/request";

// 查询项目评审信息列表
export function listInfo(query) {
  return request({
    url: "/evaluation/info/list",
    method: "get",
    params: query,
  });
}

// 查询项目评审信息详细
export function getInfo(projectEvaluationId) {
  return request({
    url: "/evaluation/info/" + projectEvaluationId,
    method: "get",
  });
}

// 新增项目评审信息
export function addInfo(data) {
  return request({
    url: "/evaluation/info",
    method: "post",
    data: data,
  });
}

// 修改项目评审信息
export function updateInfo(data) {
  return request({
    url: "/evaluation/info",
    method: "put",
    data: data,
  });
}

// 删除项目评审信息
export function delInfo(projectEvaluationId) {
  return request({
    url: "/evaluation/info/" + projectEvaluationId,
    method: "delete",
  });
}

// 询标消息记录
export function getHistoryMessage(data) {
  return request({
    url: "/bid/info/getHistoryMessage",
    method: "post",
    data: data,
  });
}
// 导出评审结果
export function exportReport(query) {
  return request({
    url: "/evaluation/info/exportEvalProjectEvaluationInfo",
    method: "post",
    query: query,
  });
}
// 导出评审报告
export function exportResult(data) {
  return request({
    url: "/evaluation/info/saveReviewReport",
    method: "post",
    query: query,
  });
}
