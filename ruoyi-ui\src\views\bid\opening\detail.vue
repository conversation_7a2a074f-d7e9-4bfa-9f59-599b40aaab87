<template>
  <div style="margin-top:20px;">

    <el-form ref="elForm" :model="formData" :rules="rules" >
    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    采购项目
                  </div></td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell" >
                      {{formData.projectName}}
                    </div></td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell cell-right-border">
                    项目编号
                  </div></td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{formData.projectCode}}
                  </div></td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell cell-right-border">开标时间</div></td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{formData.bidOpeningTime}}</div></td>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell cell-right-border">
                    开标结束时间
                  </div></td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                   {{ formData.bidOpeningEndTime }}
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-form>



    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>投标人信息</span>
          </div>
          <div >
      <el-table v-loading="loading" :data="tradderList" style="width: 100%;margin: auto;">
        <el-table-column label="投标人"  min-width="6" align="center" prop="bidderName" />
        <el-table-column label="投标报价"  min-width="2" align="center" prop="bidAmount" />
        <el-table-column label="报价单位"  min-width="1" align="center" prop="priceUnit" />
      </el-table>
          </div>
        </el-card>
      </el-col>



      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>文件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr v-for="(item, key) in formData.attachmentMap" :key="key">
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{key}}</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <template>
                      <FileUpload
                          :value="item"
                          :fileType="['pdf', 'doc', 'docx']"
                          :isShowTip="false"
                          :showOnly=true
                    ></FileUpload>
                    </template>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
  </div>
</template>
<script>
import { getOpening, delOpening, addOpening, updateOpening, openingProject,saveWithRecord } from "@/api/bid/opening";
import { getListRecord } from "@/api/bidding/record";
import { getDicts } from "@/api/system/dict/data";
export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      formData: {
        bidOpeningId: '1110165275476997',
        projectId: '1110165275476997',
        projectCode: 'code',
        projectName: 'name',
        bidOpeningEndTime: '2024-06-01 11:11:11',
        attachmentList: [],
        attachmentMap:{'111':'xxx.pdf','222':'yyy.pdf'}
      },
      attachmentTypes: [],
      attachmentMap: {},
      rules: {
        projectId: [{
          required: true,
          message: '请选择采购项目',
          trigger: 'change'
        }],
        bidOpeningEndTime: [{
          required: true,
          message: '请选择开标结束时间',
          trigger: 'change'
        }],
      },
      field102Action: '/common/upload',
      field102fileList: [],
      projectIdOptions: [],
      projectMap:{},
      // 投标人表格数据
      tradderList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    var xxx = this.$route.params.bidOpeningId;
    this.getOpening(xxx);
    // this.getAttachmentTypes();
  },
  mounted() {},
  methods: {
    getOpening(id){
      getOpening(id).then( (result) => {
        if(result.code==200){
          this.formData = result.data;
          this.getListRecord();
        }
      });
    },
    getListRecord(){
      this.loading = true;
      getListRecord({projectId:this.formData.projectId, params:{isScope:1}}).then( (result) => {
        if(result.code==200){
          this.tradderList = result.data;
        }
        this.loading = false;
      });
    },
    changeProject(r){
      let p = this.projectMap[r];
      this.formData.projectCode = p.projectCode;
      this.formData.projectName = p.projectName;
      console.info(this.formData);
      this.loading = true;
      getListRecord({projectId:this.formData.projectId}).then( (result) => {
        if(result.code==200){
          this.tradderList = result.data;
        }
        this.loading = false;
      });
    }
  }
}

</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}
.cell-right-border {
  border-right:1px solid #dfe6ec
}
 .el-form-item {
    margin-bottom: 0px;
  }
</style>

<style scoped>
/deep/ .el-upload {
 float: right;
}
/deep/ .el-upload-list {
 width: 90%;
}
</style>
