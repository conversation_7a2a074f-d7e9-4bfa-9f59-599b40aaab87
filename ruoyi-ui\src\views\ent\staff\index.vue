<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="企业id" prop="entId">
        <el-input
          v-model="queryParams.entId"
          placeholder="请输入企业id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="员工名称" prop="staffName">
        <el-input
          v-model="queryParams.staffName"
          placeholder="请输入员工名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="员工编号" prop="staffCode">
        <el-input
          v-model="queryParams.staffCode"
          placeholder="请输入员工编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="性别" prop="staffSex">
        <el-select v-model="queryParams.staffSex" placeholder="请选择性别" clearable>
          <el-option
            v-for="dict in dict.type.sys_user_sex"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="电话" prop="staffPhone">
        <el-input
          v-model="queryParams.staffPhone"
          placeholder="请输入电话"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="学历" prop="staffDegree">
        <el-select v-model="queryParams.staffDegree" placeholder="请选择学历" clearable>
          <el-option
            v-for="dict in dict.type.busi_expert_degree"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ent:staff:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ent:staff:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ent:staff:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ent:staff:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="staffList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="公告id" align="center" prop="staffId" />
      <el-table-column label="企业id" align="center" prop="entId" />
      <el-table-column label="员工名称" align="center" prop="staffName" />
      <el-table-column label="员工编号" align="center" prop="staffCode" />
      <el-table-column label="性别" align="center" prop="staffSex">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.staffSex"/>
        </template>
      </el-table-column>
      <el-table-column label="电话" align="center" prop="staffPhone" />
      <el-table-column label="学历" align="center" prop="staffDegree">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_expert_degree" :value="scope.row.staffDegree"/>
        </template>
      </el-table-column>
      <el-table-column label="职称" align="center" prop="staffTitles" />
      <el-table-column label="专业" align="center" prop="staffSpeciality" />
      <el-table-column label="身份证号" align="center" prop="staffCertificate" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ent:staff:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ent:staff:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业员工信息对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="企业id" prop="entId">
          <el-input v-model="form.entId" placeholder="请输入企业id" />
        </el-form-item>
        <el-form-item label="员工名称" prop="staffName">
          <el-input v-model="form.staffName" placeholder="请输入员工名称" />
        </el-form-item>
        <el-form-item label="员工编号" prop="staffCode">
          <el-input v-model="form.staffCode" placeholder="请输入员工编号" />
        </el-form-item>
        <el-form-item label="性别" prop="staffSex">
          <el-select v-model="form.staffSex" placeholder="请选择性别">
            <el-option
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电话" prop="staffPhone">
          <el-input v-model="form.staffPhone" placeholder="请输入电话" />
        </el-form-item>
        <el-form-item label="学历" prop="staffDegree">
          <el-select v-model="form.staffDegree" placeholder="请选择学历">
            <el-option
              v-for="dict in dict.type.busi_expert_degree"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职称" prop="staffTitles">
          <el-input v-model="form.staffTitles" placeholder="请输入职称" />
        </el-form-item>
        <el-form-item label="专业" prop="staffSpeciality">
          <el-input v-model="form.staffSpeciality" placeholder="请输入专业" />
        </el-form-item>
        <el-form-item label="身份证号" prop="staffCertificate">
          <el-input v-model="form.staffCertificate" placeholder="请输入身份证号" />
        </el-form-item>
        <el-form-item label="删除标记" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStaff, getStaff, delStaff, addStaff, updateStaff } from "@/api/ent/staff";

export default {
  name: "Staff",
  dicts: ['sys_user_sex', 'base_yes_no', 'busi_expert_degree'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业员工信息表格数据
      staffList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entId: null,
        staffName: null,
        staffCode: null,
        staffSex: null,
        staffPhone: null,
        staffDegree: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entId: [
          { required: true, message: "企业id不能为空", trigger: "blur" }
        ],
        staffName: [
          { required: true, message: "员工名称不能为空", trigger: "blur" }
        ],
        staffPhone: [
          { required: true, message: "电话不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业员工信息列表 */
    getList() {
      this.loading = true;
      listStaff(this.queryParams).then(response => {
        this.staffList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        staffId: null,
        entId: null,
        staffName: null,
        staffCode: null,
        staffSex: null,
        staffPhone: null,
        staffDegree: null,
        staffTitles: null,
        staffSpeciality: null,
        staffCertificate: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.staffId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业员工信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const staffId = row.staffId || this.ids
      getStaff(staffId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业员工信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.staffId != null) {
            updateStaff(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addStaff(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const staffIds = row.staffId || this.ids;
      this.$modal.confirm('是否确认删除企业员工信息编号为"' + staffIds + '"的数据项？').then(function() {
        return delStaff(staffIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ent/staff/export', {
        ...this.queryParams
      }, `staff_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
