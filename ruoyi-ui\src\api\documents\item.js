import request from '@/utils/request'

// 查询采购文件编制详细信息列表
export function listItem(query) {
  return request({
    url: '/documents/item/list',
    method: 'get',
    params: query
  })
}

// 查询采购文件编制详细信息详细
export function getItem(projectFileItemId) {
  return request({
    url: '/documents/item/' + projectFileItemId,
    method: 'get'
  })
}

// 新增采购文件编制详细信息
export function addItem(data) {
  return request({
    url: '/documents/item',
    method: 'post',
    data: data
  })
}

// 修改采购文件编制详细信息
export function updateItem(data) {
  return request({
    url: '/documents/item',
    method: 'put',
    data: data
  })
}

// 删除采购文件编制详细信息
export function delItem(projectFileItemId) {
  return request({
    url: '/documents/item/' + projectFileItemId,
    method: 'delete'
  })
}
