<template>
  <div class="discipline">
    <div class="titel">
      推选专家组长
    </div>
    <div class="tips">
      <div
        v-for="(item,index) in leaderList"
        :class="{ 'selected': item.id == leader.id }"
        :key="index"
        class="item"
        style="cursor: pointer;"
        @click="selectItem(item)"
      >
        <el-card
          class="box-card"
          style="width: 100%;"
          shadow="hover"
          v-if="!(item.isOwner == 1)"
        >
          <div class="info">
            <div>{{ item.xm }}</div>
            <div>担任组长次数：{{ item.expertLeaderCount }}</div>
            <div v-if="item.zhuanJiaInfoVo">{{ item.zhuanJiaInfoVo.zc }}</div>
          </div>
        </el-card>
      </div>
    </div>
    <div style="text-align:center">
      <div v-if="save">
        <span>{{ msg }}</span>
      </div>
      <div v-else>
        <el-button
          class="item-button"
          style="background-color:#F5F5F5;color:#176ADB"
          @click="back"
        >返回</el-button>
        <el-button
          class="item-button"
          @click="confirm"
        >确认</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { expertInfoById } from "@/api/expert/review";
import { keepRecord, getExpertLeader } from "@/api/expert/result";
import { updateNode } from "@/api/evaluation/expertNodeInfo";
import { formatDate } from "@/utils/index";
export default {
  name: "comfirm",
  data() {
    return {
      leaderList: [
        {
          id: 1,
          xm: "张三",
          expertLeaderCount: "1",
          zc: "职称",
        },
      ],
      leader: {},
      msg: "",
      save: false,
      intervalId: null,
    };
  },
  methods: {
    init() {
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.leaderList = response.data;
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    selectItem(leader) {
      this.leader = leader;
    },
    confirm() {
      if (Object.keys(this.leader).length === 0) {
        this.$message.warning("请推选专家组长");
      } else {
        const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
        keepRecord({
          applyId: expertInfo.applyId,
          projectId: parseInt(this.$route.query.projectId),
          voterExpertId: expertInfo.resultId,
          candidateExpertId: this.leader.resultId,
          voteTime: formatDate(new Date()),
        }).then((response) => {
          if (response.code == 200) {
            this.save = true;
            this.getLeader();
            this.intervalId = setInterval(() => {
              this.getLeader();
            }, 5000);
          } else {
            this.$message.warning(response.msg);
          }
        });
      }
    },
    getLeader() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      const params = {
        applyId: expertInfo.applyId,
      };
      getExpertLeader(params).then((res) => {
        if (res.code == 200) {
          if (res.data != undefined) {
            updateNode({
              evalExpertEvaluationInfoId: localStorage.getItem(
                "evalExpertEvaluationInfoId"
              ),
              evalNode: 5,
            }).then((result) => {
              if (result.code == 200) {
                clearInterval(this.intervalId);
                this.$emit("send", "leaderConfirm");
              }
            });
          } else {
            this.msg = res.msg;
          }
        }
      });
    },

    back() {
      updateNode({
        evalExpertEvaluationInfoId: localStorage.getItem(
          "evalExpertEvaluationInfoId"
        ),
        evalNode: 3,
      }).then((result) => {
        if (result.code == 200) {
          this.$emit("send", "avoid");
        }
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.discipline {
  padding: 20px 30px;
}
.titel {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  padding: 20px 0;
}
::v-deep .el-card {
  border: 1px solid #176adb;
}
.tips {
  // border: 1px dashed;
  display: grid;
  // 下面是重点
  grid-gap: 2%; // 卡片左右间隙
  grid-template-columns: repeat(auto-fill, 18%); // 自动填充一行的卡片个数
  justify-content: center; // 每行的卡片整体居中，但最后一行如果没有填充满会左对齐
  margin-bottom: 20px;
  .box-card {
    .info {
      display: grid;
      justify-items: center;
      align-content: space-between;
      height: 200px;
      div {
        min-height: 30px;
      }
    }
  }
}
::v-deep .box-card {
  border-radius: 4px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.selected {
  // border: 2px solid #176adb; /* 你可以根据需要修改颜色 */
  // background-color: #176adb; /* 可选的背景颜色 */
  ::v-deep .el-card {
    background-color: #176adb;
    color: #ffffff;
  }
}
</style>