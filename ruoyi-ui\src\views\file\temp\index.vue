<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="采购方式" prop="tenderMode">
        <el-select v-model="queryParams.tenderMode" placeholder="请选择采购方式" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件网络地址" prop="fileUrl">
        <el-input
          v-model="queryParams.fileUrl"
          placeholder="请输入文件网络地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目类型" prop="projectType">
        <el-select v-model="queryParams.projectType" placeholder="请选择项目类型" clearable>
          <el-option
            v-for="dict in dict.type.busi_project_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="0采购文件1响应文件" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择0采购文件1响应文件" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="文件物理路径" prop="dirPath">
        <el-input
          v-model="queryParams.dirPath"
          placeholder="请输入文件物理路径"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['file:temp:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['file:temp:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['file:temp:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['file:temp:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="tempList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="文件模板id" align="center" prop="documentsFileTempId" />
      <el-table-column label="采购方式" align="center" prop="tenderMode">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_tender_mode" :value="scope.row.tenderMode"/>
        </template>
      </el-table-column>
      <el-table-column label="文件网络地址" align="center" prop="fileUrl" />
      <el-table-column label="项目类型" align="center" prop="projectType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_project_type" :value="scope.row.projectType"/>
        </template>
      </el-table-column>
      <el-table-column label="0采购文件1响应文件" align="center" prop="type">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_tender_type" :value="scope.row.type"/>
        </template>
      </el-table-column>
      <el-table-column label="文件物理路径" align="center" prop="dirPath" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['file:temp:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['file:temp:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购/响应文件模板对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="采购方式" prop="tenderMode">
          <el-select v-model="form.tenderMode" placeholder="请选择采购方式">
            <el-option
              v-for="dict in dict.type.busi_tender_mode"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件网络地址" prop="fileUrl">
          <el-input v-model="form.fileUrl" placeholder="请输入文件网络地址" />
        </el-form-item>
        <el-form-item label="项目类型" prop="projectType">
          <el-select v-model="form.projectType" placeholder="请选择项目类型">
            <el-option
              v-for="dict in dict.type.busi_project_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="0采购文件1响应文件" prop="type">
          <el-select v-model="form.type" placeholder="请选择0采购文件1响应文件">
            <el-option
              v-for="dict in dict.type.busi_tender_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="文件物理路径" prop="dirPath">
          <el-input v-model="form.dirPath" placeholder="请输入文件物理路径" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTemp, getTemp, delTemp, addTemp, updateTemp } from "@/api/file/temp";

export default {
  name: "Temp",
  dicts: ['busi_project_type', 'busi_tender_mode', 'busi_tender_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购/响应文件模板表格数据
      tempList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        tenderMode: null,
        fileUrl: null,
        projectType: null,
        type: null,
        dirPath: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购/响应文件模板列表 */
    getList() {
      this.loading = true;
      listTemp(this.queryParams).then(response => {
        this.tempList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        documentsFileTempId: null,
        tenderMode: null,
        fileUrl: null,
        projectType: null,
        type: null,
        dirPath: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.documentsFileTempId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购/响应文件模板";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const documentsFileTempId = row.documentsFileTempId || this.ids
      getTemp(documentsFileTempId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改采购/响应文件模板";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.documentsFileTempId != null) {
            updateTemp(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addTemp(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const documentsFileTempIds = row.documentsFileTempId || this.ids;
      this.$modal.confirm('是否确认删除采购/响应文件模板编号为"' + documentsFileTempIds + '"的数据项？').then(function() {
        return delTemp(documentsFileTempIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('file/temp/export', {
        ...this.queryParams
      }, `temp_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
