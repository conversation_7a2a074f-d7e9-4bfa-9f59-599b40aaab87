<template>
  <div class="tender" v-loading="loading">

    <el-col :span="24" class="card-box" style="margin-top: 20px;">
      <el-card>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell" style="text-align: center; font-size:24px;font-weight: bolder;">{{ formData.tenderNotice.noticeName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">项目名称：</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.projectName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">项目编号：</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.projectCode }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">报价单位</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag :options="dict.type.price_unit" :value="formData.tenderNotice.priceUnit"></dict-tag>
                  </div>
                </td>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">采购方式</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag :options="dict.type.busi_tender_mode" :value="formData.tenderProject.tenderMode"></dict-tag>
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">报名开始时间</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.noticeStartTime }}</div>
                </td>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">报名截止时间</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.noticeEndTime }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">开标室</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ bidOpeningRoom.venueName }}</div>
                </td>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">开标时间</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ bidOpeningRoom.occupyStartTime }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>

    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i> 公告内容</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="6" class="el-table__cell is-leaf">
                  <div class="cell" v-html="formData.tenderNotice.noticeContent"></div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <!-- <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-monitor"></i> 投标人资格</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="6" class="el-table__cell is-leaf">

                  <div class="cell" v-html="formData.tenderProject.bidderQualification">
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col> -->
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-monitor"></i> 联系方式</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr v-if="formData.tenderProject.agencyFlag==1">
                <td colspan="1" class="el-table__cell is-leaf">
                  <div class="cell">代理机构</div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyName }}</div>
                </td>
              </tr>
              <tr v-if="formData.tenderProject.agencyFlag==1">
                <td colspan="1" class="el-table__cell is-leaf">
                  <div class="cell">联系人</div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyContactPerson }}</div>
                </td>
                <td colspan="1" class="el-table__cell is-leaf">
                  <div class="cell">联系方式</div>
                </td>
                <td colspan="5" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyPhone }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">采购单位</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">联系人</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererContactPerson }}</div>
                </td>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell">联系方式</div>
                </td>
                <td colspan="8" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererPhone }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="cell" style="color: red;font-size: 13px;">
          如有异议请电话咨询采购人或采购代理，采购流程问题请咨询平台运营。
        </div>
      </el-card>
    </el-col>
    <!-- <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i> 附件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr
                v-for="dict in dict.type.busi_tender_notice_attachment"
                :key="dict.value"
              >
                <td colspan="1" class="el-table__cell is-leaf">
                  <div class="cell">{{ dict.label }}</div>
                </td>
                <td colspan="5" class="el-table__cell is-leaf">
                  <div class="cell">
                    <FileUpload
                      @input="handleInput(dict.label, $event)"
                      @download="handleDownload($event)"
                      :fileType="['pdf', 'doc', 'docx']"
                      :isShowTip="false"
                      :value="attachments[dict.value]"
                      :showOnly="true"
                    ></FileUpload>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col> -->
    <div slot="footer" class="option">
      <el-button v-if="signUp" type="success" @click="downloadTenderFiles">我要投标</el-button>
    </div>
  </div>
</template>
<script>
import {
  getNotice,
  addNotice,
  updateNotice,
  updateAnnouncementInfo,
} from "@/api/tender/notice";
import { listProject, getProject } from "@/api/tender/project";
import { getTenderNoticeForMain, downloadTenderFile } from "@/api/tender/notice";
import { download } from "@/utils/request";
import { getDicts } from "@/api/system/dict/data";
import { listInfo } from "@/api/venue/info";
import {
  listOccupy,
  getOccupy,
  addOccupy,
  updateOccupy,
} from "@/api/venue/occupy";

export default {
  components: {},
  dicts: [
    "price_unit",
    "busi_tender_allow_coalition",
    "busi_tender_to_sme",
    "busi_tender_bid_opening_mode",
    "busi_tender_bid_evaluation_mode",
    "busi_bid_evaluation_period",
    "busi_tender_notice_attachment",
    "busi_tender_mode",
  ],
  props: [],
  data() {
    return {
      attachmentsMap: {},
      spaceOccupancyList: [],
      attachments: [],
      // 遮罩层
      loading: true,
      timePeriod: [],
      openingRoom: false,
      activeOpenRoom: "",
      evaluationRoom: false,
      activeEvaluationRoom: "",
      intervalTime: "",
      formData: {
        tenderNotice: {},
        tenderProject: {}
      },
      project: {},
      // 开标室保存信息
      bidOpeningRoom: {
        noticeId: "",
        venueId: "",
        venueName: "",
        venueType: "",
        occupyStartTime: "",
        occupyEndTime: "",
        bidEvaluationPeriod: "",
      },
      // 评标室保存信息
      bidEvaluationRoom: {
        noticeId: "",
        venueId: "",
        venueName: "",
        venueType: "",
        occupyStartTime: "",
        occupyEndTime: "",
        bidEvaluationPeriod: "",
      },
      projectIdOptions: [],
      bidOpeningRoomOptions: [],
      bidEvaluationRoomOptions: [],
      signUp: false,
      change: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getConfigKey("sys.publicity.time").then((response) => {
      this.intervalTime = response.msg;
    });
  },
  mounted() {
    var roles = this.$store.getters.roles;
    for (var index in roles) {
      console.info(roles[index]);
      if (roles[index] == 'supplier') {
        this.signUp = true;
      }
      if (roles[index] == 'purchaser' || roles[index] == 'agency') {
        this.change = true;
      }
    }
    var noticeId = this.$route.params.noticeId;
    this.getTenderNoticeForMain(noticeId);
  },
  methods: {
    getTenderNoticeForMain(noticeId) {
      getTenderNoticeForMain(noticeId).then((result) => {
        if (result.code == 200) {
          this.formData = result.data;
          this.loading = false;
        }
      });
    },
    downLoadFile(id, value) {
      let list = value.split("/");
      let fileName = list[list.length - 1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId: id,
        fileName: fileName,
        filePath: value,
        resource: value,
      };

      download("/common/download/resource", params, fileName);

      // downLoadFile(params).then(response => {
      //   if (response["code"]==200){
      //   }
      // });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
      /*
        /!** request里面的download下载函数 *!/
        //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
        console.log("文件名");*/
    },

    handleInput(fileType, data) {
      // 首先清空attachments.fileType = fileType 的数据
      this.formData.attachments = this.formData.attachments.filter(
        (attachment) => attachment.fileType !== fileType
      );
      const params = {};
      if (data) {
        const array = data.split(",");
        // 然后再新增
        array.map((item) => {
          // 设置 附件路径
          params.filePath = data;

          // 设置 附件名称
          const lastIndex = item.lastIndexOf("/");
          params.fileName = item.substring(lastIndex + 1);

          // 设置 附件类型
          params.fileType = fileType;

          // 设置 附件后缀
          const dotIndex = params.fileName.lastIndexOf(".");
          if (dotIndex !== -1) {
            params.fileSuffix = params.fileName.substring(dotIndex + 1);
          }
          this.formData.attachments.push(params);
        });
      }

      return params;
    },
    handleDownload(data) {
      let list = data.url.split("/");
      let fileName = list[list.length - 1];
      let params = {
        projectId: this.formData.projectId,
        fileName: fileName,
        filePath: data.url,
        resource: data.url,
      };
      downLoadFile(params).then(response => {
        if (response["code"] == 200) {
          download("/common/download/resource", params, fileName);

        }
      });
    },
    cancel() {
      this.$tab.closePage();
    },

    resetForm() {
      this.$refs["busiTenderNotice"].resetFields();
    },
    selectOpeningRoom(item) {
      const selectedItem = this.bidOpeningRoomOptions.find(
        (item) => item.venueId === parseInt(this.activeOpenRoom)
      );
      this.bidOpeningRoom.venueId = selectedItem.venueId;
      this.bidOpeningRoom.venueName = selectedItem.venueName;
      this.bidOpeningRoom.venueType = selectedItem.venueType;
      this.formData.bidOpeningTime = item.time;
      if (this.$route.params.noticeId != 0) {
        this.bidOpeningRoom.noticeId = parseInt(this.$route.params.noticeId);
      }

      this.openingRoom = false;
    },
    selectEvaluationRoom(item, period) {
      const selectedItem = this.bidEvaluationRoomOptions.find(
        (item) => item.venueId === parseInt(this.activeEvaluationRoom)
      );
      this.bidEvaluationRoom.venueId = selectedItem.venueId;
      this.bidEvaluationRoom.venueName = selectedItem.venueName;
      this.bidEvaluationRoom.venueType = selectedItem.venueType;
      this.formData.bidEvaluationTime = item.time;
      if (this.$route.params.noticeId != 0) {
        this.bidEvaluationRoom.noticeId = parseInt(this.$route.params.noticeId);
      }
      switch (period) {
        case "上午":
          this.bidEvaluationRoom.bidEvaluationPeriod = "0";
          break;
        case "下午":
          this.bidEvaluationRoom.bidEvaluationPeriod = "1";
          break;
        case "全天":
          this.bidEvaluationRoom.bidEvaluationPeriod = "2";
          break;
      }
      this.evaluationRoom = false;
    },
    // 根据场地占用id，初始化timePeriod
    initTimePeriod(occupyId) {
      // 根据场地id来过滤一下数据，只获得当前房间的数据
      const list = this.spaceOccupancyList.filter(
        (item) => item.venueId === parseInt(occupyId)
      );

      // 假设this.list已经被定义并且包含了一些数据
      // 假设getNext15Days函数已经定义并且返回了this.timePeriod

      // 遍历list中的每个对象
      list.forEach((listItem) => {
        // 获取occupyStartTime的值
        const startTime = listItem.occupyStartTime;
        // 转换成日期对象
        const startDate = new Date(startTime);
        // 格式化日期为"yyyy-MM-dd"
        const formattedStartDate = `${startDate.getFullYear()}年${(
          startDate.getMonth() + 1
        )
          .toString()
          .padStart(2, "0")}-${startDate
            .getDate()
            .toString()
            .padStart(2, "0")}`;

        // 遍历this.timePeriod数组，找到匹配的日期对象
        this.timePeriod.forEach((timePeriodItem, index) => {
          // 匹配日期
          if (timePeriodItem.time === formattedStartDate) {
            // 更新openPeriod和evaluation
            timePeriodItem.openPeriod.push(
              listItem.occupyStartTime + "至" + listItem.occupyEndTime
            );
            // 根据需要更新evaluation
            // 例如，如果morning时间段有占用，则将Morning设为1
            if (listItem.bidEvaluationPeriod == 0) {
              timePeriodItem.evaluation.Morning = 1;
            } else if (listItem.bidEvaluationPeriod == 1) {
              timePeriodItem.evaluation.Afternoon = 1;
            } else if (listItem.bidEvaluationPeriod == 2) {
              timePeriodItem.evaluation.allDay = 1;
            }
          }
        });
      });
    },

    handleClick(tab, event) {
      this.getNext15Days();
      this.initTimePeriod(tab.name);
    },

    showOpeningRoom(e) {
      if (e) {
        this.initTimePeriod(this.activeOpenRoom);
        this.openingRoom = true;
      }
    },
    showEvaluationRoom(e) {
      if (e) {
        this.initTimePeriod(this.activeEvaluationRoom);
        this.evaluationRoom = true;
      }
    },
    // 获取从今天开始后15天的日期，格式为XXXX年XX月XX日
    getNext15Days() {
      const dates = [];
      const today = new Date();
      for (let i = 0; i < 15; i++) {
        const date = new Date(today.getTime() + i * 24 * 60 * 60 * 1000);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;

        const dateObj = {
          time: formattedDate,
          openPeriod: [],
          evaluation: {
            Morning: 0,
            Afternoon: 0,
            allDay: 0,
          },
        };
        dates.push(dateObj);
      }
      this.timePeriod = dates;
      return dates;
    },

    downloadTenderFiles() {
      if (new Date(this.formData.tenderNotice.bidOpeningTime) <= new Date()) {
        this.$modal.msgError("已超过采购文件下载截至时间！");
      } else {
        downloadTenderFile(this.formData.tenderNotice.noticeId).then((result) => {
          const blob = new Blob([result]);
          let downloads = document.createElement("a");
          let href = window.URL.createObjectURL(blob);
          downloads.href = href;
          let noticeVersion = "";
          if (this.formData.tenderNotice.changeNum > 0) {
            noticeVersion = this.formData.tenderNotice.changeNum + '.0';
          }
          downloads.download = '采购文件' + noticeVersion + '.zip';
          document.body.appendChild(downloads);
          downloads.click();
          document.body.removeChild(downloads);
          window.URL.revokeObjectURL(href);
        });
      }
    },
    changeOpeningRoom(value) {
      if (value) {
        const selectedItem = this.bidOpeningRoomOptions.find(
          (item) => item.venueId === value
        );
        this.bidOpeningRoom.venueName = selectedItem.venueName;
        this.bidOpeningRoom.occupyStartTime = selectedItem.projectDuration;
      } else {
        this.formData.projectStartTime = "";
        this.formData.projectDeadline = "";
      }
    },
    changebidOpeningPeriod(value) {
      this.bidOpeningRoom.occupyStartTime =
        this.formData.bidOpeningTime + " " + value[0];
      this.bidOpeningRoom.occupyEndTime =
        this.formData.bidOpeningTime + " " + value[1];
    },

    handleClose(done) {
      this.getNext15Days();
      done();
    },
    // 变更公告跳转
    changeNotice() {
      // 需要一个标识表明是新增
      this.$router.push({
        path: "/tender/notice/change/" + this.$route.params.noticeId,
      });
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: rgba(185, 248, 191, 1);

  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
