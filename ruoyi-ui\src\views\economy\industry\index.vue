<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="行业代码分类" prop="industrCode">
        <el-input
          v-model="queryParams.industrCode"
          placeholder="请输入行业代码分类"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行业数据等级" prop="industrLevel">
        <el-input
          v-model="queryParams.industrLevel"
          placeholder="请输入行业数据等级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行业名称" prop="industryName">
        <el-input
          v-model="queryParams.industryName"
          placeholder="请输入行业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="行业排序" prop="industrySort">
        <el-input
          v-model="queryParams.industrySort"
          placeholder="请输入行业排序"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效标识" prop="validFlag">
        <el-select v-model="queryParams.validFlag" placeholder="请选择有效标识" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="删除标识" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标识" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker clearable
          v-model="queryParams.createTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间" prop="updateTime">
        <el-date-picker clearable
          v-model="queryParams.updateTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择修改时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['economy:industry:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['economy:industry:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['economy:industry:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['economy:industry:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="industryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="行业代码分类" align="center" prop="industrCode" />
      <el-table-column label="行业数据等级" align="center" prop="industrLevel" />
      <el-table-column label="行业名称" align="center" prop="industryName" />
      <el-table-column label="行业排序" align="center" prop="industrySort" />
      <el-table-column label="有效标识" align="center" prop="validFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_yes_no" :value="scope.row.validFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['economy:industry:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['economy:industry:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改国民经济行业分类对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="行业代码分类" prop="industrCode">
          <el-input v-model="form.industrCode" placeholder="请输入行业代码分类" />
        </el-form-item>
        <el-form-item label="行业数据等级" prop="industrLevel">
          <el-input v-model="form.industrLevel" placeholder="请输入行业数据等级" />
        </el-form-item>
        <el-form-item label="行业名称" prop="industryName">
          <el-input v-model="form.industryName" placeholder="请输入行业名称" />
        </el-form-item>
        <el-form-item label="行业排序" prop="industrySort">
          <el-input v-model="form.industrySort" placeholder="请输入行业排序" />
        </el-form-item>
        <el-form-item label="有效标识" prop="validFlag">
          <el-radio-group v-model="form.validFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="删除标识" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listIndustry, getIndustry, delIndustry, addIndustry, updateIndustry } from "@/api/economy/industry";

export default {
  name: "Industry",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 国民经济行业分类表格数据
      industryList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        industrCode: null,
        industrLevel: null,
        industryName: null,
        industrySort: null,
        validFlag: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        industrCode: [
          { required: true, message: "行业代码分类不能为空", trigger: "blur" }
        ],
        industrLevel: [
          { required: true, message: "行业数据等级不能为空", trigger: "blur" }
        ],
        industryName: [
          { required: true, message: "行业名称不能为空", trigger: "blur" }
        ],
        validFlag: [
          { required: true, message: "有效标识不能为空", trigger: "change" }
        ],
        delFlag: [
          { required: true, message: "删除标识不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],
        updateBy: [
          { required: true, message: "修改者不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询国民经济行业分类列表 */
    getList() {
      this.loading = true;
      listIndustry(this.queryParams).then(response => {
        this.industryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        industrCode: null,
        industrLevel: null,
        industryName: null,
        industrySort: null,
        validFlag: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.industrCode)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加国民经济行业分类";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const industrCode = row.industrCode || this.ids
      getIndustry(industrCode).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改国民经济行业分类";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.industrCode != null) {
            updateIndustry(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIndustry(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const industrCodes = row.industrCode || this.ids;
      this.$modal.confirm('是否确认删除国民经济行业分类编号为"' + industrCodes + '"的数据项？').then(function() {
        return delIndustry(industrCodes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('economy/industry/export', {
        ...this.queryParams
      }, `industry_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
