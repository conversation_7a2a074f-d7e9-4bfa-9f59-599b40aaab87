import request from '@/utils/request'

// 查询企业信息列表
export function listInfo(query) {
  return request({
    url: '/ent/info/list',
    method: 'get',
    params: query
  })
}

// 查询企业信息详细
export function getInfo(entId) {
  return request({
    url: '/ent/info/' + entId,
    method: 'get'
  })
}

// 新增企业信息
export function addInfo(data) {
  return request({
    url: '/ent/info',
    method: 'post',
    data: data
  })
}

// 修改企业信息
export function updateInfo(data) {
  return request({
    url: '/ent/info',
    method: 'put',
    data: data
  })
}

// 删除企业信息
export function delInfo(entId) {
  return request({
    url: '/ent/info/' + entId,
    method: 'delete'
  })
}

// 更新审核企业信息
export function updateAndAudit(data) {
  return request({
    url: '/ent/info/updateAndAudit',
    method: 'post',
    data: data
  })
}

// 核实企业二级密码
export function secondPassword(data) {
  return request({
    url: '/ent/info/secondPassword',
    method: 'post',
    data: data
  })
}

// 查询企业信息列表
export function resetSecretKey(entId) {
  return request({
    url: '/ent/info/resetSecretKey?entId='+entId,
    method: 'get'
  })
}