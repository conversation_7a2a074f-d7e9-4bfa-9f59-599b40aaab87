<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
          <div >
            <div style="font-size:18px;color: #606266;">采购需求： 
              <span style="font-size:14px;color:red">请按照模板格式上传采购需求内容。支持xlsx。</span>
            <a style="font-size:14px;text-decoration: underline;color:blue;" 
            href="/prod-api/profile/templates/tender_template/采购需求.xlsx" target="_blank" >采购需求模板</a> </div>
          <FileUpload v-model="itemContent.substantiveReq.requirement" :limit="1" :is-show-tip="false" :fileType="['xlsx']"></FileUpload>
          </div>
          <el-divider></el-divider>
          <div >
            <div style="font-size:18px;color: #606266;">报价明细表： 
              <span style="font-size:14px;color:red">请上传供投标人填写的报价明细表模板。支持xlsx。</span>
            <a style="font-size:14px;text-decoration: underline;color:blue;" 
            href="/prod-api/profile/templates/tender_template/明细报价表.xlsx" target="_blank" >报价明细表模板</a> </div>
          <FileUpload v-model="itemContent.quantityParamReq.requirement" :limit="1" :is-show-tip="false" :fileType="['xlsx']"></FileUpload>
          </div>
          <el-divider></el-divider>
          <div >
            <div style="font-size:18px;color: #606266;">响应文件偏离表： 
              <span style="font-size:14px;color:red">请上传供投标人填写的响应文件偏离表模板。支持xlsx。</span>
            <a style="font-size:14px;text-decoration: underline;color:blue;" 
            href="/prod-api/profile/templates/tender_template/偏离表.xlsx" target="_blank" >响应文件偏离表模板</a> </div>
          <FileUpload v-model="itemContent.paramReq.requirement" :limit="1" :is-show-tip="false" :fileType="['xlsx']"></FileUpload>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      itemContent: {
        paramReq:{
          requirement:""
        },
        quantityParamReq:{
          requirement:""
        },
        substantiveReq:{
          requirement:""
        }
      },
      excelUrl: "",
      requirement: "",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        this.requirement = this.itemContent.substantiveReq.requirement;
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      console.log("this.itemContent:", this.itemContent);
      let newContent = JSON.stringify(this.itemContent);
      if(this.itemContent.substantiveReq.requirement===""){
        this.$message.error("请上传 采购需求");
      }else if(this.itemContent.quantityParamReq.requirement===""){
        this.$message.error("请上传 报价明细表");
      }else if(this.itemContent.paramReq.requirement===""){
        this.$message.error("请上传 响应文件偏离表");
      }else{
        const postData = {
          entFileId: this.itemInfo.entFileId,
          projectFileId: this.itemInfo.projectFileId,
          projectId: this.projectInfo.projectId,
          uItems: [
            {
              itemName: this.itemInfo.itemName,
              entFileItemId: this.itemInfo.entFileItemId,
              projectFileId: this.itemInfo.projectFileId,
              projectFileItemId: this.itemInfo.projectFileItemId,
              itemContent: newContent,
            },
          ],
        };
        console.log("postData：", postData);
        saveUinfo(postData).then((response) => {
          if (response.code === 200) {
            this.dialogVisible = false;
            this.$message.success("保存成功");
            this.$emit("saveSuccess", response.data);
          }
        });
      }
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
.text {
  color: rgba(255, 87, 51, 1);
  font-size: 16px;
  line-height: 100%;
  text-align: left;
  text-indent: 2em;
}
</style>
