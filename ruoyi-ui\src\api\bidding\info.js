import request from '@/utils/request'

// 查询参与投标人信息列表
export function listInfo(query) {
  return request({
    url: '/bidding/info/list',
    method: 'get',
    params: query
  })
}

// 查询参与投标人信息详细
export function getInfo(bidderId) {
  return request({
    url: '/bidding/info/' + bidderId,
    method: 'get'
  })
}

// 新增参与投标人信息
export function addInfo(data) {
  return request({
    url: '/bidding/info',
    method: 'post',
    data: data
  })
}

// 修改参与投标人信息
export function updateInfo(data) {
  return request({
    url: '/bidding/info',
    method: 'put',
    data: data
  })
}

// 删除参与投标人信息
export function delInfo(bidderId) {
  return request({
    url: '/bidding/info/' + bidderId,
    method: 'delete'
  })
}
