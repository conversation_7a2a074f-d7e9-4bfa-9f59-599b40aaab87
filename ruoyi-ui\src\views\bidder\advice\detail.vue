<template>
  <div style="margin-top:20px;">

    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    采购项目
                  </div></td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell" >
                      {{formData.projectName}}
                    </div></td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell cell-right-border">
                    项目编号
                  </div></td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{formData.projectCode}}
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>


    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>投标人信息</span>
          </div>
          <div >
      <el-table v-loading="loading" :data="bidderInfoList" style="width: 100%;margin: auto;">
        <el-table-column label="成交人"  min-width="6" align="center" prop="bidderName" />
        <el-table-column label="投标金额"  min-width="2" align="center" prop="bidderAmount" />
        <el-table-column label="排名"  min-width="1" align="center" prop="ranking" />
      </el-table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>文件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr v-for="(item, key) in formData.attachmentMap" :key="key">
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{key}}</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <template>
                      <FileUpload
                          :value="item"
                          :fileType="['pdf', 'doc', 'docx']"
                          @input="handleInput(key, $event)"
                          :isShowTip="false"
                          :showOnly="!formData.submit"
                    ></FileUpload>
                    </template>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

    <div slot="footer" class="dialog-footer" style="text-align: center;">
      <!-- <el-button  v-if="formData.submit" type="success" @click="processSubmit">提    交</el-button>
      <el-button  v-if="formData.audit" type="success" @click="processAudit(1)">审核通过</el-button>
      <el-button  v-if="formData.audit" type="danger" @click="processAudit(0)">退    回</el-button> -->
      <el-button @click="close">关闭</el-button>
    </div>


    <!-- 添加或修改成交通知书对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="auditOpen" width="50%" append-to-body>
          <el-input type="textarea" :rows="3" v-model="formData.auditRemark" placeholder="请输入审核意见" />
      <div slot="footer" class="dialog-footer">
        <el-button type="success" @click="audit">确 定</el-button>
        <el-button  @click="auditOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { getAdvice, busiProcess } from "@/api/bidder/advice";
import { getListRecord } from "@/api/bidding/record";
import {listInfo} from "@/api/bidding/info";
import { getDicts } from "@/api/system/dict/data";
export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      formData: {
        attachmentList: [],
        attachmentMap:{}
      },
      attachmentMap: {},
      title:'',
      auditOpen:false,
      rules: {
        projectId: [{
          required: true,
          message: '请选择采购项目',
          trigger: 'change'
        }],
        bidOpeningEndTime: [{
          required: true,
          message: '请选择开标结束时间',
          trigger: 'change'
        }],
      },
      field102Action: '/common/upload',
      field102fileList: [],
      projectIdOptions: [],
      projectMap:{},
      // 投标人表格数据
      bidderInfoList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    var adviceNoteId = this.$route.params.adviceNoteId;
    this.getInfo(adviceNoteId);
    // this.getAttachmentTypes();
  },
  mounted() {},
  methods: {
    close() {
      this.$tab.closePage();
    },
    getInfo(id){
      getAdvice(id).then( (result) => {
        if(result.code==200){
          this.formData = result.data;
          this.getWinnerBidder();
        }
      });
    },
    handleInput(i, j){
        console.info(i+"    "+j);
        this.attachmentMap[i] = j;
    },
    getWinnerBidder(){
      this.loading = true;
      listInfo({projectId:this.formData.projectId, isWin:1}).then( (result) => {
        if(result.code==200){
          this.bidderInfoList = result.rows;
        }
        this.loading = false;
      });
    },
    processSubmit(){
      this.formData.auditResult = 1;
      this.formData.auditRemark = "提交申请";
      this.audit();
    },
    processAudit(auditResult){
      this.formData.auditResult = auditResult;
      if(auditResult==1){
        this.title = "通过意见";
        this.formData.auditRemark = "通过";
      }else{
        this.title = "退回意见";
        this.formData.auditRemark = "";
      }
      this.auditOpen = true;
    },
    audit(){
      this.loading = true;
      this.formData.attachmentMap = this.attachmentMap;
      busiProcess(this.formData).then( (result) => {
        if(result.code==200){
        this.$message({
          message: '提交审核成功',
          type: 'success'
        });
          this.$tab.closePage();
        }else{
          this.$message.error("提交审核失败，请联系平台");
        }
      });
    },
    changeProject(r){
      let p = this.projectMap[r];
      this.formData.projectCode = p.projectCode;
      this.formData.projectName = p.projectName;
      console.info(this.formData);
      this.loading = true;
      getListRecord({projectId:this.formData.projectId}).then( (result) => {
        if(result.code==200){
        this.$message({
          message: '提交审核成功',
          type: 'success'
        });
          this.$tab.closePage();
        }
        this.$message.error("提交审核失败，请联系平台");
      });
    }
  }
}

</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}
.cell-right-border {
  border-right:1px solid #dfe6ec
}
 .el-form-item {
    margin-bottom: 0px;
  }
</style>

<style scoped>
/deep/ .el-upload {
 float: right;
}
/deep/ .el-upload-list {
 width: 90%;
}
</style>
