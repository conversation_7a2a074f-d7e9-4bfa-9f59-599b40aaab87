<!-- 供应商头部 -->
<template>
  <div class="head">
    <el-row>
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="head-top">
          <div class="greetings">
            <div style="margin-right: 27px">鹤壁市</div>
            <div>你好，欢迎使用限额以下采购交易平台</div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="head-bottom">
          <el-link type="primary" style="float: right"
            target="_blank"
            href="https://xeyx.hbszfcgxh.com:10443/prod-api/profile/upload/tools/新中新webapi读卡服务3.3.4.zip"
            >下载驱动</el-link>
        
          <div class="search">
            <div style="margin-right: 92px; margin-left: 10px">
              <div class="title">
                <span style="margin-right: 20px">评审专家身份信息</span>
                <div>
                  <el-button
                    style="background-color: #176adb; color: #fff"
                    @click="readIdCard"
                    >读取</el-button
                  >
                </div>
              </div>
              <div></div>
            </div>
          </div>
          

          <el-image
            class="background-img"
            :src="background"
            fit="cover"
          ></el-image>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { readIdCard } from "@/api/idCardScan/idCard.js";
export default {
  name: "Head",
  props: {},
  data() {
    return {
      activeName: "",
      title: require("@/assets/images/title.png"),
      background: require("@/assets/images/home-background.png"),
      userInfo: {},
    };
  },
  watch: {},
  created() {
    this.userInfo = this.$store.state.user;
  },
  mounted() {},
  methods: {
    readIdCard() {
      readIdCard()
        .then((result) => {
          console.log(result);
          if (result.data.code == 0) {
            this.$message.success(result.data.retmsg);
            console.log(321, result.data);
            this.$emit("readIdCard", result.data);
          } else {
            this.$message.error(result.data.retmsg);
          }
        })
        .catch((err) => {
          this.$message.error("读取身份证失败");
        });
    },
    active(type) {
      if (type != "帮助中心") {
        this.$router.push("/login");
      } else {
        this.$modal.msgSuccess("跳转到" + type + "页面");
      }
    },
    handleCommand(command) {
      this.$modal.msgSuccess("跳转到帮助中心-" + command + "页面");
    },
    toggleActive(activeName, status) {
      if (this.$store.getters.supplierBidOpenStatus >= status) {
        this.activeName = activeName;
      } else {
        this.$message.warning("未开始");
      }
    },
    todoPage() {
      return {
        path: "/login",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  height: 210px;
  .head-top {
    background: #176adb;

    height: 40px;
    padding: 0 15%;

    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;

    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #ffffff;
    letter-spacing: 0;
    .greetings {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      align-items: center;
    }
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        height: 25px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 17px;
        color: #ffffff;
        letter-spacing: 0;

        margin-right: 28px;
        cursor: pointer;
        .el-dropdown-link {
          cursor: pointer;
          color: #ffffff;
        }
        .el-icon-arrow-down {
          font-size: 12px;
          color: #ffffff;
        }
      }
      .active {
        color: #000;
      }
    }
  }
  .head-bottom {
    height: 170px;
    padding: 0 15%;
    position: relative;
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        width: 110px;
        height: 41px;
        line-height: 41px;
        font-family: SourceHanSansSC-Bold;
        font-weight: 700;
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;
        border-radius: 6px;
        text-align: center;

        margin-right: 60px;
        cursor: pointer;
      }
      .active {
        background: #176adb;
        color: #fff;
      }
    }
    .search {
      height: 148px;
      display: flex;
      align-items: center;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      .query {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;

        margin-right: 30px;
        width: 470px;
        height: 46px;
        .input {
          border-radius: 6px 0 0 6px;
          ::v-deep .el-input__inner {
            border: 2px solid #176adb;
          }
        }
        .button {
          background: #176adb;
          width: 46px;
          padding-right: 0;
          padding-left: 0;
          color: #fff;
          border-radius: 0 6px 6px 0;
          border: 1px solid #176adb;
        }
      }
      .publish {
        width: 166px;
        height: 46px;
        background: #176adb;
        border-radius: 6px;
        color: #ffffff;

        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: justify;
        text-align: center;
      }
    }

    .background-img {
      position: absolute;
      bottom: 0;
      right: 15.7%;
      z-index: -1; /* 将图片放置在其他元素下方 */
    }
  }
}

.title {
  display: flex;
  justify-content: space-between;
  width: 400px;
  height: 50px;
  line-height: 50px;
  text-align: center;
  font-family: SourceHanSansSC-Heavy;
  font-weight: 900;
  font-size: 35px;
  color: #333333;
  letter-spacing: 0;
}
</style>
