<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="info">
      <div class="content">
        <div style="padding:40px 60px">
          <el-table
            ref="multipleTable"
            :data="tableData"
            style="width: 100%"
            :header-cell-style="headStyle"
            :cell-style="cellStyle"
            @selection-change="handleSelectionChange"
          >
            <el-table-column
              :cell-style="{ backgroundColor: '#fff' }"
              type="selection"
              width="55"
            >
            </el-table-column>
            <el-table-column prop="bidderName"></el-table-column>
            <el-table-column
              v-for="(item, index) in column"
              :key="index"
              :prop="item"
              :label="item"
            >
              <template slot-scope="scope">
	              <span v-if="item == '币种'">{{scope.row.priceUnit}}</span>
                <span v-else-if="scope.row[item] == null || scope.row[item]==undefined">/</span>
                <span v-else>{{scope.row[item]}}</span>
              </template>
            </el-table-column>
<!--	          <el-table-column prop="priceUnit"></el-table-column>-->
          </el-table>
          <div ><span style="color:red">注意：如供应商属于小型和微型、监狱企业、残疾人福利性单位等企业，请勾选供应商名称前的方框，并点击“重新打分”按钮，系统会根据价格扣除公式重新进行计算。</span></div>

          <div
            style="text-align:left;font-size:14px;line-height:1.5;margin-bottom:20px;margin-top:20px"
            v-html="conten"
          >

          </div>

          <div
            class="operation"
            v-if="!finish"
          >
            <el-button
                v-if="project.tenderMode !=3"
              class="item-button"
              style="background: #F5F5F5;color:#176ADB"
              @click="secondOffer"
            >发起二次报价</el-button>
            <el-button
              v-if="isLeader"
              class="item-button"
              @click="completed"
            >节点评审完成</el-button>
            <el-button
              v-else
              class="item-button"
              @click="back"
            >返回</el-button>
            <el-button
              class="item-button"
              style="background: #F5F5F5;color:#176ADB"
              @click="reQuotes()"
            >价格折扣</el-button>
          </div>
          <div
            v-else
            class="operation"
          >
            <el-button
              class="item-button"
              @click="back"
            >返回</el-button>
          </div>
        </div>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
import { quotationScore, expertInfoById } from "@/api/expert/review";
import { getProject } from "@/api/tender/project";
import { listProcess,updateProcess } from "@/api/evaluation/process";
import { selectByProject } from "@/api/evaluation/expertNodeInfo";

export default {
  name: "qualification",
  data() {
    return {
      project: {
        projectName: "",
      },
      tableData: [],
      column: [],
      data: {},
      conten: "",
      finish: false,

      leader: {},
      isLeader: false,
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      xwqy: [],
      cxbj: false,
    };
  },
  methods: {
    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      // 根据项目id查询项目信息
      getProject(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.project = response.data;
        } else {
          this.$message.warning(response.msg);
        }
      });
      const data = {
        itemId: this.$route.query.scoringMethodItemId,
        projectId: this.$route.query.projectId,
        resultId: expertInfo.resultId,
        xwqys: this.xwqy,
        cxbj: this.cxbj,
      };
      quotationScore(data).then((response) => {
				
        if (response.code == 200) {
          this.data = response.data;
          this.column = response.data.col;
	        this.column.push("币种")
	        console.log(this.column)
          // 更新表格数据
          this.tableData = this.assembleData(
            this.data.busiBidderInfos,
            this.data.resultMap,
	          response.data.tenderNotice
          );
	        console.log(this.tableData)
          // 确保组件已经初始化
          this.$nextTick(() => {
            const selected = this.tableData.filter((item) => item.xwqy === 1);
            if (selected.length > 0) {
              // 确保选中的行确实存在
              selected.forEach((row) => {
                this.$refs.multipleTable.toggleRowSelection(row); // 尝试选择该行
              });
            }
          });
          this.conten = this.data.scoringMethodItem.itemContent;
        } else {
          this.$message.warning(response.msg);
        }
      });
      this.finish = this.$route.query.finish;
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.leader = response.data.find((item) => {
            return item.expertLeader == 1;
          });
          if (this.leader.zjhm == this.$route.query.zjhm) {
            this.isLeader = true;
          }
        } else {
          this.$message.warning(response.msg);
        }
      });

      this.getProcess();
    },
    assembleData(busiBidderInfos, resultMap,tenderNotice) {
			
      // 初始化结果数组
      const assembledData = [];

      // 遍历每个供应商信息
      busiBidderInfos.forEach((bidderInfo) => {
        const xwqy = bidderInfo.xwqy;

        const bidderId = bidderInfo.bidderId;
        const bidderName = bidderInfo.bidderName;

        // 从 resultMap 中获取当前供应商的报价信息
        const scores = resultMap[bidderId] || {};
	      const priceUnit = tenderNotice.priceUnit == "1" ? "元" : "%";
				
        // 创建一个新的对象来存储当前供应商的所有信息
        const supplierData = {
          xwqy: xwqy,
          entId: bidderId,
          bidderName: bidderName,
	        priceUnit,
          ...scores, // 使用展开运算符将报价信息合并到新对象中
        };
        // 将新对象添加到结果数组中
        assembledData.push(supplierData);
      });
			
      // 返回组装后的数据
      return assembledData;
    },
    // 选择小微企业
    handleSelectionChange(val) {
      this.xwqy = val.map((item) => {
        return item.entId;
      });
    },
    // 重新报价
    reQuotes() {
      this.cxbj = true;
      this.init();
    },
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
    getProcess(){
      selectByProject(this.$route.query.projectId).then(result => {
        console.log("11111111111111111");
        console.log(result);
        console.log("11111111111111111");
        const params = {
          "projectEvaluationId":result.data.projectEvaluationId,
          "scoringMethodItemId":this.$route.query.scoringMethodItemId
        }
        listProcess(params).then(response => {
          console.log("response:",response.rows[0])
          localStorage.setItem(
            "evalProjectEvaluationProcess",
            JSON.stringify(response.rows[0])
          );
        });
      })

    },
    // 节点评审完成
    completed() {
      const evaluationProcessId = JSON.parse(
        localStorage.getItem("evalProjectEvaluationProcess")
      );
      var er = [];
      for(let index of this.tableData){
        let i = {};
        i["gys"]=index["bidderName"];
        i["bidder"]=index["entId"];
        i["result"]=index["投标报价得分"];
        er.push(i);
      }
      let ers = JSON.stringify(er);
      const data = {
        evaluationProcessId: evaluationProcessId.evaluationProcessId,
        evaluationResult: ers,
        evaluationState: 2,
        evaluationResultRemark: "",
      };
      updateProcess(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
            },
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
    // 表格颜色
    cellStyle({ row, column, rowIndex, columnIndex }) {
      let data = {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      };
      if (columnIndex == 0) {
        data.background = "#176ADB";
      }
      return data;
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 64vh;
  margin: 20px 0;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.little-title {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
