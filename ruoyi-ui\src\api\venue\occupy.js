import request from '@/utils/request'

// 查询场地占用列表
export function listOccupy(query) {
  return request({
    url: '/venue/occupy/list',
    method: 'get',
    params: query
  })
}

// 查询场地占用详细
export function getOccupy(occupyId) {
  return request({
    url: '/venue/occupy/' + occupyId,
    method: 'get'
  })
}

// 新增场地占用
export function addOccupy(data) {
  return request({
    url: '/venue/occupy',
    method: 'post',
    data: data
  })
}

// 修改场地占用
export function updateOccupy(data) {
  return request({
    url: '/venue/occupy',
    method: 'put',
    data: data
  })
}

// 删除场地占用
export function delOccupy(occupyId) {
  return request({
    url: '/venue/occupy/' + occupyId,
    method: 'delete'
  })
}
