<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <!-- <el-form-item label="通知书id" prop="noteId">
        <el-input
          v-model="queryParams.noteId"
          placeholder="请输入通知书id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="项目代码" prop="projectCode">
        <el-input
          v-model="queryParams.projectCode"
          placeholder="请输入项目代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="项目名称" prop="projectName">
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="中标人id" prop="bidderId">
        <el-input
          v-model="queryParams.bidderId"
          placeholder="请输入中标人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="中标人代码" prop="bidderCode">
        <el-input
          v-model="queryParams.bidderCode"
          placeholder="请输入中标人代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item label="中标人名称" prop="bidderName">
        <el-input
          v-model="queryParams.bidderName"
          placeholder="请输入中标人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['bidder:advice:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="adviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="通知书id" align="center" prop="noteId" /> -->
      <!-- <el-table-column label="项目id" align="center" prop="projectId" /> -->
      <el-table-column label="项目代码" align="center" prop="projectCode" />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <!-- <el-table-column label="中标人id" align="center" prop="bidderId" /> -->
      <!-- <el-table-column label="中标人代码" align="center" prop="bidderCode" /> -->
      <el-table-column label="中标人名称" align="center" prop="bidderName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleQueryInfo(scope.row)"
            v-hasPermi="['bidder:advice:query']"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bidder:advice:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改成交通知书对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <!-- <el-form-item label="通知书id" prop="noteId">
          <el-input v-model="form.noteId" placeholder="请输入通知书id" />
        </el-form-item>
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item> -->
        <el-form-item label="项目代码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目代码" />
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <!-- <el-form-item label="中标人id" prop="bidderId">
          <el-input v-model="form.bidderId" placeholder="请输入中标人id" />
        </el-form-item>
        <el-form-item label="中标人代码" prop="bidderCode">
          <el-input v-model="form.bidderCode" placeholder="请输入中标人代码" />
        </el-form-item> -->
        <el-form-item label="中标人名称" prop="bidderName">
          <el-input v-model="form.bidderName" placeholder="请输入中标人名称" />
        </el-form-item>
        <!-- <el-form-item label="删除标记" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标记" />
        </el-form-item> -->
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAdvice, getAdvice, delAdvice, addAdvice, updateAdvice } from "@/api/bidder/advice";

export default {
  name: "Advice",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 成交通知书表格数据
      adviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noteId: null,
        projectId: null,
        projectCode: null,
        projectName: null,
        bidderId: null,
        bidderCode: null,
        bidderName: null,
        isScope: true,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询成交通知书列表 */
    getList() {
      this.loading = true;
      listAdvice(this.queryParams).then(response => {
        this.adviceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noteId: null,
        projectId: null,
        projectCode: null,
        projectName: null,
        bidderId: null,
        bidderCode: null,
        bidderName: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.noteId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加成交通知书";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const noteId = row.noteId || this.ids
      getAdvice(noteId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改成交通知书";
      });
    },
    /** 查看按钮操作 */
    handleQueryInfo(row) {
      this.reset();
      const noteId = row.noteId || this.ids
      this.$router.push("/bidder/advice/detail/"+noteId);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.noteId != null) {
            updateAdvice(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addAdvice(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const noteIds = row.noteId || this.ids;
      this.$modal.confirm('是否确认删除成交通知书编号为"' + noteIds + '"的数据项？').then(function() {
        return delAdvice(noteIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bidder/advice/export', {
        ...this.queryParams
      }, `advice_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
