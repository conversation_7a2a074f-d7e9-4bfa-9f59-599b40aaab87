<template>
  <div>

    <Head></Head>
    <div class="overAll">
      <div class="app-container home">
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="24"
          >
            <el-card
              shadow="never"
              class="informationPublicity"
            >
              <div
                slot="header"
                class="announcement"
              >
                <div class="info">
                  <div class="clearfix">{{ title }}</div>
                  <div
                    class="type"
                    v-if="title == '采购信息'"
                  >
                    <div
                      class="select"
                      :class="{ active: activeName === '采购公告' }"
                      @click="toggleActive('20')"
                    >
                      采购公告
                    </div>
                    <!-- <div
                      class="select"
                      :class="{ active: activeName === '变更公告' }"
                      @click="toggleActive('21')"
                    >
                      变更公告
                    </div> -->
                    <div
                      class="select"
                      :class="{ active: activeName === '结果公告' }"
                      @click="toggleActive('60')"
                    >
                      结果公告
                    </div>
                    <div
                      class="select"
                      :class="{ active: activeName === '取消公告' }"
                      @click="toggleActive('-2')"
                    >
                      取消公告
                    </div>
                  </div>
                </div>
                <div class="input">
                  <el-input
                    class="search"
                    placeholder="项目名称 或 项目代码"
                    suffix-icon="el-icon-search"
                    v-model="input"
                    @keyup.enter.native="search"
                  >
                  </el-input>
                </div>
              </div>
              <el-table
                :data="informationPublicity"
                style="width: 100%"
                :row-class-name="tableRowClassName"
                :cell-style="{ 'text-align': 'center' }"
                :header-cell-style="{
                  background: '#176ADB',
                  color: '#FFFFFF',
                  'text-align': 'center',
                }"
              >
                <el-table-column label="公告名称">
                  <template slot-scope="scope">
                    <div class="name">
                      <div class="label">{{ activeName }}</div>
                      <!-- <el-tooltip
                        :content="scope.row.name"
                        placement="top-start"
                        :disabled="isShowTooltip"
                      > -->
                      <router-link
                        ref="remark"
                        :to="todoPage(scope.row)"
                        class="link-type row-name"
                      >
                        <span>{{ scope.row.name }}</span>
                      </router-link>
                      <!-- </el-tooltip> -->
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="tendererName"
                  label="采购单位"
                >
                </el-table-column>
                <el-table-column
                  prop="tenderModeName"
                  label="采购方式"
                  width="150"
                >
                </el-table-column>
                <el-table-column
                  prop="releaseTime"
                  label="成交时间"
                  width="150"
                >
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.releaseTime, '{y}-{m}-{d}') }}</span>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col :span="24">
            <pagination
              v-show="info.totalRecords != 0"
              :total="info.totalRecords"
              :page.sync="info.currentPage"
              :limit.sync="info.pageSize"
              @pagination="toggleActive(active)"
            />
          </el-col>
        </el-row>
      </div>
    </div>
    <Foot></Foot>
  </div>
</template>

<script>
import {
  portalNoticeList,
  portalNoticeInfo,
  portalNoticeProcess,
} from "@/api/portal/portal";
export default {
  data() {
    return {
      isShowTooltip: false,
      activeName: "采购公告",
      active: "20",
      activeMap: {
        20: "采购公告",
        21: "变更公告",
        60: "结果公告",
        "-2": "取消公告",
      },
      informationPublicity: [],
      title: "",
      input: "",
      info: {
        currentPage: 1,
        pageSize: 10,
        totalRecords: 100,
      },
    };
  },
  created() {},
  mounted() {
    if (this.$route.query.result == "publicity") {
      this.isShowType = true;
    }
    this.init();
  },
  watch: {
    "$route.query": {
      handler: function (newQuery, oldQuery) {
        // 检查新旧参数是否有变化，根据需要执行重新渲染的操作
        if (newQuery.result !== oldQuery.result) {
          // 执行需要重新渲染的逻辑
          this.init(); // 例如重新获取数据
        }
      },
      deep: true, // 深度监听，可选
    },
  },
  methods: {
    init() {
      if (this.$route.query.result == "publicity") {
        this.title = "采购信息";
        portalNoticeList({
          pageNum: 1,
          pageSize: 10,
          dataType: "20",
        }).then((result) => {
          this.info.totalRecords = result.total;
          this.informationPublicity = result.rows;
        });
      } else if (this.$route.query.result == "notice") {
        this.title = "通知公告";
      } else if (this.$route.query.result == "policies") {
        this.title = "政策法规";
      } else if (this.$route.query.result == "news") {
        this.title = "新闻通知";
      }
    },
    // 翻页
    handlePageChangeInfo(page) {
      this.info.currentPage = page;
      this.init();
    },
    search() {},
    errorHandler() {
      return true;
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return "even-row";
      } else {
        return "odd-row";
      }
    },
    toggleActive(type) {
      this.active = type;
      this.activeName = this.activeMap[type];
      console.log("1111");
      portalNoticeList({
        pageNum: 1,
        pageSize: 10,
        dataType: type,
      }).then((result) => {
        this.informationPublicity = result.rows;
        this.info.totalRecords = result.total;
      });
    },
    todoPage(row) {
      return {
        path: "/information",
        query: { projectId: row.projectId, dataType: this.active },
      };
    },
  },
};
</script>

<style scoped lang="scss">
.overAll {
  position: relative;

  background-color: #f5f5f5;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.home {
  background-color: #f5f5f5;
  min-height: 61vh;
  width: 70%;
  .el-row {
    // margin-bottom: 20px;
  }
  .informationPublicity {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
    }
    ::v-deep .el-table .cell {
      .name {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .label {
          width: 70px;
          height: 23px;
          background: #4694ff;
          border-radius: 3px;
          color: #ffffff;
          margin-right: 15px;
        }
        .row-name {
          width: 300px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 控制显示的行数 */
          overflow: hidden;
        }
      }
    }
    .announcement {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      .info {
        display: flex;
        .type {
          display: flex;
          flex-wrap: wrap;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          margin-left: 31px;
          .select {
            width: 60px;
            height: 22px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 15px;
            color: #666666;
            letter-spacing: 0;

            margin-right: 26px;
            cursor: pointer;
          }
          .active {
            color: #f98319;
          }
        }
      }
    }
  }
  .clearfix {
    font-family: SourceHanSansSC-Bold;
    font-weight: 700;
    font-size: 22px;
    color: #333333;
    letter-spacing: 0;
  }
  .more {
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #176adb;
    letter-spacing: 0;
    float: right;
  }
}
</style>
<style scoped lang="scss">
::v-deep .pagination-container {
  background: none;
  height: 35px;
}
::v-deep .el-card__header {
  border-bottom: none;
}
::v-deep .el-table .even-row {
  background: #eff6ff;
}

::v-deep .el-table .odd-row {
  background: #ffffff;
}

::v-deep .el-timeline-item__tail {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}
::v-deep .el-timeline-item__wrapper {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}
::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}
</style>
