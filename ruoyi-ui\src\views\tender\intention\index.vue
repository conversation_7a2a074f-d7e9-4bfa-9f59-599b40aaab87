<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <!-- <el-form-item label="意向id" prop="intentionId">
        <el-input
          v-model="queryParams.intentionId"
          placeholder="请输入意向id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="意向代码" prop="intentionCode">
        <el-input
          v-model="queryParams.intentionCode"
          placeholder="请输入意向代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="意向名称" prop="intentionName">
        <el-input
          v-model="queryParams.intentionName"
          placeholder="请输入意向名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="预算金额" prop="budgetAmount">
        <el-input
          v-model="queryParams.budgetAmount"
          placeholder="请输入预算金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目工期 (天)" prop="projectDuration">
        <el-input
          v-model="queryParams.projectDuration"
          placeholder="请输入项目工期 (天)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="采购方式" prop="tenderMode">
        <el-select v-model="queryParams.tenderMode" placeholder="请选择采购方式" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
<!-- <el-form-item label="意向开始时间">
        <el-date-picker
          v-model="daterangeIntentionStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="意向结束时间">
        <el-date-picker
          v-model="daterangeIntentionEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>&ndash;&gt;
      <el-form-item label="意向开始时间" prop="intentionStartTime">
        <el-date-picker
            v-model="form.intentionStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择意向开始时间"
            @change="setIntentionEndTime"
        />
      </el-form-item>
      <el-form-item label="意向结束时间" prop="intentionEndTime">
        <el-date-picker
            v-model="form.intentionEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择意向结束时间"
            :picker-options="endDatePickerOptions"
        />
      </el-form-item>
      <el-form-item label="删除标记" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记 (0正常 1删除)" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item> -->
<!--      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间">
        <el-date-picker
          v-model="daterangeUpdateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tender:intention:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tender:intention:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tender:intention:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['tender:intention:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="intentionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="意向id" align="center" prop="intentionId" /> -->
      <el-table-column label="意向代码" align="center" prop="intentionCode" />
<!--      <el-table-column label="意向名称" align="center" prop="intentionName" />-->
      <el-table-column label="意向名称" align="center" prop="intentionName">
        <template slot-scope="scope">
          <span style="color: #007bff; cursor: pointer;" @click="handleView(scope.row)">{{ scope.row.intentionName }}</span>
        </template>
      </el-table-column>
<!--      <el-table-column label="意向内容" align="center" prop="intentionContent" />-->
      <el-table-column label="预算金额" align="center" prop="budgetAmount" />
      <!-- <el-table-column label="项目工期 (天)" align="center" prop="projectDuration" /> -->

      <!-- <el-table-column label="意向开始时间" align="center" prop="intentionStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.intentionStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="意向结束时间" align="center" prop="intentionEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.intentionEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
<!--
      <el-table-column label="意向开始时间" align="center" prop="intentionStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.intentionStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="意向结束时间" align="center" prop="intentionEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.intentionEndTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>-->
<!--      <el-table-column label="采购方式" align="center" prop="tenderMode">-->
<!--        <template slot-scope="scope">-->
<!--          <dict-tag :options="dict.type.busi_tender_mode" :value="scope.row.tenderMode"/>-->
<!--        </template>-->
<!--      </el-table-column>-->
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看详情</el-button>

          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tender:intention:edit']"
          >修改</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tender:intention:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购意向对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
<!--        <el-form-item label="意向id" prop="intentionId">-->
<!--          <el-input v-model="form.intentionId" placeholder="请输入意向id" />-->
<!--        </el-form-item>-->
<!--        <el-form-item label="意向代码" prop="intentionCode">-->
<!--          <el-input v-model="form.intentionCode" placeholder="请输入意向代码" />-->
<!--        </el-form-item>-->
        <el-form-item label="意向名称" prop="intentionName">
          <el-input v-model="form.intentionName" placeholder="请输入意向名称" />
        </el-form-item>
        <el-form-item label="预算金额" prop="budgetAmount">
          <el-input v-model="form.budgetAmount" placeholder="请输入预算金额" />
        </el-form-item>
        <el-form-item label="项目工期 (天)" prop="projectDuration">
          <el-input v-model="form.projectDuration" placeholder="请输入项目工期 (天)" />
        </el-form-item>

        <el-form-item label="意向开始时间" prop="intentionStartTime">
          <el-date-picker clearable
            v-model="form.intentionStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择意向开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="意向结束时间" prop="intentionEndTime">
          <el-date-picker clearable
            v-model="form.intentionEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择意向结束时间">
          </el-date-picker>
        </el-form-item>
        <!-- <el-form-item label="采购方式" prop="tenderMode">
          <el-select v-model="form.tenderMode" placeholder="请选择采购方式">
            <el-option
              v-for="dict in dict.type.busi_tender_mode"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="意向内容">
          <editor v-model="form.intentionContent" :min-height="192"/>
        </el-form-item>
        <el-form-item
            v-for="dict in dict.type.busi_tender_intention_attachment"
            :key="dict.label"
            label-width="120px"
            :label="dict.label"
            class="upload"
        >
          <template>
            <FileUpload
                :value="getImgPath(dict)"
                @input="handleInput(dict, $event)"
                :fileType="['pdf', 'doc', 'docx']"
                :isShowTip="false"
              :showOnly="false"
            ></FileUpload>
          </template>
        </el-form-item>

      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listIntention, getIntention, delIntention,downLoadFile, addIntention, updateIntention } from "@/api/tender/intention";
import {download} from "@/utils/request";
import {getDicts} from "@/api/system/dict/data";

export default {
  name: "Intention",
  dicts: ['busi_tender_mode', 'busi_tender_intention_attachment','base_yes_no'],

  data() {
    return {
      fixedDays:0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购意向表格数据
      intentionList: [],
      attachmentTypes: [],
      evaluationList: [],
      attachmentMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 (0正常 1删除)时间范围
      daterangeIntentionStartTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeIntentionEndTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeCreateTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        intentionId: null,
        intentionCode: null,
        intentionName: null,
        intentionContent: null,
        budgetAmount: null,
        projectDuration: null,
        tenderMode: null,
        intentionStartTime: null,
        intentionEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {
        intentionStartTime: null,
        intentionEndTime: null,
        attachments:[]

      },
      attachmentsMap: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList()
    this.getAttachmentTypes();
    this.getConfigKey("sys.intention.fixedDays").then(response => {
      this.fixedDays = response.msg;
    });
  },
  computed: {
    endDatePickerOptions() {
      const startTime = this.form.intentionStartTime;
      return {
        disabledDate(time) {
          return time.getTime() < new Date(startTime).getTime() + this.fixedDays * 24 * 60 * 60 * 1000;
        },
      };
    },

  },
  methods: {
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
      console.log(this.attachmentsMap);
    },
    getImgPath(dict) {
      if (
          this.attachmentsMap[dict.value] &&
          this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
            .map((item) => {
              return item.filePath;
            })
            .join(",");
      }
      return "";
    },
    downLoadFile(id,value){
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length-1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId:id,
        fileName:fileName,
        filePath:value,
        resource:value
      }
      downLoadFile(params).then(response => {
        if (response["code"]==200){
          download("/common/download/resource", params, fileName);
        }
      });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
      /*
        /!** request里面的download下载函数 *!/
        //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
        console.log("文件名");*/
    },

    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },
    /** 查看详情按钮操作 */
    // handleView(row) {
    //   this.reset();
    //   const intentionId = row.intentionId || this.ids
    //   getIntention(intentionId).then(response => {
    //     this.form = response.data;
    //     this.isViewOnly = true; // 设置为查看详情模式
    //     // 使用reduce方法将attachments数组收集成一个映射
    //     this.attachmentsMap = response.data.attachments.reduce(
    //       (accumulator, attachment) => {
    //         // 如果accumulator中还没有这个fileType的键，则创建一个新数组
    //         if (!accumulator[attachment.fileType]) {
    //           accumulator[attachment.fileType] = [];
    //         }
    //         // 将当前attachment添加到对应fileType的数组中
    //         accumulator[attachment.fileType].push(attachment);
    //         return accumulator;
    //       },
    //       {}
    //     );
    //     this.open = true;
    //     this.title = "采购意向详情";
    //   });
    // },
    getAttachmentTypes(){
      getDicts("busi_tender_intention_attachment").then((result) => {
        console.info(result);
        if(result.code==200){
          this.attachmentTypes = result.data;
        }
      });
    },
    setIntentionEndTime(date) {
      const endTime = new Date(date);
      endTime.setDate(endTime.getDate() + this.fixedDays );
      if (this.form.intentionStartTime!=null){
        this.form.intentionEndTime = endTime.toISOString().split('T')[0];

      }
    },
    /** 查询采购意向列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeIntentionStartTime && '' != this.daterangeIntentionStartTime) {
        this.queryParams.params["beginIntentionStartTime"] = this.daterangeIntentionStartTime[0];
        this.queryParams.params["endIntentionStartTime"] = this.daterangeIntentionStartTime[1];
      }
      if (null != this.daterangeIntentionEndTime && '' != this.daterangeIntentionEndTime) {
        this.queryParams.params["beginIntentionEndTime"] = this.daterangeIntentionEndTime[0];
        this.queryParams.params["endIntentionEndTime"] = this.daterangeIntentionEndTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listIntention(this.queryParams).then(response => {
        this.intentionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        intentionId: null,
        intentionCode: null,
        intentionName: null,
        intentionContent: null,
        budgetAmount: null,
        projectDuration: null,
        tenderMode: null,
        intentionStartTime: null,
        intentionEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeIntentionStartTime = [];
      this.daterangeIntentionEndTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.intentionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加采购意向";
      this.reset();
      this.$router.push('/tender/intention/add/0')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const intentionId = row.intentionId || this.ids
      this.$router.push('/tender/intention/add/' + intentionId+ '/false');
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const intentionId = row.intentionId || this.ids
      this.$router.push('/tender/intention/view/' + intentionId);
    },
    /** 提交按钮 */
    submitForm() {
      var al = [];
      for(var key in this.attachmentMap){
        if(this.attachmentMap[key] != ''){
          var all = this.attachmentMap[key].split(',');
          all.forEach(element => {
            al.push({"fileType":key, "filePath":element});
          });
        }
      }
      this.form.attachments= al;

      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.intentionId != null) {
            updateIntention(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addIntention(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const intentionIds = row.intentionId || this.ids;
      this.$modal.confirm('是否确认删除采购意向编号为"' + intentionIds + '"的数据项？').then(function() {
        return delIntention(intentionIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tender/intention/export', {
        ...this.queryParams
      }, `intention_${new Date().getTime()}.xlsx`)
    },
    /** 导出按钮操作 */
    test() {
      this.$router.push('/tender/intention/pdftest');
    },

  },
  watch: {
    'form.intentionStartTime': {
      handler: 'setIntentionEndTime',
      immediate: true,
    },
  },
};
</script>
