import request from '@/utils/request'

// 查询评标记录列表
export function listEvaluation(query) {
  return request({
    url: '/bid/evaluation/list',
    method: 'get',
    params: query
  })
}

// 查询评标记录详细
export function getEvaluation(bidEvaluationId) {
  return request({
    url: '/bid/evaluation/' + bidEvaluationId,
    method: 'get'
  })
}

// 新增评标记录
export function addEvaluation(data) {
  return request({
    url: '/bid/evaluation/saveBatchBusiBidderInfo',
    method: 'post',
    data: data
  })
}
export function downLoadFile(params) {
  return request({
    url: '/bid/evaluation/downLoadFile',
    method: 'get',
    params: params
  })
}
// 修改评标记录
export function updateEvaluation(data) {
  return request({
    url: '/bid/evaluation',
    method: 'put',
    data: data
  })
}

// 删除评标记录
export function delEvaluation(bidEvaluationId) {
  return request({
    url: '/bid/evaluation/' + bidEvaluationId,
    method: 'delete'
  })
}
