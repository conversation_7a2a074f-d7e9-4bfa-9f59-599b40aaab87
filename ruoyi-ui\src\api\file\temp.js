import request from '@/utils/request'

// 查询采购/响应文件模板列表
export function listTemp(query) {
  return request({
    url: '/file/temp/list',
    method: 'get',
    params: query
  })
}

// 查询采购/响应文件模板详细
export function getTemp(documentsFileTempId) {
  return request({
    url: '/file/temp/' + documentsFileTempId,
    method: 'get'
  })
}

// 新增采购/响应文件模板
export function addTemp(data) {
  return request({
    url: '/file/temp',
    method: 'post',
    data: data
  })
}

// 修改采购/响应文件模板
export function updateTemp(data) {
  return request({
    url: '/file/temp',
    method: 'put',
    data: data
  })
}

// 删除采购/响应文件模板
export function delTemp(documentsFileTempId) {
  return request({
    url: '/file/temp/' + documentsFileTempId,
    method: 'delete'
  })
}
