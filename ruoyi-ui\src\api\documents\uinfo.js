import request from '@/utils/request'

// 查询用户编制采购文件信息保存列表
export function listUinfo(query) {
  return request({
    url: '/documents/uinfo/list',
    method: 'get',
    params: query
  })
}

// 查询用户编制采购文件信息保存列表
export function infoByParams(query) {
  return request({
    url: '/documents/uinfo/infoByParams',
    method: 'get',
    params: query
  })
}

// 查询用户编制采购文件信息保存列表
export function generateResponseZip(data) {
  return request({
    url: '/documents/uinfo/generateResponseZip',
    method: 'post',
    data: data
  })
}

// 查询用户编制采购文件信息保存列表
export function test(data) {
  return request({
    url: '/documents/uinfo/test',
    method: 'post',
    data: data
  })
}


// 查询用户编制采购文件信息保存列表
export function createProjectFileAndCgwj(data) {
  return request({
    url: '/documents/uinfo/createProjectFileAndCgwj',
    method: 'post',
    data: data
  })
}


// 查询用户编制采购文件信息保存详细
export function getUinfo(entFileId) {
  return request({
    url: '/documents/uinfo/' + entFileId,
    method: 'get'
  })
}

// 新增用户编制采购文件信息保存
export function addUinfo(data) {
  return request({
    url: '/documents/uinfo',
    method: 'post',
    params: data
  })
}

// 新增用户编制采购文件信息保存
export function checkIsOver(query) {
  return request({
    url: '/documents/uinfo/checkIsOver',
    method: 'get',
    params: query
  })
}

// 生成zip
export function generateProjectFileZip(query) {
  return request({
    url: '/documents/uinfo/generateProjectFileZip',
    method: 'get',
    params: query
  })
}

// 新增用户编制采购文件信息保存
export function saveInfo(data) {
  return request({
    url: '/documents/uinfo/saveInfo',
    method: 'post',
    data: data
  })
}

// 修改用户编制采购文件信息保存
export function updateUinfo(data) {
  return request({
    url: '/documents/uinfo',
    method: 'put',
    data: data
  })
}

// 删除用户编制采购文件信息保存
export function delUinfo(entFileId) {
  return request({
    url: '/documents/uinfo/' + entFileId,
    method: 'delete'
  })
}
