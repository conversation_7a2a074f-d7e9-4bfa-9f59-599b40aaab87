<template>
  <div style="margin-top: 20px;" class="project" v-loading="loading">
    <el-form ref="busiTenderProject" :model="formData" :rules="rules" size="medium"  label-width="0" >
    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目意向
                    </div>
                  </td>
                  <td colspan="22"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      {{ formData.tenderIntention!=null?formData.tenderIntention.intentionName:"" }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目名称
                    </div>
                  </td>
                  <td colspan="22"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      {{ formData.tenderProject.projectName }}
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      采购方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.tenderModeName }}
                  </div></td>

                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目行业
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.projectIndustryName }}
          </div></td>
                </tr>

                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目开始时间
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.projectStartTime }}
                  </div></td>

                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目工期
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.projectDuration }} 天
          </div></td>
                </tr>

                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      预算金额
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.budgetAmount }} 元
                  </div></td>

                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      资金来源
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.tenderFundSourceName }}
                </div></td>
                </tr>

                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目所属地
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.projectAreaName }}
                  </div></td>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      项目类型
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border" >
                      <dict-tag :options="dict.type.busi_project_type" :value="formData.tenderProject.projectType"/>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      获取投标人方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
<!--                    <div class="cell cell-right-border">-->
<!--                        <el-select v-model="formData.getBidderMode" placeholder="请选择" clearable :style="{ width: '100%' }" >-->
<!--                          <el-option v-for="dict in dict.type.get_bidder_mode" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>-->
<!--                        </el-select>-->
<!--                      -->
<!--                    </div>-->
                    <div class="cell cell-right-border" >
                      <dict-tag :options="dict.type.get_bidder_mode" :value="formData.tenderProject.getBidderMode"/>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>


    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>采购方信息</span>
          </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;table-layout:fixed;">
                <tbody>
                <tr v-if="formData.tenderProject.agencyFlag == 1">
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      代理机构
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.agencyName }}
                  </div></td>
                </tr>
                <tr v-if="formData.tenderProject.agencyFlag == 1">
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      联系人
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.agencyContactPerson }}
                  </div></td>

                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      联系方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.agencyPhone }}
                  </div></td>
                </tr>
                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      采购人
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.tendererName }}
                  </div></td>
                </tr>
                <tr>
                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      联系人
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.tendererContactPerson }}
                  </div></td>

                  <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      联系方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ formData.tenderProject.tendererPhone }}
                  </div></td>
                </tr>
                </tbody>
              </table>
          </div>
        </el-card>
      </el-col>

        <el-col :span="24" class="card-box">
            <el-card>
              <div slot="header">
                <span><i class="el-icon-document"></i>采购内容</span>
              </div>
              <div v-html="formData.tenderProject.projectContent">
              </div>
            </el-card>
          </el-col>

        <el-col :span="24" class="card-box">
            <el-card>
              <div slot="header">
            <span><i class="el-icon-document"></i>投标人资格</span>
          </div>
            <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;table-layout:fixed;">
                <tbody>
                <tr >
                  <td colspan="4"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      是否落实中小微企业等政策
                    </div>
                  </td>
                  <td colspan="18" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.toSme==1?'是':'否' }}
                  </div></td>
                </tr>
                <tr >
                  <td colspan="4"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      特定资格要求
                    </div>
                  </td>
                  <td colspan="18" class="el-table__cell is-leaf"><div class="cell cell-right-border" >

                    {{ formData.tenderProject.bidderQualification }}
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
              <!-- <div slot="header">
                <span><i class="el-icon-document"></i>特定资格要求</span>
              </div>
              <div  v-html="formData.tenderProject.bidderQualification">
              </div> -->
            </el-card>
          </el-col>

        <el-col :span="24" class="card-box">
            <el-card>
              <div slot="header">
                <span><i class="el-icon-document"></i>附件</span>
              </div>
              <div class="el-table el-table--enable-row-hover el-table--medium">
              <table cellspacing="0" style="width: 100%;table-layout:fixed;">
                <tbody>
                  <tr v-for="dict in dict.type.tender_project_attachment" :key="dict.label" >
        <td colspan="2"  class="el-table__cell is-leaf">
                    <div class="cell" >
                      <strong style="color:red;" v-if="dict.raw.isEquals==1">*</strong>{{ dict.label }}
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell cell-right-border">
                      <el-form-item class="upload">
                        <template>
                          <FileUpload
                            :value="getFiles(dict)"
                            :fileType="['pdf', 'doc', 'docx']"
                            :isShowTip="false"
                          ></FileUpload>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                </tbody>
                </table>
                </div>
            </el-card>
          </el-col>
      </el-form>

    <div slot="footer" class="option">
<!--      <el-button type="primary" @click="submitForm">提交</el-button>-->
<!--      <el-button type="primary" @click="submitFormAndCreateNotice">提交并发布公告</el-button>-->
<!--      <el-button @click="resetForm">重置</el-button>-->
      <el-button @click="close">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { listIntention } from "@/api/tender/intention";
import { getProject, getProjectInfo2 } from "@/api/tender/project";
import { listInfo } from "@/api/ent/info";
import { getUserProfile } from "@/api/system/user";
import store from "@/store";

export default {
  components: {},
  dicts: [
    "busi_tender_mode",
    "base_yes_no",
    "busi_project_type",
    "get_bidder_mode",
    "tender_project_attachment",
    "tender_project_fundsource",
  ],
  props: [],
  data() {
    return {
      projectIntentionIdBan: false,
      // 用户信息
      userInfo: null,
      //树形字典
      dataTreeOptionsMap: new Map(),
      dataTreeMap: new Map(),
      cascaderSetting: { value: "id", label: "name", children: "childrens" },
      // 遮罩层
      loading: true,
      intentionList: [],
      formData: {
        tenderIntention:{},
        tenderProject:{}
      },
      rules: {
      },
      projectIntentionIdOptions: [],
      agencyIdOptions: [],
      tendererIdOptions: [],
      // 公告开始时间日期选择的禁用日期
      pickerOptionsOne: {
        disabledDate: (time) => {
          return time.getTime() < new Date() - 8.64e7;
        },
      },
      attachmentsMap: {},
    };
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
    this.getProjectInfo2();
  },
  methods: {
    getFiles(dict) {
        return this.formData.tenderProject.attachmentMap[parseInt(dict.value)]
    },
    getProjectInfo2() {
      //this.$route.params.projectId
      getProjectInfo2(this.$route.params.projectId).then((response) => {
        console.log(response.data)
          this.formData = response.data;
        });
      this.loading = false;
    },
    // 关闭当前页
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.project {
  padding: 0 50px;
}
.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
  margin-bottom: 20px;
}
</style>
<style scoped>

.el-form-item{
  margin-bottom: 0px;
}

/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
