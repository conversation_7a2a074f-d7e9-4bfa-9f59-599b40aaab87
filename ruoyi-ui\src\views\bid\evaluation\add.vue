<template>
  <div
    class="app-container"
    style="margin-top: 20px;"
    v-loading="loading"
  >
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      size="medium"
      label-width="0"
    >
      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>开标情况</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table
              cellspacing="0"
              style="width: 100%;table-layout:fixed;"
            >
              <tbody>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>项目
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="projectId">
                        <el-select
                          v-model="form.projectId"
                          placeholder="请选择项目"
                          filterable
                          clearable
                          :disabled="initProject"
                          ref="selectProject"
                          :style="{ width: '100%' }"
                          @change="change($event)"
                        >
                          <el-option
                            v-for="(item, index) in projectIdOptions"
                            :key="index"
                            :label="item.projectName"
                            :value="item.projectId"
                            :disabled="item.disabled"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>评审开始时间
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="evaluationStartTime">
                        <el-date-picker
                          clearable
                          v-model="form.evaluationStartTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :style="{ width: '100%' }"
                          placeholder="请选择评审开始时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>评审结束时间
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="bidEvaluationEndTime">
                        <el-date-picker
                          clearable
                          v-model="form.bidEvaluationEndTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          :style="{ width: '100%' }"
                          placeholder="请选择评审结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>投标人资格</span>
          </div>
          <div>
            <el-table
              :data="form.projectInfoList"
              style="width: 100%"
              radio-selection="true"
            >
              <el-table-column
                prop="bidderId"
                label="供应商ID"
              ></el-table-column>
              <el-table-column
                prop="bidderName"
                label="供应商名称"
              ></el-table-column>
              <el-table-column
                prop="bidderAmount"
                label="最终报价"
              >
              </el-table-column>
              <el-table-column
                label="评分"
                prop="score"
              >
              </el-table-column>
              <el-table-column
                label="排名"
                prop="ranking"
              >
              </el-table-column>
            </el-table>
            <div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table
              cellspacing="0"
              style="width: 100%;table-layout:fixed;"
            >
              <tbody>
                <tr
                  v-for="dict in dict.type.busi_bid_evaluation_attachment"
                  :key="dict.label"
                >
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong
                        style="color:red;"
                        v-if="dict.raw.isEquals==1"
                      >*</strong>{{ dict.label }}
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell cell-right-border">
                      <el-form-item class="upload">
                        <template>
                          <FileUpload
                            :value="getImgPath(dict)"
                            @input="handleInput(dict, $event)"
                            :fileType="['pdf', 'doc', 'docx']"
                            :isShowTip="false"
                          ></FileUpload>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: center;"
    >
      <el-button
        type="primary"
        @click="submitForm"
        v-show="!isFormDisabled"
      >{{ buttonTitle }}</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import {
  listEvaluation,
  getEvaluation,
  delEvaluation,
  downLoadFile,
  addEvaluation,
  updateEvaluation,
} from "@/api/bid/evaluation";
import { listProject } from "@/api/tender/project";
import { listInfo } from "@/api/bidding/info";
import { getRecord, listRecord } from "@/api/bidding/record";
import { openingProject } from "@/api/bid/opening";
import { getDicts } from "@/api/system/dict/data";
//引入download，这是若依框架自带的，在utils/request里面封装好的方法
import { download } from "@/utils/request";
import { selectByProject } from "@/api/evaluation/expertNodeInfo";
export default {
  name: "Evaluation",
  dicts: ["base_yes_no", "busi_bid_evaluation_attachment"],
  data() {
    return {
      initProject: false,
      isFormDisabled: true,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评标记录表格数据
      evaluationList: [],
      attachmentTypes: [],
      attachmentMap: {},
      projectIdOptions: [],

      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 0正常 1删除时间范围
      daterangeEvaluationStartTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeBidEvaluationEndTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCreateTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeUpdateTime: [],

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bidEvaluationId: null,
        projectId: null,
        evaluationStartTime: null,
        bidEvaluationEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      },
      // 表单参数
      form: {
        attachments: [],
        projectInfoList: [],
        attachmentMap: {},
        abortiveTender: 0,
      },
      // 表单校验
      rules: {
        projectId: [
          { required: false, message: "项目id不能为空", trigger: "blur" },
        ],
      },
      buttonTitle: "确 定",
    };
  },
  created() {
    this.checkBiddingId();
    this.getList();
    this.getAttachmentTypes();
  },
  methods: {
    winBidder(val) {
      var biddingInfoList = this.form.projectInfoList;
      for (var index in biddingInfoList) {
        if (biddingInfoList[index].bidderId == val) {
          biddingInfoList[index].isWin = 1;
        } else {
          biddingInfoList[index].isWin = 0;
          biddingInfoList[index].isWinCheckBox = false;
        }
      }
    },
    abandonedBidCheck() {
      let normalBidderNum = 0;
      var biddingInfoList = this.form.projectInfoList;
      for (var index in biddingInfoList) {
        if (biddingInfoList[index].isAbandonedBid == 0) {
          normalBidderNum++;
        }
      }
      if (normalBidderNum < 3) {
        this.form.abortiveTender = 1;
        this.buttonTitle = "流 标";
      } else {
        this.form.abortiveTender = 0;
        this.buttonTitle = "确 定";
      }
    },
    checkBiddingId() {
      const bidEvaluationId = this.$route.params.bidEvaluationId;
      this.isFormDisabled =
        this.$route.params.isFormDisabled == "true" ? true : false;
      if (this.isFormDisabled) {
        this.$route.meta.title = "查看评审记录";
      } else {
        this.$route.meta.title = "修改评审记录";
      }
      if (bidEvaluationId == 0) {
        this.$route.meta.title = "新增评审记录";
      }
      if (bidEvaluationId) {
        // intentionId 存在，可以进行后续操作，比如加载详情
        this.loadEvaluationDetail(bidEvaluationId);
      } else {
        // intentionId 不存在，可能是新增操作
        //.initNewIntention();
      }
    },
    loadEvaluationDetail(bidEvaluationId) {
      // 加载已有意图详情的逻辑
      getEvaluation(bidEvaluationId).then((response) => {
        this.form = response.data;
        // 使用reduce方法将attachments数组收集成一个映射
        this.attachmentMap = response.data.attachments.reduce(
          (accumulator, attachment) => {
            // 如果accumulator中还没有这个fileType的键，则创建一个新数组
            if (!accumulator[attachment.fileType]) {
              accumulator[attachment.fileType] = [];
            }
            // 将当前attachment添加到对应fileType的数组中
            accumulator[attachment.fileType].push(attachment);
            return accumulator;
          },
          {}
        );
      });
    },
    handleInput(dict, data) {
      //新增操作
      this.form.attachmentMap[parseInt(dict.value)] = data;
    },
    getImgPath(dict) {
      return this.form.attachmentMap[parseInt(dict.value)];
    },
    downLoadFile(id, value) {
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length - 1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId: id,
        fileName: fileName,
        filePath: value,
        resource: value,
      };
      downLoadFile(params).then((response) => {
        if (response["code"] == 200) {
          download("/common/download/resource", params, fileName);
        }
      });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
      /*
      /!** request里面的download下载函数 *!/
      //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
      console.log("文件名");*/
    },
    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50;
      if (!isRightSize) {
        this.$message.error("文件大小超过 50MB");
      }
      let isAccept = new RegExp(".pdf").test(file.type);
      if (!isAccept) {
        this.$message.error("应该选择.pdf类型的文件");
      }
      return isRightSize && isAccept;
    },
    getAttachmentTypes() {
      getDicts("busi_bid_evaluation_attachment").then((result) => {
        console.info(result);
        if (result.code == 200) {
          console.info("attachment result" + result);
          this.attachmentTypes = result.data;
        }
      });
    },
    handleWinChange(row) {
      // 在这里处理单选按钮变化时的逻辑
      console.log("中标状态改变:", row);
      // 你可以调用API更新数据库中的中标状态
    },
    change(value) {
      this.queryParams.params = { isScope: 1 };
      this.queryParams.projectId = value;
      console.log("projectId:" + value);

      selectByProject(this.queryParams.projectId).then(result => {
            if(result.code==200){
                this.form.evaluationStartTime = result.data.evaluationTime;
                this.form.bidEvaluationEndTime = result.data.evaluationEndTime;
            }
          })
      listInfo(this.queryParams).then((response) => {
        this.form.projectInfoList = response.rows;
      });
    },
    /** 查询评标记录列表 */
    getList() {
      this.loading = true;
      // 获取项目
      listProject({ projectStatus: 40 }).then((response) => {
        this.projectIdOptions = response.rows;
        if (this.$route.query.projectId) {
          this.form.projectId = parseInt(this.$route.query.projectId);
          this.$refs.selectProject.$emit("change", this.form.projectId);
          this.initProject = true;
          selectByProject(this.form.projectId).then(result => {
            if(result.code==200){
                this.form.evaluationStartTime = result.data.evaluationTime;
                this.form.bidEvaluationEndTime = result.data.evaluationEndTime;
            }
          })
        }
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.$tab.closePage();
    },
    // 表单重置
    reset() {
      this.form = {
        bidEvaluationId: null,
        projectId: null,
        evaluationStartTime: null,
        bidEvaluationEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeEvaluationStartTime = [];
      this.daterangeBidEvaluationEndTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 提交按钮 */
    submitForm() {
      console.info("111111111");
      console.info(this.form);
      for (let item of this.dict.type.busi_bid_evaluation_attachment) {
        console.info(item);
        console.info(this.form.attachmentMap[item.value]);
        if (
          item.raw.isEquals == 1 &&
          this.form.attachmentMap[item.value] == undefined
        ) {
          this.$message.error(item.label + " 文件不能为空");
          return;
        }
      }
      console.info("22222222222222");
      this.$refs["form"].validate((valid) => {
        console.info(valid);
        if (valid) {
          // 将表格数据添加到表单数据中
          //this.form.projectInfoList = this.projectInfoList;
          // this.form.attachments = [].concat(
          //   ...Object.values(this.form.attachmentMap)
          // );
          console.info(this.form);
          if (this.form.bidEvaluationId != null) {
            updateEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.$tab.closePage();
              this.getList();
            });
          } else {
            addEvaluation(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.$tab.closePage();
              this.getList();
            });
          }
        }
      });
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 0px;
}
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
