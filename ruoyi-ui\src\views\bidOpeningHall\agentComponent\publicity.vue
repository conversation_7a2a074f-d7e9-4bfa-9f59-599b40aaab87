<!-- 代理人投标人公示 -->
<template>
  <div
    class="publicity"
    v-loading="loading"
  >
    <div class="publicity-line-two">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :header-cell-style="headStyle"
        :cell-style="cellStyle"
      >
        <el-table-column
          type="index"
          label="序号"
          width="100"
        >
        </el-table-column>
        <el-table-column
          prop="bidderName"
          label="投标供应商"
        >
        </el-table-column>
        <el-table-column
          prop="signInStatus"
          label="签到状态"
          width="150"
        >
          <template slot-scope="scope">
            <div v-if="scope.row.signInStatus == 1">已签到</div>
            <div
              v-else
              style="color:#176ADB"
            >未签到</div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="publicity-line-two">
      <el-button
        v-if="show && !failureOfBid"
        class="publicity-button"
        @click="biddersAnnouncement"
      >投标人公示</el-button>
      <el-button
        style="background-color:#db1717"
        v-if=" show && failureOfBid"
        class="publicity-button"
        @click="failureBid"
      >流标</el-button>
    </div>

  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { dataList, operationRecord } from "@/api/onlineBidOpening/info";
import { failureOfBid as failure } from "@/api/bid/opening";
import { formatDateOption } from "@/utils/index";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      loading: true,
      tableData: [],
      headStyle: {
        "text-align": "center",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
      },
      cellStyle: {
        "text-align": "center",
        height: "90px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
      show: false,
      failureOfBid: false,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 初始化投标人公示+标书解密列表
    initdataList() {
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.tableData = response.data;
          this.tableData.map((item) => {
            if (item.signInStatus == 0) {
              this.failureOfBid = true;
            }
          });
          this.loading = false;
        }
      });
    },
    // 投标人公示
    biddersAnnouncement() {
      // 记录操作
      operationRecord({
        projectId: this.$route.query.projectId,
        operationType: 2,
        operationTime: formatDateOption(new Date()),
        decryptionTime: "",
      }).then((response) => {
        if (response.code == 200) {
          // 初始化标书解密列表
          this.$emit("sendMessage", "bidPublicity");
        } else {
          this.$modal.msgwarning(msg);
        }
      });
    },
    // 按钮是否显示
    buttonShow() {
      this.$store.getters.agentBidOpenStatus;
      if (this.$store.getters.agentBidOpenStatus == 2) {
        this.show = true;
      }
    },
    // 流标操作
    failureBid() {
      const data = {
        projectId: this.$route.query.projectId,
        abortiveType: 2,
      };
      this.$modal
        .confirm("是否进行流标操作？")
        .then(() => {
          failure(data).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess("流标成功");
              this.$emit("sendMessage", "flowLabel");
            }
          });
        })
        .catch(() => {});
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.initdataList();
    this.buttonShow();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.publicity {
  padding: 30px 25px;
  .publicity-line-one {
  }

  .publicity-line-two {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 40px;
    .publicity-button {
      width: 164px;
      height: 45px;
      background: #176adb;
      border-radius: 5px;

      color: #fff;
      font-weight: 700;
    }
  }
}
</style>