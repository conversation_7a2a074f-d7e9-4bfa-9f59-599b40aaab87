import request from '@/utils/request'

// 查询采购文件编制基础信息列表
export function listInfo(query) {
  return request({
    url: '/documents/info/list',
    method: 'get',
    params: query
  })
}

// 查询采购文件编制基础信息列表
export function infoByParams(query) {
  return request({
    url: '/documents/info/infoByParams',
    method: 'get',
    params: query
  })
}


// 查询采购文件编制基础信息详细
export function getInfo(projectFileId) {
  return request({
    url: '/documents/info/' + projectFileId,
    method: 'get'
  })
}


// 新增采购文件编制基础信息
export function addInfo(data) {
  return request({
    url: '/documents/info',
    method: 'post',
    data: data
  })
}

// 修改采购文件编制基础信息
export function updateInfo(data) {
  return request({
    url: '/documents/info',
    method: 'put',
    data: data
  })
}

// 删除采购文件编制基础信息
export function delInfo(projectFileId) {
  return request({
    url: '/documents/info/' + projectFileId,
    method: 'delete'
  })
}
