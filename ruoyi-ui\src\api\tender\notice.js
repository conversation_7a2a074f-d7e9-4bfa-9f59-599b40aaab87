import request from '@/utils/request'

// 查询采购公告信息列表
export function listNotice(query) {
  return request({
    url: '/tender/notice/list',
    method: 'get',
    params: query
  })
}

// 查询采购公告信息详细
export function getNotice(noticeId) {
  return request({
    url: '/tender/notice/' + noticeId,
    method: 'get'
  })
}

// 新增采购公告信息
export function addNotice(data) {
  return request({
    url: '/tender/notice',
    method: 'post',
    data: data
  })
}

// 修改采购公告信息
export function updateNotice(data) {
  return request({
    url: '/tender/notice',
    method: 'put',
    data: data
  })
}

// 删除采购公告信息
export function delNotice(noticeId) {
  return request({
    url: '/tender/notice/' + noticeId,
    method: 'delete'
  })
}
// 发布、暂存采购公告
export function updateAnnouncementInfo(data) {
  return request({
    url: '/tender/notice/',
    method: 'post',
    data: data
  })
}

// 获取投标人数据
export function getBidderListInfo(query) {
  return request({
    url: '/ent/info/list',
    method: 'get',
    params: query
  })
}
// 获取变更公告
export function getChangeNoticeInfo(noticeId, type) {
  return request({
    url: '/tender/notice/getChangeNoticeInfo/'+noticeId+"/"+type,
    method: 'get'
  })
}
// 发布变更采购公告
export function changeAnnouncementInfo(data) {
  return request({
    url: '/tender/notice/changeAnnouncementInfo',
    method: 'post',
    data: data
  })
}
// 查询首页用采购公告
export function noticeView(tenderNoticeId) {
  return request({
    url: '/tender/notice/noticeView?tenderNoticeId='+tenderNoticeId,
    method: 'get'
  })
}

// 查询首页用采购公告
export function getTenderNoticeForMain(tenderNoticeId) {
  return request({
    url: '/tender/notice/getTenderNoticeForMain?tenderNoticeId='+tenderNoticeId,
    method: 'get'
  })
}

// 报名并下载采购文件
export function downloadTenderFile(data) {
  return request({
    url: '/tender/notice/downloadTenderFile?projectId='+data,
    method: 'get',
    responseType: 'blob'
  })
}

export function getNoticeTypes(projectId) {
  return request({
    url: '/tender/notice/getNoticeTypes?projectId='+projectId,
    method: 'get'
  })
}


export function getTenderNoticeByProject(projectId) {
  return request({
    url: '/tender/notice/getTenderNoticeByProject?projectId='+projectId,
    method: 'get'
  })
}
