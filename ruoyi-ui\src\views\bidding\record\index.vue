<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectId">
<!--        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目"
          clearable
          @keyup.enter.native="handleQuery"
        />-->
        <el-select
          v-model="queryParams.projectId"
          placeholder="请选择项目"
          filterable
          clearable
          :style="{ width: '100%' }"
          @change="change($event)"
        >
          <el-option
            v-for="(item, index) in projectIdOptions"
            :key="index"
            :label="item.projectName"
            :value="item.projectId"
            :disabled="item.disabled"
          ></el-option>
        </el-select>

      </el-form-item>
<!--      <el-form-item label="投标人id" prop="bidderId">
        <el-input
          v-model="queryParams.bidderId"
          placeholder="请输入投标人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人名称" prop="bidderName">
        <el-input
          v-model="queryParams.bidderName"
          placeholder="请输入投标人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人代码" prop="bidderCode">
        <el-input
          v-model="queryParams.bidderCode"
          placeholder="请输入投标人代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传ip" prop="uploadIp">
        <el-input
          v-model="queryParams.uploadIp"
          placeholder="请输入上传ip"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="上传时间">
        <el-date-picker
          v-model="daterangeUploadTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="取消时间">
        <el-date-picker
          v-model="daterangeCancelTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="删除标记 0正常 1删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记 0正常 1删除" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间">
        <el-date-picker
          v-model="daterangeUpdateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bidding:record:add']"
        >新增</el-button>
      </el-col>
<!--      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bidding:record:edit']"
        >修改</el-button>
      </el-col>-->
      <el-col :span="1.5">
        <!-- <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bidding:record:remove']"
        >删除</el-button> -->
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['bidding:record:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="recordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="投标记录id" align="center" prop="biddingId" />
      <el-table-column label="项目名称" align="center" prop="project.projectName" />
      <el-table-column label="投标人id" align="center" prop="bidderId" />
      <el-table-column label="投标人名称" align="center" prop="bidderName" />
      <el-table-column label="投标报价" align="center" prop="bidAmount" />
<!--      <el-table-column label="报价单位" align="center" prop="priceUnit" />-->
      <el-table-column label="报价单位 " align="center" prop="priceUnit">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.price_unit"
            :value="scope.row.priceUnit"
          />
        </template>
      </el-table-column>
      <el-table-column label="上传ip" align="center" prop="uploadIp" />
      <el-table-column label="上传时间" align="center" prop="uploadTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.uploadTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['bidding:record:query']"
          >查看详情</el-button>
<!--          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bidding:record:edit']"
          >修改</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="cancelInfo(scope.row)"
            v-hasPermi="['bidding:record:edit']"
          >撤回</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bidding:record:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改投标记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectId">
<!--          <el-input v-model="form.projectId" placeholder="请输入项目" />-->
          <el-select
            v-model="form.projectId"
            placeholder="请选择项目"
            filterable
            clearable
            :style="{ width: '100%' }"
            @change="change($event)"
          >
            <el-option
              v-for="(item, index) in projectIdOptions"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
<!--        <el-form-item label-width="120px" :label="item.dictLabel"
                      v-for="(item,index) in attachmentTypes" :key="index" >
          <template>
            <FileUpload
              :value="attachmentMap[item.dictValue]"
              @input="handleInput(item.dictValue, $event)"
              :fileType="['pdf', 'doc', 'docx']"
              :isShowTip="false"
              :showOnly="false"
            ></FileUpload>
          </template>
        </el-form-item>-->
        <el-form-item label="投标报价" prop="bidAmount">
          <!--          <el-input v-model="form.bidAmount" placeholder="请输入投标报价" />-->
          <el-input-number
            v-model="form.bidAmount"
            :min="0.1"
            :step="0.01"
            :precision="2"
            step-strictly
            placeholder="请输入预算金额"
            :style="{ width: '20%' }"
          ></el-input-number>
        </el-form-item>

        <el-form-item
          v-for="dict in dict.type.busi_bidding_record_attachment"
          :key="dict.label"
          label-width="120px"
          :label="dict.label"
          class="upload"
        >
          <template>
            <FileUpload
              :value="getImgPath(dict)"
              @input="handleInput(dict, $event)"
              :fileType="['pdf', 'doc', 'docx']"
              :isShowTip="false"
            ></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listRecord, getRecord, delRecord, addRecord, updateRecord, getCancelInfoRecord} from "@/api/bidding/record";
import {listProject} from "@/api/tender/project";
import {downLoadFile} from "@/api/bid/evaluation";
import {download} from "@/utils/request";
import {getDicts} from "@/api/system/dict/data";

export default {
  name: "Record",
  dicts: ['base_yes_no','price_unit','busi_bidding_record_attachment'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 投标记录表格数据
      recordList: [],
      evaluationList: [],
      attachmentTypes: [],
      attachmentsMap: {},
      projectIdOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 0正常 1删除时间范围
      daterangeUploadTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCancelTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCreateTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        uploadIp: null,
        uploadTime: null,
        cancelTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {
        attachments:[]
      },
      rules: {
        projectId: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
       /* bidderId: [
          { required: true, message: "投标人id不能为空", trigger: "blur" }
        ],
        bidderName: [
          { required: true, message: "投标人名称不能为空", trigger: "blur" }
        ],
        bidderCode: [
          { required: true, message: "投标人代码不能为空", trigger: "blur" }
        ],
        uploadIp: [
          { required: true, message: "上传ip不能为空", trigger: "blur" }
        ],
        uploadTime: [
          { required: true, message: "上传时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标记 0正常 1删除不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],*/
      }
    };
  },
  created() {
    this.getAttachmentTypes();
  },
  mounted() {
    this.getList();
  },
  methods: {
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
      console.log(this.attachmentsMap);
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    change(value) {
      this.queryParams.params = {};
      this.queryParams.projectId = value;
      console.log("projectId:"+value);

      listRecord(this.queryParams).then(response => {
        this.projectInfoList = response.rows;
        console.log(123);
      });

      // listInfo(this.queryParams).then(response => {
      //   console.log(response);
      //
      //   this.projectInfoList = response.rows;
      //
      // });
    },
    downLoadFile(id,value){
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length-1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId:id,
        fileName:fileName,
        filePath:value,
        resource:value
      }
      downLoadFile(params).then(response => {
        if (response["code"]==200){
          download("/common/download/resource", params, fileName);
        }
      });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
      /*
        /!** request里面的download下载函数 *!/
        //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
        console.log("文件名");*/
    },

    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },

    getAttachmentTypes(){
      getDicts("busi_bidding_record_attachment").then((result) => {
        console.info(result);
        if(result.code==200){
          this.attachmentTypes = result.data;
        }
      });
    },

    /** 查询投标记录列表 */
    getList() {
      this.loading = true;

      this.queryParams.params = {};
      if (null != this.daterangeUploadTime && '' != this.daterangeUploadTime) {
        this.queryParams.params["beginUploadTime"] = this.daterangeUploadTime[0];
        this.queryParams.params["endUploadTime"] = this.daterangeUploadTime[1];
      }
      if (null != this.daterangeCancelTime && '' != this.daterangeCancelTime) {
        this.queryParams.params["beginCancelTime"] = this.daterangeCancelTime[0];
        this.queryParams.params["endCancelTime"] = this.daterangeCancelTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listRecord(this.queryParams).then(response => {
        this.recordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      if(this.$route.query.projectId){
        // 1、打开弹窗
        this.open = true;
        // 2、给弹窗中的项目赋值
        this.form.projectId = this.$route.query.projectId;
      }
      listProject(this.queryParams).then((response) => {
        this.projectIdOptions = response.rows;
        this.loading = false;
      });


    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        biddingId: null,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        uploadIp: null,
        uploadTime: null,
        cancelTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null

      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeUploadTime = [];
      this.daterangeCancelTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.biddingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {

      this.reset();
      // this.open = true;
      // this.title = "添加投标记录";
      this.$router.push('/bidding/record/add/0')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const biddingId = row.biddingId || this.ids
      this.$router.push('/bidding/record/add/' + biddingId+ '/false');
      // getRecord(biddingId).then(response => {
      //   this.form = response.data;
      //   // 使用reduce方法将attachments数组收集成一个映射
      //   this.attachmentsMap = response.data.attachments.reduce(
      //     (accumulator, attachment) => {
      //       // 如果accumulator中还没有这个fileType的键，则创建一个新数组
      //       if (!accumulator[attachment.fileType]) {
      //         accumulator[attachment.fileType] = [];
      //       }
      //       // 将当前attachment添加到对应fileType的数组中
      //       accumulator[attachment.fileType].push(attachment);
      //       return accumulator;
      //     },
      //     {}
      //   );
      //   this.projectInfoList = [];
      //   this.open = true;
      //   this.title = "修改投标记录";
      // });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const biddingId = row.biddingId || this.ids
      this.$router.push('/bidding/record/add/' + biddingId+ '/true');
    },
    /*撤回*/
    cancelInfo(row) {
      const biddingId = row.biddingId || this.ids
      getCancelInfoRecord(biddingId).then(response => {
       // this.form = response.data;
        this.$modal.msgSuccess("撤回成功");
        this.open = false;
        this.getList();
      });
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
         /* var al = [];
          for(var key in this.attachmentMap){
            if(this.attachmentMap[key] != ''){
              var all = this.attachmentMap[key].split(',');
              all.forEach(element => {
                al.push({"fileType":key, "filePath":element});
              });
            }
          }
          this.form.attachments= al;*/
          this.form.attachments = [].concat(
            ...Object.values(this.attachmentsMap)
          );
          if (this.form.biddingId != null) {
            updateRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const biddingIds = row.biddingId || this.ids;
      this.$modal.confirm('是否确认删除投标记录编号为"' + biddingIds + '"的数据项？').then(function() {
        return delRecord(biddingIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bidding/record/export', {
        ...this.queryParams
      }, `record_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
