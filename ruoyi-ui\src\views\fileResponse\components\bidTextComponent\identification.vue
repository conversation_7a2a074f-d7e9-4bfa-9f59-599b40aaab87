<!-- 法定代表人身份证明 -->
<template>
  <div class=''>
    <div style="text-align:right"><el-button class="item-button">保存</el-button></div>
    <el-row>
      <div>
        <el-col
          :offset="4"
          :span="3"
        >法定代表人姓名：</el-col>
        <el-col :span="8">
          <el-input
            v-model="form.legalPersonName"
            placeholder="请输入内容"
          ></el-input>
        </el-col>
      </div>
    </el-row>

    <el-row>
      <div>
        <el-col
          :offset="4"
          :span="3"
        >性别：</el-col>
        <el-col :span="8">
          <el-input
            v-model="form.legalPersonSex"
            placeholder="请输入内容"
          ></el-input>
        </el-col>
      </div>
    </el-row>
    <el-row>
      <div>
        <el-col
          :offset="4"
          :span="3"
        >年龄：</el-col>
        <el-col :span="8">
          <el-input
            v-model="form.legalPersonAge"
            placeholder="请输入内容"
          ></el-input>
        </el-col>
      </div>
    </el-row>
    <el-row>
      <div>
        <el-col
          :offset="4"
          :span="3"
        >职务：</el-col>
        <el-col :span="8">
          <el-input
            v-model="form.legalPergonPosition"
            placeholder="请输入内容"
          ></el-input>
        </el-col>
      </div>
    </el-row>

    <el-row>
      <div>
        <el-col
          :offset="4"
          :span="3"
        >身份证（正反面）：</el-col>
        <el-col :span="8">
          <image-upload
            :limit="9999"
            v-model="pdfUrl"
          />
        </el-col>
      </div>
    </el-row>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      form: {
        legalPersonName: "",
        legalPersonSexi: "",
        legalPersomAgej: "",
        legalPergonPosition: "",
      },
      pdfUrl: "",
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.item-button {
  border: #333 1px solid;
  width: 88px;
  height: 32px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1);
  color: #fff;
  &:hover {
    color: #fff;
  }
}
/*@import url()*/
</style>