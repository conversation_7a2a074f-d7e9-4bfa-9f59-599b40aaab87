import request from '@/utils/request'

// 查询取消采购项目列表
export function listProject(query) {
  return request({
    url: '/cancel/project/list',
    method: 'get',
    params: query
  })
}
export function listTenderProject(query) {
  return request({
    url: '/tender/project/list',
    method: 'get',
    params: query
  })
}

// 查询取消采购项目详细
export function getProject(cancelId) {
  return request({
    url: '/cancel/project/' + cancelId,
    method: 'get'
  })
}

// 新增取消采购项目
export function addProject(data) {
  return request({
    url: '/cancel/project',
    method: 'post',
    data: data
  })
}
export function approveRequest(data) {
  return request({
    url: '/cancel/project/approveRequest',
    method: 'post',
    data: data
  })
}
// 修改取消采购项目
export function updateProject(data) {
  return request({
    url: '/cancel/project',
    method: 'put',
    data: data
  })
}

// 删除取消采购项目
export function delProject(cancelId) {
  return request({
    url: '/cancel/project/' + cancelId,
    method: 'delete'
  })
}
