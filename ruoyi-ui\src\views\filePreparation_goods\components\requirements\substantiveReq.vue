<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
          <el-input
            type="textarea"
            :rows="25"
            placeholder="1、智能安检门须符合国家标准《通过式金属探测门通用技术规范》（GB 15210-2018），对心脏起搏器佩戴者、孕妇、磁性介质等无害；
2、安检门本身应具有操控面板，可直接操控调节门的所有功能参数，配置≥7英寸液晶触摸功能屏，≥10英寸液晶显示屏，分别用于考生通过显示和老师现场监控。能够显示通过人数、报警人数、报警物品类别、报警物品所在人体虚拟区位等；
3、安检门应能实现现场维护与设置，现场使用人员可自行通过液晶触摸功能屏对安检门进行参数的设置和调试；
4、安检门应至少具备电子产品探测模式。在电子产品探测模式下，安检门应能对正常着装上的金属纽扣、项链、打火机、钥匙、硬币、皮带扣等日常小金属物品不报警，当探测到手机、移动硬盘、摄像机、电脑等电子产品时，应实时报警，且当手机等电子产品藏匿于身体的任何部位，安检门都可监测到，同时发出声光报警及以图形和文字的形式提示报警物品类别、藏匿位置等，并可在液晶显示屏上显示手机藏匿于人体正面或背面，并显示手机藏匿时的姿态；
5、安检门应同时具有可通过网络实现远程电脑连接功能，实现门体与远程监控电脑双屏报警监控。"
            v-model="requirement"
          >
          </el-input>
          <div>
            <p
              class="text"
              style="text-indent:0"
            >注：</p>
            <p class="text">1、上述技术参数不得出现负偏离，否则，其响应无效；</p>
            <p class="text">2、供应商就上述技术参数应在“技术偏差表”中如实做出响应，并对响应内容的真实性负责。</p>
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      itemContent: {
        paramReq:{
          requirement:""
        },
        quantityParamReq:{
          requirement:""
        },
        substantiveReq:{
          requirement:""
        }
      },
      requirement: "",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        this.requirement = this.itemContent.substantiveReq.requirement;
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      console.log("this.requirement.", this.requirement);
      this.itemContent.substantiveReq.requirement = this.requirement;
      console.log("this.itemContent.", this.itemContent);
      let newContent = JSON.stringify(this.itemContent);
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newContent,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
.text {
  color: rgba(255, 87, 51, 1);
  font-size: 16px;
  line-height: 100%;
  text-align: left;
  text-indent: 2em;
}
</style>
