<template>
  <div>
    <el-card class="box-card" v-if="show">
          <div
            slot="header"
            class="clearfix"
            style="display: flex; justify-content: space-between"
          >
            <div><span>签章</span></div>
            <div>
              <el-button
                type="primary"
                size="mini"
                @click="reloadPage(ControlType.Seal)"
                >重置企业印章</el-button
              >
              <el-button
                type="primary"
                size="mini"
                @click="reloadPage(ControlType.legalPerson)"
                >重置法人印章</el-button
              >
            </div>
          </div>
          <div class="block">
            <div
              class="signer-list"
              style="display: flex; justify-content: center"
            >
              <ul>
                <div v-for="(element, index) in thisControlList" :key="index">
                  <li
                    class="unmover li-style"
                    style="text-align: center"
                    v-if="element.type == ControlType.Seal"
                  >
                    企业印章
                  </li>
                  <li
                    class="unmover li-style"
                    style="text-align: center"
                    v-if="element.type == ControlType.legalPerson"
                  >
                    法人印章
                  </li>
                  <!-- <li
                        class="unmover li-style"
                        v-if="element.type== ControlType.Signature"
                      >手写签名-{{element.user.userName?element.user.userName:'未手写签名'}}</li> -->
                  <li :class="['li-entp-seal']">
                    <div
                      class="entp-seal item"
                      v-if="element.type == ControlType.Seal"
                      @click="openModal(element)"
                    >
                      <img
                        :src="'data:image/png;base64,' + element.value"
                        v-if="element.value"
                      />
                      <span v-else>请先制作印章</span>
                    </div>
                    <div
                      class="entp-seal item"
                      v-if="element.type == ControlType.legalPerson"
                      @click="openModal(element)"
                    >
                      <img
                        :src="'data:image/png;base64,' + element.value"
                        v-if="element.value"
                      />
                      <span v-else>请先制作印章</span>
                    </div>
                    <!-- <div
                          class="person-seal item"
                          v-if="element.type == ControlType.Signature"
                          @click="openModal(element)"
                        >
                          <img
                            :src="'data:image/png;base64,'+element.value"
                            v-if="element.value"
                          />
                          <span v-else>请先设置手写签名</span>
                        </div> -->
                  </li>
                </div>
              </ul>
            </div>
            <Seal
              :seal-modal-show.sync="sealModalShow"
              @success="sealModalSubmit"
              :entId="form.entId"
              :type="type"
            ></Seal>
            <Signature
              :signature-modal-show.sync="signatureModalShow"
              @success="signatureModalSubmit"
            ></Signature>
          </div>
        </el-card>
      </el-col>
  <el-divider></el-divider>
  <!-- <el-form ref="form2" :model="user" :rules="rules" label-width="80px">
    <el-form-item label="旧密码" prop="oldPassword">
      <el-input v-model="user.oldPassword" placeholder="请输入旧密码" type="password" show-password/>
    </el-form-item>
    <el-form-item label="新密码" prop="newPassword">
      <el-input v-model="user.newPassword" placeholder="请输入新密码" type="password" show-password/>
    </el-form-item>
    <el-form-item label="确认密码" prop="confirmPassword">
      <el-input v-model="user.confirmPassword" placeholder="请确认新密码" type="password" show-password/>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" size="mini" @click="submit">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form> -->
</div>
</template>

<script>
import { updateUserPwd, getUserProfile } from "@/api/system/user";
import { getInfo, updateInfo, updateAndAudit } from "@/api/ent/info";
import Seal from "@/components/Seal";
import Signature from "@/components/Signature";
import request from "@/utils/request";
import {
  CanvasZoom,
  controlList,
  ControlType,
} from "@/components/control/data/ControlData";

export default {
  components: { Seal, Signature },
  data() {
    const equalToPassword = (rule, value, callback) => {
      if (this.user.newPassword !== value) {
        callback(new Error("两次输入的密码不一致"));
      } else {
        callback();
      }
    };
    return {
      show: true,
      thisControlList: JSON.parse(JSON.stringify(controlList)),
      user: {
        oldPassword: undefined,
        newPassword: undefined,
        confirmPassword: undefined
      },
      // 表单校验
      rules: {
        oldPassword: [
          { required: true, message: "旧密码不能为空", trigger: "blur" }
        ],
        newPassword: [
          { required: true, message: "新密码不能为空", trigger: "blur" },
          { min: 6, max: 20, message: "长度在 6 到 20 个字符", trigger: "blur" },
          { pattern: /^[^<>"'|\\]+$/, message: "不能包含非法字符：< > \" ' \\\ |", trigger: "blur" }
        ],
        confirmPassword: [
          { required: true, message: "确认密码不能为空", trigger: "blur" },
          { required: true, validator: equalToPassword, trigger: "blur" }
        ]
      },
      ControlType: ControlType,
      type: "",
      sealModalShow: false,
      signatureModalShow: false,
      form: {},
      //签署相关的属性
      signData: {
        signType: 1,
        entKeyword: "",
        personalKeyword: "",
        entName: "",
        legalName: "",
        personalName: "",
        entSeal: "",
        legalSeal: "",
        personalSeal: "",
        entPositionList: [],
        personalPositionList: [],
      },
      //文档展示的数据  包含控件列表
      documentPDF: {
        images: [
          {
            docPage: 0,
            image: "",
          },
          {
            docPage: 1,
            image: "",
          },
        ],
        control: [],
      },
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    submit() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateUserPwd(this.user.oldPassword, this.user.newPassword).then(response => {
            this.$modal.msgSuccess("修改成功");
          });
        }
      });
    },
    close() {
      this.$tab.closePage();
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
        if (!response.data.admin) {
          getInfo(response.data.entId).then((response) => {
            this.thisControlList.map((element) => {
              if (element.type == ControlType.Seal) {
                element.value = response.data.enterpriseSignature;
              } else if (element.type == ControlType.legalPerson) {
                element.value = response.data.legalRepreSignature;
              }
            });
            this.form = response.data;
            this.operation = [
              {
                timestamp: this.form.createTime,
                content: "账号注册成功，请完善信息并提交审核。",
              },
            ];
            this.$nextTick(() => {
              window.scrollTo(0, 0); // 确保页面滚动到顶部
            });
          });
          this.show = true;
        }
      });
    },
    reloadPage(type) {
      this.signData = {
        signType: 1,
        entKeyword: "",
        personalKeyword: "",
        entName: "",
        personalName: "",
        entSeal: "",
        personalSeal: "",
        entPositionList: [],
        personalPositionList: [],
      };
      if (type == ControlType.Seal) {
        this.thisControlList[0].value = "";
        this.thisControlList[0].user.userName = "";
        // this.$refs.entInfo.resetForm('enterpriseSignature','');
      this.resetSeal('enterpriseSignature');
      } else if (type == ControlType.legalPerson) {
        this.thisControlList[1].value = "";
        this.thisControlList[1].user.userName = "";
        // this.$refs.entInfo.resetForm('legalRepreSignature','');
        this.resetSeal('legalRepreSignature');
      }
      this.documentPDF.control = [];
    },
    resetSeal(type) {
      const response = request({
        url: "/kaifangqian/clip/resetSeal",
        method: "post",
        data: {
          entId: this.form.entId,
          type: type,
          opUser: this.form.entName
        },
      });
      console.log(response);
    },
    openModal(element) {
      if (element.type == this.ControlType.Seal && !element.value) {
        //signData.value.entPositionList.push(temItem)
        this.type = this.ControlType.Seal;
        this.sealModalShow = true;
      } else if (
        element.type == this.ControlType.legalPerson &&
        !element.value
      ) {
        this.type = this.ControlType.legalPerson;
        this.sealModalShow = true;
      } else if (element.type == this.ControlType.Signature && !element.value) {
        //signData.value.personalPositionList.push(temItem)
        this.signatureModalShow = true;
      }
    },
    /**
     * 接受签章图片
     */
    sealModalSubmit(data) {
      var temControlList = JSON.parse(JSON.stringify(this.thisControlList));
      if (data.type == ControlType.Seal) {
        temControlList[0].value = data.sealImage;
        temControlList[0].user.userName = data.entpName;
        this.signData.entName = data.entpName;
        this.signData.entSeal = data.sealImage;
      } else if (data.type == ControlType.legalPerson) {
        temControlList[1].value = data.sealImage;
        temControlList[1].user.userName = data.entpName;
        this.signData.legalName = data.entpName;
        this.signData.legalSeal = data.sealImage;
      }

      this.signData.entName = data.entpName;
      this.signData.entSeal = data.sealImage;
      this.thisControlList = temControlList;

      //替换控件中已经使用的签章
      this.documentPDF.control.forEach((item) => {
        if (item.type == this.ControlType.Seal) {
          item.value = data.sealImage;
        } else if (item.type == this.ControlType.legalPerson) {
          item.value = data.sealImage;
        }
      });
    },
    /**
     * 接收手写签名图片
     */
    signatureModalSubmit(data) {
      var temControlList = JSON.parse(JSON.stringify(this.thisControlList));
      temControlList[1].value = data.image;
      temControlList[1].user.userName = data.userName;
      this.signData.personalName = data.userName;
      this.signData.personalSeal = data.image;
      this.thisControlList = temControlList;
      //替换控件中已经使用的手写签名
      this.documentPDF.control.forEach((item) => {
        if (item.type == this.ControlType.Signature) {
          item.value = data.image;
        }
      });
    },
  }
};
</script>
