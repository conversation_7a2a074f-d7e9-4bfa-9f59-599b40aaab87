import request from '@/utils/request'

// 查询评分办法详细信息列表
export function listItem(query) {
  return request({
    url: '/method/item/list',
    method: 'get',
    params: query
  })
}

// 查询评分办法详细信息详细
export function getItem(scoringMethodItemId) {
  return request({
    url: '/method/item/' + scoringMethodItemId,
    method: 'get'
  })
}

// 新增评分办法详细信息
export function addItem(data) {
  return request({
    url: '/method/item',
    method: 'post',
    data: data
  })
}

// 修改评分办法详细信息
export function updateItem(data) {
  return request({
    url: '/method/item',
    method: 'put',
    data: data
  })
}

// 删除评分办法详细信息
export function delItem(scoringMethodItemId) {
  return request({
    url: '/method/item/' + scoringMethodItemId,
    method: 'delete'
  })
}
