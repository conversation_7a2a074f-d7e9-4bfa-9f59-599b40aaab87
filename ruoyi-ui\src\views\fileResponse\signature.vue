<template>
  <div>
    <el-header height="100px">响应文件制作</el-header>
    <el-container>
      <el-aside width="230px">
        <div style="min-height: 740px">
            <div>
                <div>图片</div>
                <div>公司章</div>
            </div>
            <div>
                <div>图片</div>
                <div>法人章</div>
            </div>
            <div>

                
            </div>
        </div>

      </el-aside>
      <el-main>

      </el-main>
    </el-container>
  </div>
</template>

<script>
import basicInfo from "./components/basicInfo";
import biddingDocuments from "./components/biddingDocuments";
import bidText from "./components/bidText";
import { infoByParams } from "@/api/documents/uinfo";
export default {
  components: {
    basicInfo,
    biddingDocuments,
    bidText,
  },
  name: "Page401",
  data() {
    return {
      projectId: "",
      projectInfo: {},
      uItemsMap: {},
      uInfo: {},
      step: "1",
      resultInfo: {},
    };
  },
  mounted() {
    this.init(this.$route.query.projectId);
  },
  methods: {
    init(projectId) {
      let projectInfoJsonStr = localStorage.getItem(
        `fileResponseProjectInfo_${this.projectId}`
      );
      if (projectInfoJsonStr) {
        this.projectInfo = JSON.parse(projectInfoJsonStr);
      }
      this.projectId = projectId;
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: #409eff !important; /* 激活状态下的字体颜色 */
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;

  .title-button {
    font-size: 20px;
    color: #333;
    margin: 40px 0;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.head {
  display: flex;
  justify-content: space-between;
}
</style>
