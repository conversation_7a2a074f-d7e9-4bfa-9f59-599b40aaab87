import request from "@/utils/request";

// 查询用户评分办法信息列表
export function listUinfo(query) {
  return request({
    url: "/method/uinfo/list",
    method: "get",
    params: query,
  });
}

// 新增用户编制采购文件信息保存
export function saveInfo(data) {
  return request({
    url: "/method/uinfo/saveInfo",
    method: "post",
    data: data,
  });
}

// 查询用户评分办法信息详细
export function getUinfo(entMethodId) {
  return request({
    url: "/method/uinfo/" + entMethodId,
    method: "get",
  });
}

// 新增用户评分办法信息
export function addUinfo(data) {
  return request({
    url: "/method/uinfo",
    method: "post",
    data: data,
  });
}

// 修改用户评分办法信息
export function updateUinfo(data) {
  return request({
    url: "/method/uinfo",
    method: "put",
    data: data,
  });
}

// 删除用户评分办法信息
export function delUinfo(entMethodId) {
  return request({
    url: "/method/uinfo/" + entMethodId,
    method: "delete",
  });
}
