<template>
  <div class="tender" v-loading="loading">
    <el-col :span="24" class="card-box">
      <el-card>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div style="text-align: center; font-size:24px;font-weight: bolder;">{{ formData.tenderNotice.noticeName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">项目名称</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.projectName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">报价单位</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag
                      :options="dict.type.price_unit"
                      :value="formData.tenderNotice.priceUnit"
                    ></dict-tag>
                  </div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">评标方式</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag
                      :options="dict.type.busi_tender_bid_evaluation_mode"
                      :value="formData.tenderNotice.bidEvaluationMode"
                    ></dict-tag>
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">面向小微企业</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag
                      :options="dict.type.busi_tender_to_sme"
                      :value="formData.tenderNotice.toSme"
                    ></dict-tag>
                  </div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">开标方式</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    <dict-tag
                      :options="dict.type.busi_tender_bid_opening_mode"
                      :value="formData.tenderNotice.bidOpeningMode"
                    ></dict-tag>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i> 公告变更信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">原公告开始时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.oldTenderNotice.noticeStartTime }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">原投标截止时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.oldTenderNotice.bidOpeningTime }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">原开标室</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.oldTenderNotice.bidOpening.venueName }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">原开标时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.oldTenderNotice.bidOpeningTime }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">原开标地址：</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf" v-if="formData.oldTenderNotice.venueType==1">
                  <div class="cell">河南省鹤壁市淇滨区湘江路与钜新路交叉口京东大厦13楼</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf" v-if="formData.oldTenderNotice.venueType==2">
                  <div class="cell">{{ formData.oldTenderNotice.bidOpening.remark }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell" style="font-size: 17px;font-weight: bold;">变更为</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">公告开始时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.noticeStartTime }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">投标截止时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.bidOpeningTime }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">开标室</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.bidOpening.venueName }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">开标时间</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderNotice.bidOpeningTime }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">开标地址：</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf" v-if="formData.tenderNotice.venueType==1">
                  <div class="cell">河南省鹤壁市淇滨区湘江路与钜新路交叉口京东大厦13楼</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf" v-if="formData.tenderNotice.venueType==2">
                  <div class="cell">{{ formData.tenderNotice.bidOpening.remark }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i> 变更内容</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell" v-html="formData.tenderNotice.noticeContent"></div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <!-- <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-monitor"></i> 投标人资格</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell" v-html="formData.tenderProject.bidderQualification">
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">采购异议处理</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell" style="color: red">
                    如有异议请电话咨询采购人或采购代理，采购流程问题请咨询平台运营。
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col> -->
    <el-col :span="24" class="card-box" v-if="project.agencyFlag == 1">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-monitor"></i> 采购代理信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">名称</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">联系人</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyContactPerson }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">联系方式</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.agencyPhone }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-monitor"></i> 采购人信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">名称</div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererName }}</div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">联系人</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererContactPerson }}</div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">联系方式</div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">{{ formData.tenderProject.tendererPhone }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box" v-if="showNum">
      <el-card>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr>
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell" >当前报名供应商数量：</div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell" >{{ formData.biddingRecordNum }}</div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box">
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i> 附件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%; table-layout: fixed">
            <tbody>
              <tr
                v-for="(item, key) in formData.tenderNotice.attachmentMap"
                :key="dict.value"
              >
                <td colspan="1" class="el-table__cell is-leaf">
                  <div class="cell">{{ key }}</div>
                </td>
                <td colspan="5" class="el-table__cell is-leaf">
                  <div class="cell">
                    <FileUpload
                      :fileType="['pdf', 'doc', 'docx']"
                      :isShowTip="false"
                      :value="item"
                      :showOnly="true"
                    ></FileUpload>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <!-- <div slot="footer" class="option">
      <el-button v-if="formData.tenderNotice.delFlag==0" type="success" @click="changeNotice">变更</el-button>
      <el-button @click="cancel">关闭</el-button>
    </div> -->
  </div>
</template>
<script>
import {  getNotice,  noticeView} from "@/api/tender/notice";
import { listProject, getProject } from "@/api/tender/project";
import { download } from "@/utils/request";
import { getDicts } from "@/api/system/dict/data";
import { listInfo } from "@/api/venue/info";
import {
  listOccupy,
  getOccupy,
  addOccupy,
  updateOccupy,
} from "@/api/venue/occupy";
export default {
  components: {
  },
  dicts: [
    "price_unit",
    "busi_tender_allow_coalition",
    "busi_tender_to_sme",
    "busi_tender_bid_opening_mode",
    "busi_tender_bid_evaluation_mode",
    "busi_bid_evaluation_period",
    "busi_tender_notice_attachment",
  ],
  props: {
    noticeId: [String, Number],
    showNum: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName:'first',
      attachmentsMap: {},
      spaceOccupancyList: [],
      attachments: [],
      // 遮罩层
      loading: true,
      timePeriod: [],
      openingRoom: false,
      activeOpenRoom: "",
      evaluationRoom: false,
      activeEvaluationRoom: "",
      intervalTime: "",
      formData: {
        tenderNotice: {
          bidOpening: {}
        },
        oldTenderNotice: {
          bidOpening: {}
        },
        tenderProject: {},
        biddingRecordNum: 0,
        attachments: [],
      },
      project: {},
      // 开标室保存信息
      bidOpeningRoom: {
        noticeId: "",
        venueId: "",
        venueName: "",
        venueType: "",
        occupyStartTime: "",
        occupyEndTime: "",
        bidEvaluationPeriod: "",
      },
      // 评标室保存信息
      bidEvaluationRoom: {
        noticeId: "",
        venueId: "",
        venueName: "",
        venueType: "",
        occupyStartTime: "",
        occupyEndTime: "",
        bidEvaluationPeriod: "",
      },
      projectIdOptions: [],
      bidOpeningRoomOptions: [],
      bidEvaluationRoomOptions: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getConfigKey("sys.publicity.time").then((response) => {
      this.intervalTime = response.msg;
    });
  },
  mounted() {
    console.info("----------------noticeId");
    console.info(this.noticeId);
    if(this.noticeId==undefined){
      console.info(this.$route.params.noticeId);
      this.noticeId = this.$route.params.noticeId;
    }
    this.noticeView(this.noticeId);
  },
  methods: {
    noticeView(noticeId) {
      this.loading = true;
      // 根据公告id初始化采购公告
      noticeView(noticeId).then((response) => {
        if(response.code==200){
          this.formData = response.data;
          if(!this.formData.oldTenderNotice.bidOpening){
            this.formData.oldTenderNotice['bidOpening'] = {
              venueName:"",
              remark:''
            }
          }
        }
        this.loading = false;
      });
    },
    cancel() {
      this.$tab.closePage();
    },
    // 变更公告跳转
    changeNotice() {
      // 需要一个标识表明是新增
      this.$router.push({
        path: "/tender/notice/change/" + this.noticeId,
      });
    },
    handleClick(tab, event){
      console.log(tab, event);
    }
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: rgba(185, 248, 191, 1);

  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
