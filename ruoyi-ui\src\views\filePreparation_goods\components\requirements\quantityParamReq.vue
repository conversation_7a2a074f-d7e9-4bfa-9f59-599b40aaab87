<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
          <!-- <div class="image__preview">
            <div>
              <div style="font-size:12px;">偏离表-偏离模板</div>
              <el-image style="width: 100px ;margin-right:20px" :src="srcList[0]" :preview-src-list="srcList">
              </el-image>
            </div>
            <div>
              <div style="font-size:12px;">偏离表-分档模板</div>
              <el-image style="width: 100px;margin-right:20px" :src="srcList[1]" :preview-src-list="srcList">
              </el-image>
            </div>
          </div> -->
          可量化参数要求：<FileUpload v-model="pdfUrl" :limit="1" :fileType="['pdf']"></FileUpload>
          偏离表模板：<FileUpload v-model="wordUrl" :limit="1" :fileType="['doc','docx']"></FileUpload>
          <div style="font-size:12px;color: #606266;">请上传 <span style="color:red">word版</span> 偏离表模板</div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      itemContent: {
        paramReq: {
          requirement: ""
        },
        quantityParamReq: {
          requirement: ""
        },
        substantiveReq: {
          requirement: ""
        }
      },
      pdfUrl: "",
      wordUrl: "",
      // 偏离表-分档模板；偏离表-偏离模板
      srcList: [
        "/goods_pianli/pianli.png",
        "/goods_pianli/fendang.png"
      ]
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        this.pdfUrl = this.itemContent.quantityParamReq.pdfUrl;
        this.wordUrl = this.itemContent.quantityParamReq.wordUrl;
        if (this.pdfUrl && this.pdfUrl.length > 5) {
          this.pdfUrl = this.$download.returnFileHttpUrl(this.pdfUrl);
        }
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      this.itemContent.quantityParamReq.pdfUrl = this.pdfUrl;
      this.itemContent.quantityParamReq.wordUrl = this.wordUrl;
      if (this.pdfUrl && this.pdfUrl.length > 5) {
        this.itemContent.quantityParamReq.pdfUrl =
          this.$download.returnFileDirPath(this.pdfUrl);
      }
      let newContent = JSON.stringify(this.itemContent);
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newContent,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    //   导入
    leadInto() {
      this.$refs.fileUpload.onClick();
    },
    //删除
    delete_btn() {
      this.pdfUrl = "";
      this.saveUinfo();
    },
    handleInput(value) {
      this.pdfUrl = value.url;
      if (this.pdfUrl) {
        this.saveUinfo();
      }
    },
  },
  mounted() { },
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.image__preview {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
</style>
