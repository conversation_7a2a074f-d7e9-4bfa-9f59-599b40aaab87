<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <span>评分办法：</span>
            <el-select style="width:250px" v-model="value" placeholder="请选择" @change="(val) => loadScoringTemplateItems(val, true)">
              <el-option v-for="item in options" :key="item.value" :label="item.label+'\t\t'+'合计：'+ item.score +'分'" :value="item.value">
              </el-option>
            </el-select>
          </div>
          <!-- <div>
            <el-button
              class="item-button"
              style="background-color: #fff"
              @click="leadInto"
              >导入</el-button
            >
          </div> -->
        </div>
      </el-header>
      <el-container>
        <el-aside width="200px">
<!--          <div style="min-height: 600px">
            <div class="title-button" :class="{ btnActive: step == '1' }" @click="active('1', 'qualification', '资格性评审')" v-if="showAndHide('资格性评审')">
              资格性评审
            </div>
            <div class="title-button" :class="{ btnActive: step == '2' }" @click="active('2', 'qualification', '符合性评审')" v-if="showAndHide('符合性评审')">
              符合性评审
            </div>
            <div class="title-button" :class="{ btnActive: step == '3' }" @click="active('3', 'scoring', '投标报价打分')" v-if="showAndHide('投标报价打分')">
              投标报价打分
            </div>
            <div class="title-button" :class="{ btnActive: step == '4' }" @click="active('4', 'technicalBid', '技术标评审')" v-if="showAndHide('技术标评审')">
              技术标评审
            </div>
            <div class="title-button" :class="{ btnActive: step == '5' }" @click="active('5', 'technicalBid', '商务标评审')" v-if="showAndHide('商务标评审')">
              商务标评审
            </div>
            <div class="title-button" :class="{ btnActive: step == '6' }" @click="active('6', 'technicalBid', '其他评审因素')" v-if="showAndHide('其他评审因素')">
              其他评审因素
            </div>
            <div class="title-button" :class="{ btnActive: step == '7' }" @click="active('7', 'technicalBid', '中标候选人推选条件')" v-if="showAndHide('中标候选人推选条件')">
              中标候选人推选条件
            </div>
          </div>-->
          <div style="min-height: 600px">
            <!-- 通过v-for循环scoringInfoItems来动态生成按钮 -->
            <div
              v-for="infoItem in scoringInfoItems"
              :key="infoItem"
              class="title-button"
              :class="{ btnActive: step == infoItem }"
              @click="active(infoItem, getRefName(infoItem), infoItem)"
            >
              {{ infoItem }}
            </div>
          </div>
        </el-aside>
        <el-main>
<!--          <qualification v-if="step == '1'" ref="qualification" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('资格性评审', 'qualification', '1', uInfo)
            "></qualification>
          <qualification v-if="step == '2'" ref="qualification" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('符合性评审', 'qualification', '2', uInfo)
            "></qualification>
          <scoring v-if="step == '3'" ref="scoring" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap1('投标报价打分', 'scoring', '3', uInfo)
            "></scoring>
          <technicalBid v-if="step == '4'" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap('技术标评审', 'technicalBid', '4', uInfo)
            "></technicalBid>
          <technicalBid v-if="step == '5'" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap('商务标评审', 'technicalBid', '5', uInfo)
            "></technicalBid>
          <technicalBid v-if="step == '6'" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('其他评审因素', 'technicalBid', '6', uInfo)
            "></technicalBid>
          <technicalBid v-if="step == '7'" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('中标候选人推选条件', 'technicalBid', '7', uInfo)
            "></technicalBid>-->
          <qualification v-if="step.includes('资格性评审') " ref="qualification" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('资格性评审', 'qualification', '资格性评审', uInfo)
            "></qualification>
          <qualification v-if="step.includes('符合性评审')" ref="qualification" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('符合性评审', 'qualification', '符合性评审', uInfo)
            "></qualification>
          <scoring v-if="step.includes('投标报价打分') " ref="scoring" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap1('投标报价打分', 'scoring', '投标报价打分', uInfo)
            "></scoring>
          <technicalBid v-if="step.includes('技术标评审') " ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap('技术标评审', 'technicalBid', '技术标评审', uInfo)
            "></technicalBid>
          <technicalBid v-if="step.includes('商务标评审')" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) => updateInfoMap('商务标评审', 'technicalBid', '商务标评审', uInfo)
            "></technicalBid>
          <technicalBid v-if="step.includes('其他评审因素')" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('其他评审因素', 'technicalBid', '其他评审因素', uInfo)
            "></technicalBid>
          <technicalBid v-if="step.includes('中标候选人推选条件')" ref="technicalBid" @deleteSuccess="deleteSuccess" @saveSuccess="
              (uInfo) =>
                updateInfoMap('中标候选人推选条件', 'technicalBid', '中标候选人推选条件', uInfo)
            "></technicalBid>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import qualification from "./scoringMethods/qualification.vue";
import scoring from "./scoringMethods/scoring.vue";
import technicalBid from "./scoringMethods/technicalBid.vue";
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
import { listInfo } from "@/api/method/info";
import { listItem } from "@/api/method/item";

export default {
  components: { qualification, scoring, technicalBid },
  data() {
    return {
      uinfo: {},
      projectInfo: {},
      itemInfo: {},
      options: [
        {
          value: "选项1",
          label: "选项1",
        },
      ],
      scoringInfoItems: [],
      scoringItemMap: {},
      tempScoringItemMap: {},
      scoringUinfo: {},
      value: "",
      step: "资格性评审",
      activeParams: {
        stepNumber: "资格性评审",
        refName: "qualification",
        name: "资格性评审",
      },
    };
  },
  methods: {
    deleteSuccess(activeParams) {
      this.activeParams = activeParams;
      this.init(this.projectInfo, this.itemInfo, this.uinfo);
    },
    getRefName(infoItem) {
      console.log(infoItem)
      // 假设规则是根据特定关键字匹配返回不同ref名，可按实际需求改
      if (infoItem.includes('资格性评审')||infoItem.includes('符合性评审')) {
        console.log(111)
        return 'qualification';
      } else if (infoItem.includes('投标报价打分')) {
        console.log(222)
        return 'scoring';
      } else {
        console.log(333)
        return 'technicalBid';
      }
    },
    updateInfoMap1(name, refName, stepNumber, scoringUinfo) {
      this.$emit("saveSuccess", scoringUinfo);
      // this.scoringUinfo = scoringUinfo;
      // this.scoringItemMap[name] = this.scoringUinfo.scoringMethodUitems;
      // this.active(
      //   stepNumber,
      //   refName,
      //   name,
      //   this.scoringUinfo.scoringMethodUitems
      // );
    },
    updateInfoMap(name, refName, stepNumber, scoringUinfo) {
      this.$emit("saveSuccess", scoringUinfo);
      // this.scoringUinfo = scoringUinfo;
      // if (
      //   this.scoringUinfo &&
      //   this.scoringUinfo.scoringMethodUitems &&
      //   this.scoringUinfo.scoringMethodUitems.length > 0
      // ) {
      //   console.log(888, scoringUinfo);
      //   this.mergeUitemMap();
      // }
    },
    showAndHide(val) {
      return  this.scoringInfoItems.some(item => item.includes(val))
    },
    loadScoringTemplateItems(val, isSave) {
      if (!val) {
        return false;
      }
      let query = {
        scoringMethodId: val,
        pageNum: 1,
        pageSize: 1000,
        params: {
          returnUitem: "true",
          projectId: this.projectInfo.projectId,
        },
      };
      return listItem(query)
        .then((result) => {
          if (result.code === 200) {
            this.scoringInfoItems = result.rows.map((item) => {
              console.log(item.itemName,item.score)
              var itemNameScore=item.itemName+"（"+item.score+"分）";
              if (item.score==0){
                itemNameScore=item.itemName;
              }
              this.scoringItemMap[itemNameScore] =
                this.scoringItemMap[itemNameScore] || [];
              if (item.uitems && item.uitems.length > 0) {
                // console.log(777, item, item.uitems);
                this.scoringItemMap[itemNameScore].push(...item.uitems);
              }
              this.tempScoringItemMap[itemNameScore] = item;
              return itemNameScore;
            });
            console.log(444, this.scoringInfoItems, this.scoringItemMap);
            if (isSave) {
              this.saveUinfo();
            }
            //获取id==val的对象
            this.scoringUinfo = this.options.find((item) => {
              console.log(222, item, val);
              return item.value == val;
            }).entity.uinfo;
            console.log(333, this.scoringUinfo);
            console.log(111, this.scoringUinfo, this.options);
            if (
              this.scoringUinfo &&
              this.scoringUinfo.scoringMethodUitems &&
              this.scoringUinfo.scoringMethodUitems.length > 0
            ) {
              this.mergeUitemMap();
            }
          }
        })
        .catch((err) => { });
    },
    mergeUitemMap() {
      console.log(
        666,
        this.scoringItemMap,
        this.scoringUinfo.scoringMethodUitems
      );
      for (const key in this.scoringItemMap) {
        // 假设this.scoringItemMap[key]是一个包含多个对象的数组
        let foundItems = this.scoringUinfo.scoringMethodUitems.filter((item) =>
          this.scoringItemMap[key].some(
            (mapItem) =>
              // mapItem.scoringMethodItemId === item.scoringMethodItemId
              this.tempScoringItemMap[key].scoringMethodItemId ===
              item.scoringMethodItemId
          )
        );
        // 将找到的所有items赋值给this.scoringItemMap[key]
        if (foundItems && foundItems.length > 0) {
          this.scoringItemMap[key] = foundItems;
        }
      }
    },
    //加载评分模板
    loadScoringTemplate() {
      let query = {
        tenderMode: this.projectInfo.tenderMode,
        projectType: this.projectInfo.projectType,
        pageNum: 1,
        pageSize: 1000,
        params: {
          projectId: this.projectInfo.projectId,
          returnUinfo: "true",
        },
      };
      return listInfo(query)
        .then((result) => {
          this.options = result.rows.map((item) => {
            return {
              label: item.methodName,
              value: item.scoringMethodId,
              score: item.score,
              entity: item,
            };
          });
        })
        .catch((err) => { });
    },
    active(stepNumber, refName, name, extData) {
      console.log(
        "socring info active",
        this.projectInfo,
        this.scoringItemMap[name],
        this.scoringUinfo,
        this.tempScoringItemMap[name]
      );
      this.activeParams = {
        stepNumber: stepNumber,
        refName: refName,
        name: name,
      };
      this.step = stepNumber;
      // 加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(
          this.projectInfo,
          this.scoringItemMap[name],
          this.scoringUinfo,
          this.tempScoringItemMap[name],
          { stepNumber, refName, name },
          extData
        );
      });
    },
    // TODO 导入
    leadInto() { },
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      console.log("scoringMethod init start", projectInfo, itemInfo, uinfo);
      this.projectInfo = projectInfo;
      this.uinfo = uinfo;
      this.loadScoringTemplate()
        .then(() => {
          var isSave = true;
          if (this.options && this.options.length > 0) {
            if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
              this.value = parseInt(itemInfo.itemContent);
              isSave = false;
            } else {
              this.value = this.options[0].value;
            }
            this.itemInfo = itemInfo;
            if (uinfo && uinfo.entFileId) {
              this.itemInfo.entFileId = uinfo.entFileId;
            }
            this.loadScoringTemplateItems(this.value, isSave)
              .then(() => {
                //加载子页面
                this.$nextTick(() => {
                  this.active(
                    this.activeParams.stepNumber,
                    this.activeParams.refName,
                    this.activeParams.name
                  );
                });
              })
              .catch((err) => { });
          }
        })
        .catch((err) => { });
      console.log("scoringMethod init end", this.itemInfo);
    },
    saveUinfo() {
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: this.value,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: rgba(152, 200, 253, 1) !important; /* 激活状态下的字体颜色 */
}
.head {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
  line-height: 100px;
}
.el-aside {
  padding: 10px 0px 10px 20px;
  background-color: #fff;
  border-right: #333 1px solid;
  .title-button {
    font-size: 16px;
    color: #333;
    margin: 25px 0;
    cursor: pointer;
    s &:hover {
      color: rgba(152, 200, 253, 1);
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  //   padding: 0;
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #333;
  }
}
</style>
