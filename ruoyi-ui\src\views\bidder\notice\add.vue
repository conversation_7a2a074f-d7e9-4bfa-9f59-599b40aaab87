<template>
  <div
    class="tender"
    style="margin: 20px 0px;"
    v-loading="loading"
  >
    <el-form
      ref="winningBidderNotice"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="0"
    >
      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>开标情况</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table
              cellspacing="0"
              style="width: 100%;table-layout:fixed;"
            >
              <tbody>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>项目
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="projectId">
                        <el-select
                          v-model="formData.projectId"
                          placeholder="请选择项目"
                          ref="selectProject"
                          filterable
                          clearable
                          :disabled="initProject"
                          :style="{ width: '100%' }"
                          @change="change($event)"
                        >
                          <el-option
                            v-for="(item, index) in projectIdOptions"
                            :key="index"
                            :label="item.projectName"
                            :value="item.projectId"
                            :disabled="item.disabled"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>公告名称
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="noticeName">
                        <el-input
                          v-model="formData.noticeName"
                          placeholder="公告名称"
                          clearable
                          :style="{ width: '100%' }"
                        >
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>项目编号
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="projectId">
                        {{formData.projectCode}}
                      </el-form-item>
                    </div>
                  </td>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>公告发布时间
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="noticeStartTime">
                        <el-date-picker
                          v-model="formData.noticeStartTime"
                          format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          :picker-options="pickerOptionsOne"
                          :style="{ width: '100%' }"
                          placeholder="请选择日期"
                          clearable
                          disabled
                        ></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>中标人信息</span>
          </div>
          <div>
            <el-table
              :data="bidderTable"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              ></el-table-column>
              <el-table-column
                v-if="false"
                label="投标人记录id"
                prop="bidderInfoId"
              ></el-table-column>
              <el-table-column
                label="投标人名称"
                prop="bidderName"
              ></el-table-column>
              <el-table-column
                label="投标报价"
                prop="bidderAmount"
              ></el-table-column>
              <el-table-column
                prop="score"
                label="评分"
              > </el-table-column>
              <el-table-column
                prop="ranking"
                label="名次"
              > </el-table-column>
              <el-table-column label="确认中标人">
                <template slot-scope="scope">
                  <el-checkbox
                    v-model="scope.row.isWinCheckBox"
                    @change="winBidder(scope.row.bidderId)"
                  ></el-checkbox>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-card>
      </el-col>

<!-- <el-col
  :span="24"
  class="card-box"
>
  <el-card>
    <div slot="header">
      <span><i class="el-icon-document"></i>公告内容</span>
    </div>
    <div>
      <el-form-item prop="noticeContent">
        <div
          v-html="formData.noticeContent"
          :min-height="192"
        />
      </el-form-item>
    </div>
  </el-card>
</el-col> -->

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table
              cellspacing="0"
              style="width: 100%;table-layout:fixed;"
            >
              <tbody>
                <tr
                  v-for="dict in dict.type.busi_bidder_notice_attachment"
                  :key="dict.label"
                >
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong
                        style="color:red;"
                        v-if="dict.raw.isEquals==1"
                      >*</strong>{{ dict.label }}
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell cell-right-border">
                      <el-form-item class="upload">
                        <template>
                          <FileUpload
                            :value="getImgPath(dict)"
                            @input="handleInput(dict, $event)"
                            :fileType="['pdf', 'doc', 'docx']"
                            :isShowTip="false"
                          ></FileUpload>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

    </el-form>
    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: center;"
    >
      <el-button
        type="primary"
        @click="submitForm"
      >发布</el-button>
      <!-- <el-button type="primary" @click="TemporaryStorage">暂存</el-button> -->
      <el-button @click="resetForm">重置</el-button>
    </div>
  </div>
</template>
<script>
import { listProject } from "@/api/tender/project";
import { listInfo } from "@/api/bidding/info";
import {
  addNotice,
  updateNotice,
  createNoticeContent,
} from "@/api/bidder/notice";

export default {
  components: {},
  dicts: ["busi_bidder_notice_attachment", "price_unit"],
  props: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      initProject: false,
      formData: {
        projectId: "",
        noticeName: "",
        noticeCode: "",
        noticeContent: "",
        noticeType: 1,
        noticeStartTime: "",
        bidderId: "",
        bidderCode: "",
        bidderName: "",
        bidAmount: "",
        ranking: "",
        score: "",
        attachments: [],
      },
      bidderTable: [],
      noticeName: "",
      rules: {
        projectId: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        noticeName: [
          {
            required: true,
            message: "请输入公告名称",
            trigger: "blur",
          },
        ],
        noticeStartTime: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        noticeContent: [
          {
            required: false,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
      },
      projectIdOptions: [],
      attachmentsMap: {},
      // 公告开始时间日期选择的禁用日期
      pickerOptionsOne: {
        disabledDate: (time) => {
          return time.getTime() < new Date() - 8.64e7;
        },
      },
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getList();
    this.formData.noticeStartTime = new Date();
  },
  methods: {
    getSelectedWinBidders() {
      return this.bidderTable.filter(bidder => bidder.isWinCheckBox);
    },
    getList() {
      listProject({ delFlag: 0, projectStatus: 50 }).then((response) => {
        this.projectIdOptions = response.rows;
        this.loading = false;
        if (this.$route.query.projectId) {
          this.formData.projectId = parseInt(this.$route.query.projectId);
          this.$refs.selectProject.$emit("change", this.formData.projectId);
          this.initProject = true;
        }
      });
    },
    change(value) {
      if (value) {
        const selected = this.projectIdOptions.find((option) => {
          return option.projectId == value;
        });

        this.formData.noticeName = selected.projectName + "-结果公告";
        this.formData.projectCode = selected.projectCode;
        listInfo({ projectId: value }).then((response) => {
          this.bidderTable = response.rows;
        });
        // getBidderInfoByProjectID(value).then((result) => {
        //   if (result.code == 200) {
        //    // this.formData.noticeContent = result.data;
        //     console.log(result)
        //     this.bidderTable = result.data; // 将数据赋值给bidderTable

        //   }
        // });
      }
    },
    resetForm() {
      this.$refs["winningBidderNotice"].resetFields();
    },
    handleInput(dict, data) {
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
    },
    submitForm() {
      for (let item of this.dict.type.busi_bidder_notice_attachment) {
        if (
          item.raw.isEquals == 1 &&
          this.attachmentsMap[item.value] == undefined
        ) {
          this.$message.error(item.label + " 文件不能为空");
          return;
        }
      }
      const selectedWinBidders = this.getSelectedWinBidders();
      console.log('选中的中标人数据:', selectedWinBidders);
      if (selectedWinBidders.length !== 1) {
        // 如果不是一条记录，弹出错误信息
        this.$message.error('只能选中一条中标人数据');
      }

      /*const winningBidder = this.bidderTable.find((bidder) => {
        bidder.isWin == 1;
      });*/
      const winner = this.getSelectedWinBidders()[0];

      this.$refs["winningBidderNotice"].validate((valid) => {
        if (!valid) return;

        if (this.$route.params.noticeId == 0) {
          this.formData.attachments = [].concat(
            ...Object.values(this.attachmentsMap)
          );
          this.formData.bidderId = winner.bidderId;
          this.formData.bidderCode = winner.bidderCode;
          this.formData.bidderName = winner.bidderName;
          this.formData.bidAmount = winner.bidderAmount;
          this.formData.ranking = winner.ranking;
          this.formData.score = winner.score;
          console.log("submit：")
          console.log(this.formData)
          addNotice(this.formData).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.close();
          });
        } else {
          updateNotice(this.formData).then((response) => {
            this.$modal.msgSuccess("修改成功");
            this.close();
          });
        }
      });
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    winBidder(val) {
      var biddingInfoList = this.bidderTable;
      var winBidder = '';
      for (var index in biddingInfoList) {
        if (biddingInfoList[index].bidderId == val) {
          biddingInfoList[index].isWin = 1;
          winBidder = biddingInfoList[index];
        } else {
          biddingInfoList[index].isWin = 0;
          biddingInfoList[index].isWinCheckBox = false;
        }
      }
      console.log('winBidder',winBidder);

      createNoticeContent(winBidder.projectId, winBidder.bidderInfoId).then((result) => {
        if (result.code == 200) {
          this.formData.noticeContent = result.data;
        }
      });
    },
    // 关闭当前页
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
.el-form-item {
  margin-bottom: 0px;
}
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
