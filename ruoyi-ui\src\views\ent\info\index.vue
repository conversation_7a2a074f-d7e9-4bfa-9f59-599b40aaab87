<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="140px">
      <el-form-item label="企业名称" prop="entName">
        <el-input
          v-model="queryParams.entName"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="统一社会信用代码" prop="entCode">
        <el-input
          v-model="queryParams.entCode"
          placeholder="请输入统一社会信用代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!-- <el-form-item label="经济性质" prop="entNature">
        <el-select v-model="queryParams.entNature" placeholder="请选择经济性质" clearable>
          <el-option
            v-for="dict in dict.type.ent_economic_nature"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <!-- <el-form-item label="联系人" prop="entLinkman">
        <el-input
          v-model="queryParams.entLinkman"
          placeholder="请输入联系人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="联系方式" prop="entContactPhone">
        <el-input
          v-model="queryParams.entContactPhone"
          placeholder="请输入联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="法人" prop="entLegalPerson">
        <el-input
          v-model="queryParams.entLegalPerson"
          placeholder="请输入法人"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="法人联系方式" prop="entLegalPersonPhone">
        <el-input
          v-model="queryParams.entLegalPersonPhone"
          placeholder="请输入法人联系方式"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="企业状态" prop="entStatus">
        <el-select v-model="queryParams.entStatus" placeholder="请选择企业状态" clearable>
          <el-option
            v-for="dict in dict.type.base_ent_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item> -->
      <el-form-item label="企业类型" prop="entType">
        <el-select v-model="queryParams.entType" placeholder="请选择企业类型" clearable>
          <el-option
            v-for="dict in dict.type.base_ent_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ent:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ent:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ent:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ent:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="企业id" align="center" prop="entId" /> -->
      <el-table-column label="企业名称" align="center" prop="entName" />
      <el-table-column label="统一社会信用代码" align="center" prop="entCode" />
      <el-table-column label="经济性质" align="center" prop="entNature">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.ent_economic_nature" :value="scope.row.entNature"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="企业地址" align="center" prop="entAddress" />
      <el-table-column label="重点代理领域" align="center" prop="entKeyAgencyAreas" /> -->
      <!-- <el-table-column label="开户行" align="center" prop="entOpeningBank" /> -->
      <!-- <el-table-column label="开户行户号" align="center" prop="entBankCode" /> -->
      <!-- <el-table-column label="单位网址" align="center" prop="entWebsite" />
      <el-table-column label="企业简介" align="center" prop="entIntro" /> -->
      <el-table-column label="联系人" align="center" prop="entLinkman" />
      <el-table-column label="联系方式" align="center" prop="entContactPhone" />
      <!-- <el-table-column label="法人" align="center" prop="entLegalPerson" />
      <el-table-column label="法人联系方式" align="center" prop="entLegalPersonPhone" /> -->
      <el-table-column label="企业状态" align="center" prop="entStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_ent_status" :value="scope.row.entStatus"/>
        </template>
      </el-table-column>
      <!-- <el-table-column label="企业LOGO" align="center" prop="entLogo" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.entLogo" :width="50" :height="50"/>
        </template>
      </el-table-column> -->
      <el-table-column label="企业类型" align="center" prop="entType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_ent_type" :value="scope.row.entType"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ent:info:edit']"
          >修改</el-button>
          <el-button
            v-show="scope.row.busiState == 1"
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAudit(scope.row)"
            v-hasPermi="['ent:info:audit']"
          >审核</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ent:info:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ent:info:remove']"
          >重置登录密码</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="resetKey(scope.row)"
            v-hasPermi="['ent:info:remove']"
          >重置二级密码</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业信息对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="企业名称" prop="entName">
          <el-input v-model="form.entName" placeholder="请输入企业名称" />
        </el-form-item>
        <el-form-item label="统一社会信用代码" prop="entCode">
          <el-input v-model="form.entCode" placeholder="请输入统一社会信用代码" />
        </el-form-item>
        <el-form-item label="密钥" prop="secretKey">
          <el-input v-model="form.secretKey" placeholder="密钥" />
        </el-form-item>
        <el-form-item label="经济性质" prop="entNature">
          <el-select v-model="form.entNature" placeholder="请选择经济性质">
            <el-option
              v-for="dict in dict.type.ent_economic_nature"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业地址" prop="entAddress">
          <el-input v-model="form.entAddress" placeholder="请输入企业地址" />
        </el-form-item>
        <el-form-item label="重点代理领域" prop="entKeyAgencyAreas">
          <el-input v-model="form.entKeyAgencyAreas" placeholder="请输入重点代理领域" />
        </el-form-item>
        <el-form-item label="开户行" prop="entOpeningBank">
          <el-input v-model="form.entOpeningBank" placeholder="请输入开户行" />
        </el-form-item>
        <el-form-item label="开户行户号" prop="entBankCode">
          <el-input v-model="form.entBankCode" placeholder="请输入开户行户号" />
        </el-form-item>
        <el-form-item label="单位网址" prop="entWebsite">
          <el-input v-model="form.entWebsite" placeholder="请输入单位网址" />
        </el-form-item>
        <el-form-item label="企业简介" prop="entIntro">
          <editor v-model="form.entIntro" :min-height="192"/>
        </el-form-item>
        <el-form-item label="联系人" prop="entLinkman">
          <el-input v-model="form.entLinkman" placeholder="请输入联系人" />
        </el-form-item>
        <el-form-item label="联系方式" prop="entContactPhone">
          <el-input v-model="form.entContactPhone" placeholder="请输入联系方式" />
        </el-form-item>
        <el-form-item label="法人" prop="entLegalPerson">
          <el-input v-model="form.entLegalPerson" placeholder="请输入法人" />
        </el-form-item>
        <el-form-item label="法人联系方式" prop="entLegalPersonPhone">
          <el-input v-model="form.entLegalPersonPhone" placeholder="请输入法人联系方式" />
        </el-form-item>
        <el-form-item label="企业状态" prop="entStatus">
          <el-select v-model="form.entStatus" placeholder="请选择企业状态">
            <el-option
              v-for="dict in dict.type.base_ent_status"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业类型" prop="entType">
          <el-select v-model="form.entType" placeholder="请选择企业类型">
            <el-option
              v-for="dict in dict.type.base_ent_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="企业LOGO" prop="entLogo">
          <image-upload v-model="form.entLogo"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo, resetSecretKey } from "@/api/ent/info";

export default {
  name: "Info",
  dicts: ['ent_economic_nature', 'base_ent_status', 'base_ent_type', 'base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entName: null,
        entCode: null,
        entNature: null,
        entLinkman: null,
        entContactPhone: null,
        entLegalPerson: null,
        entLegalPersonPhone: null,
        entStatus: null,
        entLogo: null,
        entType: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entName: [
          { required: true, message: "企业名称不能为空", trigger: "blur" }
        ],
        entCode: [
          { required: true, message: "统一社会信用代码不能为空", trigger: "blur" }
        ],
        entAddress: [
          { required: true, message: "企业地址不能为空", trigger: "blur" }
        ],
        entKeyAgencyAreas: [
          { required: false, message: "重点代理领域不能为空", trigger: "blur" }
        ],
        entLinkman: [
          { required: true, message: "联系人不能为空", trigger: "blur" }
        ],
        entContactPhone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" }
        ],
        entStatus: [
          { required: true, message: "企业状态不能为空", trigger: "change" }
        ],
        entType: [
          { required: true, message: "企业类型不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业信息列表 */
    getList() {
      this.loading = true;
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    //重置二级密码
    resetKey(row){
      this.loading = true;
      resetSecretKey(row.entId).then(response => {
        if(response.code==200){
          this.$message.success(response.msg);
        }else{
        this.$message.error(response.msg);
        }
        this.loading = false;
      });

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        entId: null,
        entName: null,
        entCode: null,
        entNature: null,
        entAddress: null,
        entKeyAgencyAreas: null,
        entOpeningBank: null,
        entBankCode: null,
        entWebsite: null,
        entIntro: null,
        entLinkman: null,
        entContactPhone: null,
        entLegalPerson: null,
        entLegalPersonPhone: null,
        entStatus: null,
        entLogo: null,
        entType: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.entId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const entId = row.entId || this.ids
      getInfo(entId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业信息";
      });
    },
    /** 审核按钮操作 */
    handleAudit(row) {
      this.reset();
      const entId = row.entId || this.ids;
      this.$router.push("/ent/audit/" + entId);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.entId != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const entIds = row.entId || this.ids;
      this.$modal.confirm('是否确认删除企业信息编号为"' + entIds + '"的数据项？').then(function() {
        return delInfo(entIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ent/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
