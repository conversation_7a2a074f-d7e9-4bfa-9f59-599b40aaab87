import request from '@/utils/request'

// 查询场地信息列表
export function listInfo(query) {
  return request({
    url: '/venue/info/list',
    method: 'get',
    params: query
  })
}

// 查询场地信息详细
export function getInfo(venueId) {
  return request({
    url: '/venue/info/' + venueId,
    method: 'get'
  })
}

// 新增场地信息
export function addInfo(data) {
  return request({
    url: '/venue/info',
    method: 'post',
    data: data
  })
}

// 修改场地信息
export function updateInfo(data) {
  return request({
    url: '/venue/info',
    method: 'put',
    data: data
  })
}

// 删除场地信息
export function delInfo(venueId) {
  return request({
    url: '/venue/info/' + venueId,
    method: 'delete'
  })
}
