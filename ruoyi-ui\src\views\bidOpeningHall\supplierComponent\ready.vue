<!-- 供应商开标准备 -->
<template>
  <div class="ready">
    <div class="ready-line-one">
      <div class="ready-audio">
        <audio
          controls
          autoplay
          loop
        >
          <source src="../../../assets/mp3/test.mp3" />
        </audio>
      </div>
      <div class="ready-countdown">
        <el-statistic
          format="开标倒计时：HH小时mm分ss秒"
          :value="deadline"
          time-indices
        >
        </el-statistic>
      </div>
    </div>

    <div class="ready-line-two">
      <div class="ready-title">开标流程介绍</div>
      <div class="ready-content">
        <p>
        </p>
        <p>
          
          在进行线上开标过程中，为确保过程的公平、公正与透明，所有参与方需严格遵守以下纪律：
          <br>1、所有响应人（供应商）应提前登录电子交易平台进入开标大厅，确保网络连接稳定，以准时参加开标会议；
          <br>2、在整个开标期间，响应人、采购单位或其委托的代理机构都必须对所获信息保密，未经许可不得泄露给第三方；
          <br>3、响应人、采购单位或其委托的代理机构应保持良好的沟通态度，禁止发表不恰当言论或采取任何可能干扰开标正常进行的行为；
          <br>4、遇到技术故障时应及时联系技术支持团队解决，若因技术原因导致无法继续，则根据预案决定是否暂停或重新安排时间；
          <br>5、开标结束后，采购单位或其委托的代理机构将按照规定程序打印开标记录表，响应人则需留在“开标已结束”页面等待二次报价。
        </p>
      </div>

    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    projectInfo: {},
  },
  data() {
    //这里存放数据
    return {
      deadline: new Date(this.projectInfo.bidOpeningTime),
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {},
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.ready {
  padding: 20px 25px;
  .ready-line-one {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .ready-audio {
      width: 50%;
      display: flex;
      justify-content: center;
    }
    .ready-countdown {
      width: 50%;
      height: 105px;

      background: #176adb;
      display: flex;
      align-items: center;
      justify-content: center;
      ::v-deep .number {
        color: #fff;
        font-weight: 700;
      }
    }
  }
  .ready-line-two {
    border: 1px solid #979797;
    padding: 25px 38px;

    .ready-title {
      text-align: center;
      font-weight: 700;
      font-size: 25px;
      color: #333333;

      margin-bottom: 15px;
    }
    .ready-content {
      font-weight: 500;
      font-size: 15px;
      color: #333333;
      letter-spacing: 0;
      height: 300px;

      // overflow: hidden;

      p {
        text-indent: 2em;
        margin: 0;
        line-height: 30px;
      }
    }
  }
}
</style>