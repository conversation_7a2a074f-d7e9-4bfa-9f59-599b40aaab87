<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
<!--      <el-form-item label="评标记录id" prop="bidEvaluationId">
        <el-input
          v-model="queryParams.bidEvaluationId"
          placeholder="请输入评标记录id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item label="项目" prop="projectId">
<!--        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />-->
        <el-select
            v-model="queryParams.projectId"
            placeholder="请选择项目"
            filterable
            clearable
            :style="{ width: '100%' }"
            @change="change($event)"
        >
          <el-option
              v-for="(item, index) in projectIdOptions"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
              :disabled="item.disabled"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="评审开始时间">
        <el-date-picker
          v-model="daterangeEvaluationStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <!--      <el-form-item label="评审结束时间">
              <el-date-picker
                v-model="daterangeBidEvaluationEndTime"
                style="width: 240px"
                value-format="yyyy-MM-dd HH:mm:ss"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="删除标记" prop="delFlag">
              <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记 0正常 1删除" clearable>
                <el-option
                  v-for="dict in dict.type.base_yes_no"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="daterangeCreateTime"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="创建者" prop="createBy">
              <el-input
                v-model="queryParams.createBy"
                placeholder="请输入创建者"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="修改时间">
              <el-date-picker
                v-model="daterangeUpdateTime"
                style="width: 240px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
            <el-form-item label="修改者" prop="updateBy">
              <el-input
                v-model="queryParams.updateBy"
                placeholder="请输入修改者"
                clearable
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>-->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bid:evaluation:add']"
        >新增</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bid:evaluation:edit']"
        >修改</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bid:evaluation:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['bid:evaluation:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="evaluationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="评标记录id" align="center" prop="bidEvaluationId" /> -->
      <el-table-column label="项目名称" align="center" prop="project.projectName" />
      <el-table-column label="评审开始时间" align="center" prop="evaluationStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.evaluationStartTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评审结束时间" align="center" prop="bidEvaluationEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidEvaluationEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['bid:evaluation:view']"
          >查看详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bid:evaluation:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bid:evaluation:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改评标记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectId">
<!--          <el-input v-model="form.projectId" placeholder="请输入项目id" />-->
          <el-select
              v-model="form.projectId"
              placeholder="请选择项目"
              filterable
              clearable
              :style="{ width: '100%' }"
              @change="change($event)"
          >
            <el-option
                v-for="(item, index) in projectIdOptions"
                :key="index"
                :label="item.projectName"
                :value="item.projectId"
                :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="评审开始时间" prop="evaluationStartTime">
          <el-date-picker clearable
            v-model="form.evaluationStartTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择评审开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="评审结束时间" prop="bidEvaluationEndTime">
          <el-date-picker clearable
            v-model="form.bidEvaluationEndTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择评审结束时间">
          </el-date-picker>
        </el-form-item>
<!--        <el-form-item label="删除标记 0正常 1删除" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-table :data="projectInfoList" style="width: 100%">
          <el-table-column prop="bidderId" label="供应商ID"></el-table-column>
          <el-table-column prop="bidderName" label="供应商名称"></el-table-column>
          <el-table-column label="最终报价">
            <template slot-scope="scope">
              <el-input v-model="scope.row.bidderAmount"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="评分">
            <template slot-scope="scope">
              <el-input v-model="scope.row.score"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="排名">
            <template slot-scope="scope">
              <el-input v-model="scope.row.ranking"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="是否中标">
            <template slot-scope="scope">
              <el-radio-group v-model="scope.row.isWin" @change="handleWinChange(scope.row)">
                <el-radio :label="0">未中标</el-radio>
                <el-radio :label="1">中标</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column label="是否废标">
            <template slot-scope="scope">
<!--              <el-radio-group v-model="scope.row.isAbandonedBid" @change="handleWinChange(scope.row)">-->
<!--                <el-radio :label="1">废标</el-radio>-->
<!--              </el-radio-group>-->
              <el-switch
                :active-value="1"
                :inactive-value="0"
                v-model="scope.row.isAbandonedBid"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="废标原因">
            <template slot-scope="scope">
              <el-input v-model="scope.row.abandonedBidReason"></el-input>
            </template>
          </el-table-column>
        </el-table>
<!--        <el-form-item label-width="120px" :label="item.dictLabel"
                      v-for="(item,index) in attachmentTypes" :key="index" >
          <template>
          <FileUpload
            :value="attachmentMap[item.dictValue]"
            @input="handleInput(item.dictValue, $event)"
            :fileType="['pdf', 'doc', 'docx']"
            :isShowTip="false"
            :showOnly="false"
          ></FileUpload>
&lt;!&ndash;           添加下载链接
          <a :href="/dev-api/+attachmentMap[item.dictValue]" download>
            {{ attachmentMap[item.dictValue]}}
          </a>&ndash;&gt;

            &lt;!&ndash; 新增的按钮，调用 downloadFile 方法 &ndash;&gt;
            <el-button
              size="mini"
              type="text"
              icon="el-icon-download"
            @click="downLoadFile(form.projectId,attachmentMap[item.dictValue])"
            >调用下载</el-button>

          </template>
        </el-form-item>-->
        <el-form-item
          v-for="dict in dict.type.busi_bid_evaluation_attachment"
          :key="dict.label"
          label-width="120px"
          :label="dict.label"
          class="upload"
        >
          <template>
            <FileUpload
              :value="getImgPath(dict)"
              @input="handleInput(dict, $event)"
              :fileType="['pdf', 'doc', 'docx']"
              :isShowTip="false"
            ></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listEvaluation, getEvaluation, delEvaluation,downLoadFile, addEvaluation, updateEvaluation } from "@/api/bid/evaluation";
import { listProject } from "@/api/tender/project";
import {listInfo} from "@/api/bidding/info";
import {listRecord} from "@/api/bidding/record";
import {openingProject} from "@/api/bid/opening";
import {getDicts} from "@/api/system/dict/data";
//引入download，这是若依框架自带的，在utils/request里面封装好的方法
import {download} from "@/utils/request";
export default {
  name: "Evaluation",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 评标记录表格数据
      evaluationList: [],
      attachmentTypes: [],
      attachmentMap: {},
      projectIdOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 0正常 1删除时间范围
      daterangeEvaluationStartTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeBidEvaluationEndTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCreateTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeUpdateTime: [],
      projectInfoList:[],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bidEvaluationId: null,
        projectId: null,
        evaluationStartTime: null,
        bidEvaluationEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {
        attachments:[]
      },
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "blur" }
        ],
        evaluationStartTime: [
          { required: true, message: "评审开始时间不能为空", trigger: "blur" }
        ],
        bidEvaluationEndTime: [
          { required: true, message: "评审结束时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标记 0正常 1删除不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
    this.getAttachmentTypes();
  },
  methods: {
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentMap[dict.value] = fileList;
      }
    },
    getImgPath(dict) {
      if (
        this.attachmentMap[dict.value] &&
        this.attachmentMap[dict.value].length > 0
      ) {
        let arr = this.attachmentMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    downLoadFile(id,value){
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length-1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId:id,
        fileName:fileName,
        filePath:value,
        resource:value
      }
       downLoadFile(params).then(response => {
          if (response["code"]==200){
            download("/common/download/resource", params, fileName);
          }
      });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
    /*
      /!** request里面的download下载函数 *!/
      //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
      console.log("文件名");*/
    },

    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },
    // handleInput(i, j){
    //   console.info(i+"    "+j);
    //   this.attachmentTypes[i] = j;
    //   console.info(this.attachmentTypes);
    //   this.attachmentMap[i] = j;
    // },
    getAttachmentTypes(){
      getDicts("busi_bid_evaluation_attachment").then((result) => {
        console.info(result);
        if(result.code==200){
          console.info("attachment result"+result);
          this.attachmentTypes = result.data;
        }
      });
    },

    handleWinChange(row) {
      // 在这里处理单选按钮变化时的逻辑
      console.log('中标状态改变:', row);
      // 你可以调用API更新数据库中的中标状态
    },
    change(value) {
      this.queryParams.params = {};
      this.queryParams.projectId = value;
      console.log("projectId:"+value);

      listRecord(this.queryParams).then(response => {
        this.projectInfoList = response.rows;
        console.log(123);
      });

      // listInfo(this.queryParams).then(response => {
      //   console.log(response);
      //
      //   this.projectInfoList = response.rows;
      //
      // });
    },
    /** 查询评标记录列表 */
    getList() {
      this.loading = true;
      // 获取项目
      listProject().then((response) => {
        this.projectIdOptions = response.rows;
        this.loading = false;
      });
      this.queryParams.params = {};
      if (null != this.daterangeEvaluationStartTime && '' != this.daterangeEvaluationStartTime) {
        this.queryParams.params["beginEvaluationStartTime"] = this.daterangeEvaluationStartTime[0];
        this.queryParams.params["endEvaluationStartTime"] = this.daterangeEvaluationStartTime[1];
      }
      if (null != this.daterangeBidEvaluationEndTime && '' != this.daterangeBidEvaluationEndTime) {
        this.queryParams.params["beginBidEvaluationEndTime"] = this.daterangeBidEvaluationEndTime[0];
        this.queryParams.params["endBidEvaluationEndTime"] = this.daterangeBidEvaluationEndTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listEvaluation(this.queryParams).then(response => {
        this.evaluationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        bidEvaluationId: null,
        projectId: null,
        evaluationStartTime: null,
        bidEvaluationEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
      this.attachmentMap = {};
      this.projectInfoList = [];
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeEvaluationStartTime = [];
      this.daterangeBidEvaluationEndTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bidEvaluationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      this.title = "添加评标记录";
      this.$router.push('/bid/evaluation/add/0')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const bidEvaluationId = row.bidEvaluationId || this.ids
      this.$router.push('/bid/evaluation/add/' + bidEvaluationId+ '/false');
    },
    handleView(row) {
      this.reset();
      const bidEvaluationId = row.bidEvaluationId || this.ids
      this.$router.push('/bid/evaluation/add/' + bidEvaluationId+ '/true');
    },

    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 将表格数据添加到表单数据中
          this.form.projectInfoList = this.projectInfoList;
          this.form.attachments = [].concat(
            ...Object.values(this.attachmentMap)
          );
          if (this.form.bidEvaluationId != null) {
            updateEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addEvaluation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bidEvaluationIds = row.bidEvaluationId || this.ids;
      this.$modal.confirm('是否确认删除评标记录编号为"' + bidEvaluationIds + '"的数据项？').then(function() {
        return delEvaluation(bidEvaluationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bid/evaluation/export', {
        ...this.queryParams
      }, `evaluation_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
