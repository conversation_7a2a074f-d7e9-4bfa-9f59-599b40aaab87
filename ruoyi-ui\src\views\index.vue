<template>
  <div class="app-container home">
    <Purchaser
    :userInfo="userInfo"
      ref="purchaserRef"
      v-if="purchaserRefShow && roleGroup"
    ></Purchaser>
    <Supplier :userInfo="userInfo" ref="supplierRef" v-if="supplierRefShow"></Supplier>
    <Admin :userInfo="userInfo" ref="adminRef" v-if="adminRefShow"></Admin>
    <!-- <Purchaser  v-if="roleGroup === '超级管理员'"></Purchaser> -->
  </div>
</template>

<script>
import { getUserProfile } from "@/api/system/user";
export default {
  name: "Index",
  data() {
    return {
      userInfo:"",
      roleGroup: "",
      purchaserRefShow:false,
      supplierRefShow:false,
      adminRefShow:false
    };
  },
  created() {
    this.getUser();
  },
  activated() {
    // 当组件被激活时调用
    this.getUser();
  },
  mounted(){
  },
  methods: {
    getUser() {
      getUserProfile().then((response) => {
        console.info("----------------getUserProfile------------------")
        console.info(1111,response)
        console.info(response.data.admin)
        if( response.data.ent != null && response.data.ent.entStatus == 1){
           this.$router.push({ path: "/user/profile" }).catch(()=>{});
        }
        this.roleGroup = response.roleGroup;
        this.userInfo = response.data;
        if(response.roleGroup == "采购人" || response.roleGroup == "代理机构"){
          this.supplierRefShow = false;
          this.adminRefShow = false;
          this.initPurchaserDiv();
        }else if(response.roleGroup == "供应商"){
          this.purchaserRefShow = false;
          this.adminRefShow = false;
          this.initSupplierDiv();
        }else if(response.roleGroup == "超级管理员" || response.roleGroup == "小鹤智能"){
          this.purchaserRefShow = false;
          this.supplierRefShow = false;
          this.initAdminDiv();
        }
      });
    },
    initPurchaserDiv() {
      this.purchaserRefShow = true;
      // this.$nextTick(() => {
      //   this.$refs.purchaserRef.init();
      // });
    },
    initSupplierDiv() {
      this.supplierRefShow = true;
      // this.$nextTick(() => {
      //   this.$refs.supplierRef.init();
      // });
    },
    initAdminDiv() {
      this.adminRefShow = true;
      // this.$nextTick(() => {
      //   this.$refs.supplierRef.init();
      // });
    },
  },
};
</script>

<style scoped lang="scss">
.app-container {
  background: #f5f5f5;
  padding-top: 0;
}
</style>
