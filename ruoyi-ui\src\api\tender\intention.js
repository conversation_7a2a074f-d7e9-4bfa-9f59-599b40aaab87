import request from '@/utils/request'

// 查询采购意向列表
export function listIntention(query) {
  return request({
    url: '/tender/intention/list',
    method: 'get',
    params: query
  })
}

export function downLoadFile(params) {
  return request({
    url: '/tender/intention/downLoadTenderIntentionFile',
    method: 'get',
    params: params
  })
}
// 查询采购意向详细
export function getIntention(intentionId) {
  return request({
    url: '/tender/intention/' + intentionId,
    method: 'get'
  })
}

// 新增采购意向
export function addIntention(data) {
  return request({
    url: '/tender/intention',
    method: 'post',
    data: data
  })
}

// 修改采购意向
export function updateIntention(data) {
  return request({
    url: '/tender/intention',
    method: 'put',
    data: data
  })
}

// 删除采购意向
export function delIntention(intentionId) {
  return request({
    url: '/tender/intention/' + intentionId,
    method: 'delete'
  })
}
