<!-- 首页 -->
<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="home">
      <div class="expertReview">
        <div
          v-for="(item,index) in list"
          :key="index"
          class="item"
          style="cursor: pointer;"
          @click="enterReview(item)"
        >
          <el-card
            class="box-card"
            style="width: 100%;"
            shadow="hover"
          >
            <div style="font-size: 16px;font-weight: 600;">
              <div class="name">项目名称：{{ item.projectName }}</div>
              <el-divider></el-divider>
              <div style="display: grid;justify-content: center;">
                <div style="display: flex;justify-content: center;align-items: center;">采购方式：<dict-tag
                    style="color:#176ADB"
                    :options="dict.type.busi_tender_mode"
                    :value="item.tenderMode"
                  /></div>
                <div style="text-align:left;">预算价格：<span style="color:#176ADB">{{ item.budgetAmount }}元</span></div>
              </div>

            </div>
          </el-card>
        </div>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { allProject } from "@/api/expert/review";
import { addInfo } from "@/api/evaluation/info";
import { formatDate } from "@/utils/date";

export default {
  //import引入的组件需要注入到对象中才能使用
  dicts: ["busi_tender_mode"],
  components: {},
  data() {
    //这里存放数据
    return {
      list: [],
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    init() {
      allProject({
        zjhm: this.$route.query.zjhm,
      }).then((response) => {
        if (response.code == 200) {
          this.list = response.data;
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    enterReview(item) {
      const data = {
        projectId: item.projectId,
        evaluationTime: formatDate(new Date()),
        expertCode: this.$route.query.zjhm,
      };
      addInfo(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: { projectId: item.projectId, zjhm: this.$route.query.zjhm },
          });
        } else {
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.init();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.expertReview {
  // border: 1px dashed;
  display: grid;
  // 下面是重点
  grid-gap: 2%; // 卡片左右间隙
  grid-template-columns: repeat(auto-fill, 23%); // 自动填充一行的卡片个数
  justify-content: center; // 每行的卡片整体居中，但最后一行如果没有填充满会左对齐
  padding: 0 200px;
}
.home {
  background-color: #f5f5f5;
  min-height: 69vh;
  padding: 20px 0;
  line-height: 40px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 10px;
  height: 240px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
::v-deep .el-card__body {
  padding: 15px 0;
}
.box-card {
  height: 100%;
  width: 70%;
  padding: 20px 0;
  .name {
    padding: 0 20px;
    margin-bottom: 20px;
    font-family: SourceHanSansSC-Bold;
    font-weight: 700;
    font-size: 18px;
    color: #333333;
    letter-spacing: 0;
    line-height: 1.5;
    max-height: 3em;
    overflow: hidden;
    text-align: center;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  .el-divider {
    margin: 0 0 20px 0;
  }
}
.body {
  display: flex;
  flex-wrap: wrap;
}
</style>