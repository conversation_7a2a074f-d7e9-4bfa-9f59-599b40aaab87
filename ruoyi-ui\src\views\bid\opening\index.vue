<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="开标时间">
        <el-date-picker
          v-model="daterangeBidOpeningTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bid:opening:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bid:opening:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bid:opening:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['bid:opening:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="openingList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="投标记录id" align="center" prop="bidOpeningId" v-if=false />
      <el-table-column label="项目id" align="center" prop="projectId"  v-if=false />
      <el-table-column label="项目名称" align="center" prop="projectName" />
      <!-- <el-table-column label="开标方式" align="center" prop="bidOpeningMode"> -->
        <!-- <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_tender_bid_opening_mode" :value="scope.row.bidOpeningMode"/>
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="解密开始时间" align="center" prop="decodeStartTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.decodeStartTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="唱标时间" align="center" prop="bidAnnounceTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidAnnounceTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column> -->
      <el-table-column label="开标时间" align="center" prop="bidOpeningTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidOpeningTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="开标结束时间" align="center" prop="bidOpeningEndTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidOpeningEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleDetail(scope.row)"
          >查看</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bid:opening:edit']"
          >修改</el-button> -->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bid:opening:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改开标记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="主持人id" prop="compereId">
          <el-input v-model="form.compereId" placeholder="请输入主持人id" />
        </el-form-item>
        <el-form-item label="主持人名称" prop="compereName">
          <el-input v-model="form.compereName" placeholder="请输入主持人名称" />
        </el-form-item>
        <el-form-item label="主持人代码" prop="compereCode">
          <el-input v-model="form.compereCode" placeholder="请输入主持人代码" />
        </el-form-item>
        <el-form-item label="开标方式" prop="bidOpeningMode">
          <el-select v-model="form.bidOpeningMode" placeholder="请选择开标方式">
            <el-option
              v-for="dict in dict.type.busi_tender_bid_opening_mode"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="解密开始时间" prop="decodeStartTime">
          <el-date-picker clearable
            v-model="form.decodeStartTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择解密开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="唱标时间" prop="bidAnnounceTime">
          <el-date-picker clearable
            v-model="form.bidAnnounceTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择唱标时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开标时间" prop="bidOpeningTime">
          <el-date-picker clearable
            v-model="form.bidOpeningTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开标时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="开标结束时间" prop="bidOpeningEndTime">
          <el-date-picker clearable
            v-model="form.bidOpeningEndTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择开标结束时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOpening, getOpening, delOpening, addOpening, updateOpening } from "@/api/bid/opening";

export default {
  name: "Opening",
  dicts: ['busi_tender_bid_opening_mode', 'base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 开标记录表格数据
      openingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记，0正常，1删除时间范围
      daterangeDecodeStartTime: [],
      // 删除标记，0正常，1删除时间范围
      daterangeBidAnnounceTime: [],
      // 删除标记，0正常，1删除时间范围
      daterangeBidOpeningTime: [],
      // 删除标记，0正常，1删除时间范围
      daterangeBidOpeningEndTime: [],
      // 删除标记，0正常，1删除时间范围
      daterangeCreateTime: [],
      // 删除标记，0正常，1删除时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        compereId: null,
        compereName: null,
        compereCode: null,
        bidOpeningMode: null,
        decodeStartTime: null,
        bidAnnounceTime: null,
        bidOpeningTime: null,
        bidOpeningEndTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: { }
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {
    this.getList();
  },
  methods: {
    /** 查询开标记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDecodeStartTime && '' != this.daterangeDecodeStartTime) {
        this.queryParams.params["beginDecodeStartTime"] = this.daterangeDecodeStartTime[0];
        this.queryParams.params["endDecodeStartTime"] = this.daterangeDecodeStartTime[1];
      }
      if (null != this.daterangeBidAnnounceTime && '' != this.daterangeBidAnnounceTime) {
        this.queryParams.params["beginBidAnnounceTime"] = this.daterangeBidAnnounceTime[0];
        this.queryParams.params["endBidAnnounceTime"] = this.daterangeBidAnnounceTime[1];
      }
      if (null != this.daterangeBidOpeningTime && '' != this.daterangeBidOpeningTime) {
        this.queryParams.params["beginBidOpeningTime"] = this.daterangeBidOpeningTime[0];
        this.queryParams.params["endBidOpeningTime"] = this.daterangeBidOpeningTime[1];
      }
      if (null != this.daterangeBidOpeningEndTime && '' != this.daterangeBidOpeningEndTime) {
        this.queryParams.params["beginBidOpeningEndTime"] = this.daterangeBidOpeningEndTime[0];
        this.queryParams.params["endBidOpeningEndTime"] = this.daterangeBidOpeningEndTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listOpening(this.queryParams).then(response => {
        this.openingList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        bidOpeningId: null,
        projectId: null,
        compereId: null,
        compereName: null,
        compereCode: null,
        bidOpeningMode: null,
        decodeStartTime: null,
        bidAnnounceTime: null,
        bidOpeningTime: null,
        bidOpeningEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDecodeStartTime = [];
      this.daterangeBidAnnounceTime = [];
      this.daterangeBidOpeningTime = [];
      this.daterangeBidOpeningEndTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bidOpeningId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // this.open = true;
      // this.title = "添加开标记录";
      this.$router.push({
        path: "/bid/opening/add",
        query: {bidOpeningId:0}
      });
    },
    /** 查看按钮操作 */
    handleDetail(row) {
      this.reset();
      const bidOpeningId = row.bidOpeningId
      // this.open = true;
      // this.title = "添加开标记录";
      this.$router.push("/bid/opening/detail/"+bidOpeningId);
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const bidOpeningId = row.bidOpeningId || this.ids
      getOpening(bidOpeningId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改开标记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.bidOpeningId != null) {
            updateOpening(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOpening(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bidOpeningIds = row.bidOpeningId || this.ids;
      this.$modal.confirm('是否确认删除开标记录编号为"' + bidOpeningIds + '"的数据项？').then(function() {
        return delOpening(bidOpeningIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bid/opening/export', {
        ...this.queryParams
      }, `opening_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
