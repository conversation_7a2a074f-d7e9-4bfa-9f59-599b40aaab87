<template>
  <div class="foot">
    <el-row :gutter="32">
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="info">
          <el-row :gutter="32">
            <el-col :xs="8" :sm="8" :lg="8">
              <div class="address">
                <i
                  class="el-icon-location"
                  style="margin-right: 10px; color: #fff; width: 50px"
                ></i>
                <span>
                  地址：河南省鹤壁市淇滨区湘江路与钜新路交叉口东南角
                  创业创新园3号楼5楼
                </span>
              </div>
            </el-col>
            <el-col :xs="8" :sm="8" :lg="8">
              <div class="phone">
                <i class="el-icon-phone-outline" style="color: #fff"></i>
                电话：0392-2155585
              </div>
            </el-col>
            <el-col :xs="8" :sm="8" :lg="8">
              <div class="email">
                <i class="el-icon-message" style="color: #fff"></i>
                邮箱：<EMAIL>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="copyRight">
          Copyright © 2020 河南云中鹤大数据产业发展有限公司
          备案/许可证编号：豫ICP备2020025475
        </div>
      </el-col>
      <el-col :xs="24" :sm="24" :lg="24">
        <div class="record">
          <svg-icon style="margin-right: 10px;" icon-class="record" />
           豫公网安备 41061102000206号
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Foot",
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.foot {
  height: 140px;
  background: #176adb;
  padding: 15px 17%;

  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 15px;
  color: #ffffff;
  letter-spacing: 2.25px;
  .info {
    .address {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      justify-content: center;
    }
    .phone {
      text-align: center;
      border-right: #ffffff 1px solid;
      border-left: #ffffff 1px solid;
    }
    .email {
      text-align: center;
    }
    padding-bottom: 20px;
  }

  .copyRight {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row;
    padding-bottom: 10px;
  }
  .record {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: nowrap;
    flex-direction: row;
  }
}
</style>
