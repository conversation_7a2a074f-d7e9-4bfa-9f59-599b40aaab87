<template>
  <div class="container">
    <div class="transfer">
      <div style="text-align: right">
        <el-button
          class="item-button"
          style="background-color: #fff; height: 40px; color: #333"
          @click="open()"
          >新增</el-button
        >
      </div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column label="序号" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="factor" label="评审因素" width="180">
        </el-table-column>
        <el-table-column prop="content" label="评审内容"> </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button @click="handleClick(scope.row)" type="text" size="small"
              ><svg-icon icon-class="delete" class-name="delete-icon"
            /></el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <el-dialog title="新建评审因素" :visible.sync="dialogVisible" width="30%">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="评审因素" prop="factor">
          <el-input v-model="form.factor"></el-input>
        </el-form-item>
        <el-form-item label="评审内容" prop="content">
          <el-input v-model="form.content"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirm()">新 增</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/method/uinfo";

export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      tableData: [
        // {
        //   factor: "具有独立承担民事责任的能力",
        //   content:
        //     "有效营业执照复印件(注);\n供应商法定代表人身份证明和法定代表人授权代表委托书。\n不具有独立法人的分公司、办事处等分支机构不能参加谈判。",
        // },
        // {
        //   factor: "参加政府采购活动前三年内，在经营活动中没有重大违法记录",
        //   content: "提供供应商书面声明;",
        // },
        // {
        //   factor: "具有良好的商业信誉和健全的财务会计制度",
        //   content: "承诺书",
        // },
      ],
      dialogVisible: false,
      form: {
        factor: "",
        content: "",
      },
      rules: {
        factor: [
          { required: true, message: "请输入评审因素", trigger: "blur" },
        ],
        content: [
          { required: true, message: "请输入评审内容", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      console.log("qualification init start", projectInfo, itemInfo, uinfo);
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entMethodId && itemInfo.itemRemark) {
        this.tableData = JSON.parse(itemInfo.itemRemark);
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entMethodId) {
        this.itemInfo.entMethodId = uinfo.entMethodId;
      }
      console.log("qualification init end", this.itemInfo);
    },
    //删除
    handleClick(row) {
      this.tableData.splice(this.tableData.indexOf(row), 1);
      this.saveUinfo();
    },
    // 新建开标项
    open() {
      this.form.factor = "";
      this.form.content = "";
      this.dialogVisible = true;
    },
    // 确认
    handleConfirm() {
      let a = {
        factor: this.form.factor,
        content: this.form.content,
      };
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.tableData.push(a);
            this.saveUinfo();
            this.dialogVisible = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    saveUinfo() {
      const postData = {
        entMethodId: this.itemInfo.entMethodId,
        scoringMethodId: this.itemInfo.scoringMethodId,
        projectId: this.projectInfo.projectId,
        scoringMethodUitems: this.tableData.map(item=>{
          return {
            entMethodItemId: this.itemInfo.entMethodItemId,
            scoringMethodId: this.itemInfo.scoringMethodId,
            scoringMethodItemId: this.itemInfo.scoringMethodItemId,
            itemRemark: item.content,
            itemName: item.factor,
          }
        }),
        // [
        //   {
        //     entMethodItemId: this.itemInfo.entMethodItemId,
        //     scoringMethodId: this.itemInfo.scoringMethodId,
        //     scoringMethodItemId: this.itemInfo.scoringMethodItemId,
        //     itemRemark: JSON.stringify(this.tableData),
        //     itemName: this.itemInfo.itemName,
        //   },
        // ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}
.transfer {
  width: 70%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
