<template>
  <div class="info-container">
    <div class="center-box">
      <h1 class="text-2xl font-bold text-gray-800 mb-6 text-center">中标通知书</h1>
      <div class="grid grid-cols-1 gap-4">
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">项目编号:</span>
          <span class="value text-gray-800">{{ formData.projectNumber }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">项目名称:</span>
          <span class="value text-gray-800">{{ formData.projectName }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">采购单位:</span>
          <span class="value text-gray-800">{{ formData.tendererName }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">采购人:</span>
          <span class="value text-gray-800">{{ formData.tendererContactPerson }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">采购人联系方式:</span>
          <span class="value text-gray-800">{{ formData.tendererPhone }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">代理机构:</span>
          <span class="value text-gray-800">{{ formData.agencyName ? formData.agencyName : '无' }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">代理机构联系人:</span>
          <span class="value text-gray-800">{{ formData.agencyContactPerson ? formData.agencyContactPerson : '无' }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">代理机构联系方式:</span>
          <span class="value text-gray-800">{{ formData.agencyPhone? formData.agencyPhone : '无'  }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">中标人:</span>
          <span class="value text-gray-800">{{ formData.winningBidder }}</span>
        </div>
        <div class="flex items-center">
          <span class="label w-32 text-gray-600 text-left">中标金额:</span>
          <span class="value text-gray-800">{{ formData.winningAmount }}</span>
        </div>
      </div>
      <div class="download-link-container mt-8 text-left">
        <a :href="formData.downloadLink" target="_blank" class="text-blue-500 hover:underline">中标通知书下载链接</a>
      </div>
    </div>
  </div>
</template>

<script>
import { getProjectCodePage } from "@/api/tender/project";

export default {
  name: 'QrCodeResult',
  data() {
    return {
      info: null,
      qrcodeInfo: null,
      formData: {
        winningBidder: '',
        winningAmount: '',
        projectNumber: '',
        projectName: '',
        downloadLink: '',
        tendererName: '',
        tendererContactPerson: '',
        tendererPhone: '',
        agencyName: '',
        agencyContactPerson: '',
        agencyPhone: '',
      },
    };
  },
  created() {
    this.qrcodeInfo = this.$route.params.qrcodeInfo;
    console.log(this.qrcodeInfo);
    this.getProjectCodePage(this.qrcodeInfo);
  },
  methods: {
    getProjectCodePage(qrcodeInfo) {
      getProjectCodePage(qrcodeInfo).then((response) => {
        // 中标人 - 金额
        this.formData.winningBidder = response.data.bidderNoticeServiceOne.bidderName;
        this.formData.winningAmount = response.data.bidderNoticeServiceOne.bidAmount;
        // 项目编号 - 项目名称
        this.formData.projectNumber = response.data.tenderProject.projectCode;
        this.formData.projectName = response.data.tenderProject.projectName;
        // 采购人单位名称 -- 采购人 -- 采勾人联系方式
        this.formData.tendererName = response.data.tenderProject.tendererName;
        this.formData.tendererContactPerson = response.data.tenderProject.tendererContactPerson;
        this.formData.tendererPhone = response.data.tenderProject.tendererPhone;
        // 代理机构
        this.formData.agencyName = response.data.tenderProject.agencyName;
        this.formData.agencyContactPerson = response.data.tenderProject.agencyContactPerson;
        this.formData.agencyPhone = response.data.tenderProject.agencyPhone;
        // 下载链接
        this.formData.downloadLink = response.data.downUrl;
      });
    }
  }
};
</script>

<style scoped>
.info-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f9f9f9;
}

.center-box {
  width: 80%;
  max-width: 600px;
  background-color: white;
  padding: 20px;
  border: 1px solid #ccc; /* 添加边框形成方框 */
  border-radius: 5px;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
.download-link-container{
  color: blue;
}
.label {
  display: inline-block;
  width: 200px;
  color: #666;
}

.value {
  color: #333;
}

.download-link-container {
  text-align: center;
  margin-top: 20px;
}

.items-center{
  margin: 5px auto;
}
</style>
