<template>
  <div
    class="committee"
    v-loading="loading"
  >
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      size="medium"
      label-width="0"
      label-position="left"
    >
<!--
      <el-card class="box-card">
        <div
          slot="header"
          class="clearfix"
        >
          <span><i class="el-icon-suitcase"></i>项目信息</span>
        </div>
        <div class="el-table el-table&#45;&#45;enable-row-hover el-table&#45;&#45;medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>项目
                  </div>
                </td>
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-form-item prop="projectId">
                      <el-select
                        ref="selectProject"
                        v-model="formData.projectId"
                        placeholder="请选择"
                        clearable
                        :style="{ width: '50%' }"
                        @change="change($event)"
                        @clear="clear()"
                      >
                        <el-option
                          v-for="(item, index) in projectIdOptions"
                          :key="index"
                          :label="item.projectName"
                          :value="item.projectId"
                          :disabled="item.disabled"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>项目编号
                  </div>
                </td>
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-form-item>
                      <template>
                        <el-input
                          disabled
                          v-model="projectCode"
                          :style="{ width: '100%' }"
                        >
                        </el-input>
                      </template>
                    </el-form-item>
                  </div>
                </td>

              </tr>
              <tr>
                &lt;!&ndash; 新增抽取申请类型字段的表单项 &ndash;&gt;
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>抽取申请类型
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item prop="extraction_type">
                        <el-select
                          v-model="formData.extraction_type"
                          placeholder="请选择抽取申请类型"
                          clearable
                          :style="{ width: '100%' }"
                        >
                          <el-option
                            label="论证(建项目)"
                            value="0"
                          ></el-option>
                          <el-option
                            label="评审（评审项目）"
                            value="1"
                          ></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>抽取类型
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-form-item prop="applyMethod">
                      <el-select
                        v-model="formData.applyMethod"
                        placeholder="请选择抽取方式"
                        :style="{ width: '100%' }"
                      >
                        <el-option
                          v-for="dict in dict.type.busi_apply_method"
                          :key="dict.value"
                          :label="dict.label"
                          :value="parseInt(dict.value)"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </td>
              </tr>
              <tr>

                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>委员会总人数
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-form-item prop="expertNumber">
                      <el-input-number
                        v-model="formData.expertNumber"
                        :style="{ width: '50%' }"
                        :min="0"
                        :step="1"
                        step-strictly
                      ></el-input-number>
                    </el-form-item>
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>采购人代表数量
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-form-item prop="ownerNumber">
                      <el-input-number
                        v-model="formData.ownerNumber"
                        :style="{ width: '50%' }"
                        :min="0"
                        :step="1"
                        step-strictly
                      ></el-input-number>
                    </el-form-item>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
-->
      <el-card class="box-card custom-card">
        <!-- 卡片头部 -->
        <div slot="header" class="card-header">
          <i class="el-icon-suitcase header-icon"></i>
          <span class="header-title">项目信息</span>
        </div>

        <!-- 卡片内容区域 -->
        <div class="card-content">
          <div class="form-row">
            <!-- 抽取申请类型 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>抽取申请类型
              </label>
              <el-form-item prop="extractionType">
                <el-select
                  v-model="formData.extractionType"
                  placeholder="请选择抽取申请类型"
                  clearable
                  class="custom-select"
                  @change="handleExtractionTypeChange"
                >
                  <el-option label="论证(建项目)" value="0"></el-option>
                  <el-option label="评审（评审项目）" value="1"></el-option>
                </el-select>
              </el-form-item>
            </div>

            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>占用时间段
              </label>
              <el-form-item prop="occupationTime">
                <el-select
                  v-model="formData.occupationTime"
                  placeholder="请选择占用时间段"
                  clearable
                  class="custom-select"
                >
                  <el-option label="上午" value="0"></el-option>
                  <el-option label="下午" value="1"></el-option>
                  <el-option label="全天" value="2"></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- 项目选择 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>项目
              </label>
              <el-form-item prop="projectId">
                <el-select
                  ref="selectProject"
                  v-model="formData.projectId"
                  placeholder="请选择项目"
                  clearable
                  class="custom-select"
                  :disabled="!formData.extractionType"
                  @change="change($event)"
                  @clear="clear()"
                >
                  <el-option
                    v-for="(item, index) in projectIdOptions"
                    :key="index"
                    :label="item.projectName"
                    :value="item.projectId"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>
            <!-- 项目编号 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>项目编号
              </label>
              <el-form-item>
                <el-input
                  disabled
                  v-model="projectCode"
                  placeholder="项目编号自动填充"
                  class="custom-input"
                />
              </el-form-item>
            </div>


          </div>

          <div class="form-row">
            <!-- 抽取类型 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>抽取类型
              </label>
              <el-form-item prop="applyMethod">
                <el-select
                  v-model="formData.applyMethod"
                  placeholder="请选择抽取方式"
                  clearable
                  class="custom-select"
                >
                  <el-option
                    v-for="dict in dict.type.busi_apply_method"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  ></el-option>
                </el-select>
              </el-form-item>
            </div>

            <!-- 委员会总人数 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>委员会总人数
              </label>
              <el-form-item prop="expertNumber">
                <el-input-number
                  v-model="formData.expertNumber"
                  :min="0"
                  :step="1"
                  step-strictly
                  class="custom-input-number"
                />
              </el-form-item>
            </div>

            <!-- 采购人代表数量 -->
            <div class="form-group">
              <label class="form-label">
                <strong class="required-mark">*</strong>采购人代表数量
              </label>
              <el-form-item prop="ownerNumber">
                <el-input-number
                  v-model="formData.ownerNumber"
                  :min="0"
                  :step="1"
                  step-strictly
                  class="custom-input-number"
                />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-card>
      <el-row>
        <el-col :span="24">
          <div style="height: 20px"></div>
        </el-col>
      </el-row>
      <el-card class="box-card">
        <div
          slot="header"
          class="clearfix"
        >
          <span><i class="el-icon-suitcase"></i>专家信息</span>
          <el-button
            v-if="formData.applyMethod == 0"
            style="float: right"
            type="primary"
            class="makeTenserFile"
            @click="handleAdd(1)"
          >
            新增专家组
          </el-button>
          <el-button
            v-if="formData.applyMethod == 1"
            style="float: right"
            type="primary"
            class="makeTenserFile"
            @click="handleAdd(4)"
          >
            选择专家
          </el-button>
        </div>
        <el-row>
          <el-col :span="24">
            <div style="height: 10px"></div>
          </el-col>
        </el-row>
        <el-row v-if="formData.applyMethod == 0">
          <el-col :span="24">
            <el-table
              :data="expertGroup"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="groupName"
                label="专家组名称"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="groupAddress"
                label="评审品目"
              >
              </el-table-column>
              <el-table-column
                prop="expertNumber"
                label="专家数量"
              >
              </el-table-column>
              <el-table-column
                label="操作"
                width="150"
              >
                <template slot-scope="scope">
                  <el-button
                    @click="expertHandleDelete(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >删除</el-button>
                  <el-button
                    @click="expertHandleModify(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
        <el-row v-if="formData.applyMethod == 1">
          <el-col :span="24">
            <el-table
              :data="expertTable"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="xm"
                label="姓名"
                width="120"
              >
              </el-table-column>
              <el-table-column
                prop="pspm"
                label="专家类别"
              >
                <!--              <template slot-scope="scope">-->
                <!--                <dict-tag-->
                <!--                  :options="dict.type.busi_expert_type"-->
                <!--                  :value="scope.row.expertType"-->
                <!--                ></dict-tag>-->
                <!--              </template>-->
              </el-table-column>
              <el-table-column
                prop="zc"
                label="职称等级"
              >
                <!--              <template slot-scope="scope">-->
                <!--                <dict-tag-->
                <!--                  :options="dict.type.busi_expert_titles_grade"-->
                <!--                  :value="scope.row.expertType"-->
                <!--                ></dict-tag>-->
                <!--              </template>-->
              </el-table-column>
              <el-table-column
                prop="xgzdw"
                label="现工作单位"
              >
              </el-table-column>
              <el-table-column
                prop="sjhm"
                label="联系方式"
                width="150"
              >
              </el-table-column>
              <el-table-column
                label="操作"
                width="100"
              >
                <template slot-scope="scope">
                  <el-button
                    @click="manualExpertHandleDelete(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>

      <el-row>
        <el-col :span="24">
          <div style="height: 20px"></div>
        </el-col>
      </el-row>
      <el-card class="box-card">
        <div
          slot="header"
          class="clearfix"
        >
          <span><i class="el-icon-suitcase"></i>采购人代表信息</span>
          <el-button
            style="float: right"
            type="primary"
            class="makeTenserFile"
            @click="handleAdd(5)"
          >
            新增采购人代表
          </el-button>
        </div>
        <el-row>
          <el-col :span="24">
            <div style="height: 10px"></div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              :data="yzpwGroup"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="yzpwName"
                label="姓名"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="yzpwPhone"
                label="联系电话"
              >
              </el-table-column>
              <el-table-column
                prop="yzpwId"
                label="身份证号"
              >
              </el-table-column>
              <el-table-column
                label="操作"
                width="150"
              >
                <template slot-scope="scope">
                  <el-button
                    @click="yzpwHandleDelete(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >删除</el-button>
                  <el-button
                    @click="yzpwHandleModify(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>

      <el-row>
        <el-col :span="24">
          <div style="height: 20px"></div>
        </el-col>
      </el-row>
      <el-card class="box-card">
        <div
          slot="header"
          class="clearfix"
        >
          <span><i class="el-icon-suitcase"></i>回避单位</span>
          <el-button
            style="float:right"
            type="primary"
            class="makeTenserFile"
            @click="handleAdd(2)"
          >
            新增回避单位
          </el-button>
        </div>
        <el-row>
          <el-col :span="24">
            <div style="height: 10px"></div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              :data="avoidanceUnit"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="evadeName"
                label="单位名称"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="evadeReason"
                label="回避原因"
              >
              </el-table-column>
              <el-table-column
                label="操作"
                width="150"
              >
                <template slot-scope="scope">
                  <el-button
                    @click="evadeUnitHandleDelete(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >删除</el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="evadeHandleModify(scope.row, scope.$index)"
                  >修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>
      <el-row>
        <el-col :span="24">
          <div style="height: 10px"></div>
        </el-col>
      </el-row>
      <el-card class="box-card">
        <div
          slot="header"
          class="clearfix"
        >
          <span><i class="el-icon-suitcase"></i>回避专家</span>
          <el-button
            style="float:right"
            type="primary"
            class="makeTenserFile"
            @click="handleAdd(3)"
          >
            新增回避专家
          </el-button>
        </div>
        <el-row>
          <el-col :span="24">
            <div style="height: 10px"></div>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-table
              :data="avoidanceExpert"
              border
              style="width: 100%"
            >
              <el-table-column
                label="序号"
                type="index"
                width="100"
              >
              </el-table-column>
              <el-table-column
                prop="evadeName"
                label="专家名称"
                width="180"
              >
              </el-table-column>
              <el-table-column
                prop="evadeReason"
                label="回避原因"
              >
              </el-table-column>
              <el-table-column
                label="操作 "
                width="150"
              >
                <template slot-scope="scope">
                  <el-button
                    @click="evadeExpertHandleDelete(scope.row, scope.$index)"
                    type="text"
                    size="small"
                  >删除</el-button>
                  <el-button
                    type="text"
                    size="small"
                    @click="evadeHandleModify(scope.row, scope.$index)"
                  >修改</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-card>
      <el-row>
        <el-col :span="24">
          <div style="height: 20px"></div>
        </el-col>
      </el-row>
    </el-form>
    <div
      slot="footer"
      class="option"
    >
      <el-button
        type="success"
        @click="submitForm(1)"
      >提交</el-button>
      <!-- <el-button type="primary" @click="submitForm(0)">暂存</el-button> -->
      <el-button @click="resetForm">重置</el-button>
    </div>
    <!-- 添加或修改专家组对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      title="新增专家组"
      :visible.sync="open"
      width="80%"
      append-to-body
    >
      <el-form
        ref="expertForm"
        :model="expertForm"
        :rules="expertRules"
        label-width="120px"
      >
        <el-form-item
          label="专家组名称"
          prop="groupName"
        >
          <el-input
            v-model="expertForm.groupName"
            placeholder="请输入专家组名称"
          />
        </el-form-item>

        <!--        <el-form-item label="专家专业" prop="expertClassificationCode">
          <el-cascader
            ref="expertClassificationCode"
            style="width: 100%"
            filterable
            clearable
            :props="{  ...cascaderSetting, expandTrigger: 'hover' }"
            v-model="expertForm.expertClassificationCode"
            :options="getTreeDataOptions(7)"
            @change="expertClassificationCodeChange($event)"
          ></el-cascader>
        </el-form-item>-->
        <!--        <el-form-item label="专业类别" prop="expertType">
          <el-select
              v-model="expertForm.expertType"
              placeholder="请选择专业类别"
              @change="expertTypeChange($event)"
          >
            <el-option
                v-for="dict in dict.type.busi_expert_type"
                :key="dict.value"
                :label="dict.label"
                :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>-->
        <el-form-item
          label="评审品目"
          prop="pspm"
        >
          <!--          <el-select
            v-model="expertForm.expertType"
            placeholder="请选择评审品目"
            @change="expertTypeChange($event)"
          >
            <el-option
              v-for="dict in dict.type.busi_expert_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>-->
          <el-cascader
            ref="groupAddress"
            style="width: 100%"
            filterable
            clearable
            props.checkStrictly="true"
            :props="{ ...cascaderSetting , expandTrigger: 'hover'}"
            v-model="expertForm.groupAddress"
            :options="getTreeDataOptions(2)"
            @change="groupAddressChange($event)"
          ></el-cascader>
        </el-form-item>

        <el-form-item
          label="抽取专家人数"
          prop="expertNumber"
        >
          <el-input-number
            v-model="expertForm.expertNumber"
            :style="{ width: '50%' }"
            :min="0"
            :max="expertNumber"
            :step="1"
            step-strictly
          ></el-input-number>
          <el-tag
            style="margin-left: 20px"
            type="warning"
          >可抽取专家个数：{{ expertNumber }}</el-tag>
        </el-form-item>
        <!--        <el-form-item label="评审区域" prop="groupAddress">
          <el-cascader
            ref="groupAddress"
            style="width: 100%"
            filterable
            clearable
            props.checkStrictly="true"
            :props="{ checkStrictly: true, ...cascaderSetting }"
            v-model="expertForm.groupAddress"
            :options="getTreeDataOptions(9)"
            @change="groupAddressChange($event)"
          ></el-cascader>
        </el-form-item>-->
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="expertSubmitForm"
        >确 定</el-button>
        <el-button @click="expertCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改专家抽取回避对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      title="回避条件"
      :visible.sync="openEvade"
      width="80%"
      append-to-body
    >
      <el-form
        ref="evadeForm"
        :model="evadeForm"
        :rules="evadeRules"
        label-width="80px"
      >
        <el-form-item
          :label="evadeForm.evadeType == 1 ? '专家名称' : '单位名称'"
          prop="evadeName"
        >
          <el-input
            v-model="evadeForm.evadeName"
            placeholder="请输入回避名称"
          />
        </el-form-item>
        <el-form-item
          label="回避类型"
          prop="evadeType"
          v-show="false"
        >
          <el-select
            v-model="evadeForm.evadeType"
            placeholder="请选择回避类型"
          >
            <el-option
              v-for="dict in dict.type.busi_expert_evade_type"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="回避原因"
          prop="evadeReason"
        >
          <el-input
            v-model="evadeForm.evadeReason"
            placeholder="请输入回避原因"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="evadeSubmitForm"
        >确 定</el-button>
        <el-button @click="evadeCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 自主添加专家对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      title="自主抽取专家"
      :visible.sync="openExpert"
      width="80%"
      append-to-body
    >
      <el-select
          ref="manualExpert"
          v-model="manualExpert"
          placeholder="请选择专家"
          style="width: 100%"
          :filterable="true"
      :filter-method="filterExperts"
      >
      <el-option
          v-for="item in filteredExpertOptions"
      :key="item.id"
      :label="item.xm+'（'+item.sjhm+'）'"
      :value="item.id"
      >
      </el-option>
      </el-select>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="manualExpertSubmitForm"
        >确 定</el-button>
        <el-button @click="manualExpertCancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改采购人代表信息对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      title="采购人代表信息"
      :visible.sync="openyzpw"
      width="80%"
      append-to-body
    >
      <el-form
        ref="yzpwForm"
        :model="yzpwForm"
        :rules="yzpwRules"
        label-width="80px"
      >
        <el-form-item
          label="姓名"
          prop="yzpwName"
        >
          <el-input
            v-model="yzpwForm.yzpwName"
            placeholder="请输入姓名"
          />
        </el-form-item>
        <el-form-item
          label="联系方式"
          prop="yzpwPhone"
        >
          <el-input
            v-model="yzpwForm.yzpwPhone"
            placeholder="请输入联系方式"
          />
        </el-form-item>
        <el-form-item
          label="身份证号"
          prop="yzpwId"
        >
          <el-input
            v-model="yzpwForm.yzpwId"
            placeholder="请输入身份证号"
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="yzpwSubmitForm"
        >确 定</el-button>
        <el-button @click="yzpwCancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {listProject, listProjectByExtractionType} from "@/api/tender/project";
import { getcgxhzj, listInfo } from "@/api/expert/info";
import { getApply, addApply, updateApply } from "@/api/expert/apply";
import { listGroup } from "@/api/expert/group";
import { listEvade } from "@/api/expert/evade";

export default {
  components: {},
  props: [],
  dicts: [
    "busi_expert_type",
    "base_yes_no",
    "busi_expert_evade_type",
    "busi_apply_method",
    "busi_expert_titles_grade",
  ],
  data() {
    return {
      // 可抽取专家个数
      expertNumber: 0,
      //树形字典
      dataTreeOptionsMap: new Map(),
      dataTreeMap: new Map(),
      // 遮罩层
      open: false,
      openEvade: false,
      openExpert: false,
      isModificationOperation: false,
      loading: true,
      cascaderSetting: { value: "id", label: "name", children: "childrens" },
      formData: {
        projectId: "",
        expertNumber: "",
        ownerNumber: 0,
        applyMethod: "",
        purchaserDelegateNumber: 0,
        applyStatus: "",
        occupationTime: null,
        extractionType:null
      },
      projectCode: "",
      rules: {
        projectId: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        expertNumber: [
          {
            required: true,
            message: "请填写抽取专家人数",
            trigger: "blur",
          },
        ],
        applyMethod: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        purchaserDelegateNumber: [
          {
            required: true,
            message: "",
            trigger: "blur",
          },
        ],
        // 新增：占用时间段验证规则
        occupationTime: [
          {
            required: true,
            message: "请选择占用时间段",
            trigger: "change"
          }
        ]
      },
      projectIdOptions: [],
      manualExpert: '',  // 绑定的选中值
      expertOptions: [],  // 原始的专家选项数据列表，假设从接口等获取后赋值到这里
      filteredExpertOptions: [],  // 经过筛选后的专家选项数据列表，用于展示在下拉框里
      expertGroup: [],
      expertTable: [],
      expertInfo: [],
      avoidanceUnit: [],
      avoidanceExpert: [],
      expertForm: {
        groupName: "",
        expertNumber: "",
        expertClassificationCode: "",
        expertClassificationName: "",
        expertType: "",
        groupAddress: "",
        groupAddressStr: "",
      },
      evadeForm: {
        evadeName: "",
        evadeType: "",
        evadeReason: "",
      },
      // 表单校验
      expertRules: {
        groupName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        expertNumber: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        expertClassificationCode: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        // expertType: [
        //   {
        //     required: true,
        //     message: "请选择",
        //     trigger: "change",
        //   },
        // ],
        groupAddress: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      evadeRules: {
        evadeName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        evadeReason: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      openyzpw: false,
      yzpwGroup: [],
      yzpwForm: {
        yzpwName: "",
        yzpwPhone: "",
        yzpwId: "",
      },
      yzpwRules: {
        yzpwName: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        yzpwPhone: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
        yzpwId: [
          {
            required: true,
            message: "请输入",
            trigger: "blur",
          },
        ],
      },
      groupIds: [],
      unitIds: [],
      expertIds: [],
      manualExpertIds: [],
      yzpwIds: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.treeDict
      .getdataTreeOptionsMap({ isEnabled: 1 })
      .then((result) => {
        this.dataTreeOptionsMap = result;
        return this.treeDict.getdataTreeListMap({ isEnabled: 1 });
      })
      .then((result) => {
        this.dataTreeMap = result;
       // return this.getList();
      })
      .catch((err) => {
        console.error(" expert/info init err ", err);
      });
  },
  mounted() {
    this.getList().then(() => {
      if (this.$route.query.projectId) {
        this.formData.projectId = parseInt(this.$route.query.projectId);
        this.$refs.selectProject.$emit("change", this.formData.projectId);
      }
    });
    this.filteredExpertOptions = this.expertOptions;  // 初始化展示的筛选后数据为全部数据

  },
  methods: {
    // 新增抽取申请类型变更处理方法
    handleExtractionTypeChange(value) {
      this.formData.extractionType = value;
      // 根据选择的抽取申请类型获取项目信息
      this.fetchProjectsByExtractionType(value);

      // 如果从评审切换到论证，清空时间段选择
      // if (value === '0' && !this.formData.occupationTime) {
      //   this.formData.occupationTime = '';
      // }
    },
    // 根据抽取申请类型获取项目信息
    fetchProjectsByExtractionType(extractionType) {
      this.loading = true;
      // 调用API获取项目信息，传入extractionType参数
      listProjectByExtractionType({
        delFlag: 0,
        projectStatus: 20,
        extractionType: extractionType // 假设API支持根据抽取类型过滤项目
      }).then((response) => {
        this.projectIdOptions = response.data;
        // 清空已选择的项目
        this.formData.projectId = "";
        this.projectCode = "";
      }).catch(error => {
        console.error('获取项目信息失败:', error);
        this.$message.error('获取项目信息失败');
      }).finally(() => {
        this.loading = false;
      });
    },
    filterExperts(query) {
      if (query === '') {
        this.filteredExpertOptions = this.expertOptions;
      } else {
        this.filteredExpertOptions = this.expertOptions.filter(item => {
          const searchText = query.toLowerCase();
          return item.xm.toLowerCase().includes(searchText) ||
              item.sjhm.toLowerCase().includes(searchText);
        });
      }
    },
    /** 从map取值 */
    getTreeDataOptions(key) {
      return this.dataTreeOptionsMap.get(key)
        ? this.dataTreeOptionsMap.get(key).childrens
        : [];
    },
    async getList() {
      this.loading = true;
     /* await listProject({ delFlag: 0, projectStatus: 20 }).then((response) => {
        this.projectIdOptions = response.rows;
      });*/
      /* 修改采用采购协会专家信息
      await listInfo().then((response) => {
        this.expertOptions = response.rows;
      });*/
      await getcgxhzj().then((response) => {
        console.log(response.data.data.experts);
        this.expertOptions = response.data.data.experts;
        this.filteredExpertOptions = this.expertOptions;  // 初始化展示的筛选后数据为全部数据

        console.log("response", response);
      });
      // 初始化
      if (this.$route.params.applyId != 0 && this.$route.params.applyId) {
        // 专家抽取申请表
        await getApply(this.$route.params.applyId).then((response) => {
          this.forformDatam = response.data;
        });
        // 专家组
        await listGroup({ applyId: this.$route.params.applyId }).then(
          (response) => {
            this.expertGroup = response.rows;
          }
        );
        // 回避单位
        await listEvade({
          applyId: this.$route.params.applyId,
          evadeType: 0,
        }).then((response) => {
          this.avoidanceUnit = response.rows;
        });
        // 回避专家
        await listEvade({
          applyId: this.$route.params.applyId,
          evadeType: 1,
        }).then((response) => {
          this.avoidanceExpert = response.rows;
        });
      }
      this.loading = false;
    },
    change(value) {
      if (value) {
        this.projectCode = value;
      } else {
        this.projectCode = "";
      }
    },
    clear() {
      this.projectCode = "";
    },
    submitForm(value) {
      this.$refs["elForm"].validate((valid) => {
        if (!valid) return;
        if (value == 0) {
          this.formData.applyStatus = 0;
        } else {
          this.formData.applyStatus = 1;
        }
        if (this.$route.params.applyId == 0) {
          if (this.formData.applyMethod == 0) {
            this.formData.groups = this.expertGroup;

            let expertGroupSize = this.expertGroup.reduce((total, item) => {
              return total + item.expertNumber;
            }, 0);
            if (
              this.formData.ownerNumber + expertGroupSize !==
              this.formData.expertNumber
            ) {
              this.$modal.msgError(
                "抽取专家人数不等于采购人代表数量与专家数量总和"
              );
              return;
            }
            if (this.formData.groups.length <= 0) {
              this.$modal.msgError("专家组不能为空");
              return;
            }
          } else if (this.formData.applyMethod == 1) {
            this.formData.results = this.expertTable;
            if (
              this.formData.ownerNumber + this.expertTable.length !==
              this.formData.expertNumber
            ) {
              this.$modal.msgError(
                "抽取专家人数不等于采购人代表数量与专家数量总和"
              );
              return;
            }
            if (this.formData.results.length <= 0) {
              this.$modal.msgError("专家列表不能为空");
              return;
            }
          }
          this.formData.evades = [
            ...this.avoidanceUnit,
            ...this.avoidanceExpert,
          ];
          this.formData.yzpw = this.yzpwGroup;

          if (this.formData.expertNumber % 2 !== 1) {
            this.$modal.msgError("抽取专家人数应不是奇数");
            return;
          }
          // 新增专家抽取申请
          addApply(this.formData).then((response) => {
            this.$modal.msgSuccess("新增成功");
            this.close();
          });
        } else {
          if (this.formData.applyMethod == 0) {
            this.formData.groups = this.expertGroup;
          } else if (this.formData.applyMethod == 1) {
            this.formData.results = this.expertTable;
          }
          this.formData.evades = [
            ...this.avoidanceUnit,
            ...this.avoidanceExpert,
          ];
          this.formData.yzpw = this.yzpwGroup;
          // 更新专家抽取申请
          updateApply(this.formData).then((response) => {
            this.$modal.msgSuccess("更新成功");
            this.close();
          });
        }
      });
    },
    resetForm() {
      this.$refs["elForm"].resetFields();
    },
    // 专家组表单重置
    expertReset() {
      this.expertForm = {
        groupName: null,
        expertNumber: null,
        expertClassificationCode: null,
        expertType: null,
        groupAddress: null,
      };
    },
    // 回避条件表单重置
    evadeReset() {
      this.evadeForm = {
        evadeName: null,
        evadeType: null,
        evadeReason: null,
      };
    },
    // 采购人代表表单重置
    yzpwReset() {
      this.yzpwRules = {
        yzpwName: null,
        yzpwPhone: null,
        yzpwId: null,
      };
    },
    // 新增
    handleAdd(num) {
      console.log(num);
      this.expertReset();
      this.isModificationOperation = false;
      //新增专家组1，新增回避单位是2，新增回避专家是3，选择专家是4
      if (num == 1) {
        //this.expertNumber = 0;
        getcgxhzj().then((response) => {
          console.log(response.data.data.expertCount);
          this.expertNumber = response.data.data.expertCount;
        });

        this.open = true;
      } else if (num == 2) {
        this.evadeForm.evadeType = 0;
        this.openEvade = true;
      } else if (num == 3) {
        this.evadeForm.evadeType = 1;
        this.openEvade = true;
      } else if (num == 4) {
        this.openExpert = true;
      } else if (num == 5) {
        this.openyzpw = true;
      }
    },
    // 取消按钮
    expertCancel() {
      this.open = false;
      this.expertReset();
    },
    evadeCancel() {
      this.openEvade = false;
      this.evadeReset();
    },
    manualExpertCancel() {
      this.openExpert = false;
      this.manualExpert = null;
    },
    yzpwCancel() {
      this.openyzpw = false;
      this.yzpwReset();
    },
    /** 提交按钮 */
    expertSubmitForm() {
      this.$refs["expertForm"].validate((valid) => {
        if (valid) {
          // this.expertForm.expertClassificationCode =this.expertForm.expertClassificationCode.join(",");
          let nodeInfo = this.$refs["groupAddress"].getCheckedNodes();
          const pathString = nodeInfo[0].pathNodes
            .map((node) => node.label)
            .join("/");
          this.expertForm.groupAddress = pathString;
          // this.expertForm.expertType = this.expertForm.groupAddress.join(",");
          // 修改
          if (this.isModificationOperation) {
            const index = this.expertGroup.findIndex(
              (group) => group.groupName === this.expertForm.groupName
            );
            if (index !== -1) {
              // 更新找到的对象内容为 expertForm 的最新内容
              this.$set(this.expertGroup, index, { ...this.expertForm }); // 使用 $set 方法触发响应式更新
              this.open = false;
            }
          } else {
            this.expertGroup.push(this.expertForm);
            this.open = false;
          }
          // 新增
        }
      });
    },
    evadeSubmitForm() {
      this.$refs["evadeForm"].validate((valid) => {
        if (valid) {
          if (this.isModificationOperation) {
            if (this.evadeForm.evadeType == 0) {
              // 修改回避单位信息
              const index = this.avoidanceUnit.findIndex(
                (group) => group.evadeName === this.evadeForm.evadeName
              );
              if (index !== -1) {
                // 更新找到的对象内容为 evadeForm 的最新内容
                this.$set(this.avoidanceUnit, index, { ...this.evadeForm }); // 使用 $set 方法触发响应式更新
                this.openEvade = false;
              }
            } else {
              // 修改回避专家信息
              const index = this.avoidanceExpert.findIndex(
                (group) => group.evadeName === this.evadeForm.evadeName
              );
              if (index !== -1) {
                // 更新找到的对象内容为 evadeForm 的最新内容
                this.$set(this.avoidanceExpert, index, { ...this.evadeForm }); // 使用 $set 方法触发响应式更新
                this.openEvade = false;
              }
            }
          } else {
            if (this.evadeForm.evadeType == 0) {
              // 新增回避单位信息
              this.avoidanceUnit.push(this.evadeForm);
              this.openEvade = false;
            } else {
              // 回避专家信息
              this.avoidanceExpert.push(this.evadeForm);
              this.openEvade = false;
            }
          }
        }
        this.evadeReset();
      });
    },
    manualExpertSubmitForm() {
      console.log("manualExpert",this.manualExpert);

      if (this.manualExpert) {
        for(let key in this.expertOptions){
          let expert =  this.expertOptions[key];
          if(expert.id == this.manualExpert){

            this.expertTable.push(expert);
          }
        }
        this.openExpert = false;
        this.manualExpert = null;
      }
    },
    yzpwSubmitForm() {
      this.$refs["yzpwForm"].validate((valid) => {
        if (valid) {
          if (this.isModificationOperation) {
            // 修改回避单位信息
            const index = this.yzpwGroup.findIndex(
              (group) => group.yzpwId === this.yzpwForm.yzpwId
            );
            if (index !== -1) {
              // 更新找到的对象内容为 evadeForm 的最新内容
              this.$set(this.yzpwGroup, index, { ...this.yzpwForm }); // 使用 $set 方法触发响应式更新
              this.openyzpw = false;
            }
          } else {
            // 新增回避单位信息
            this.yzpwGroup.push(this.yzpwForm);
            this.openyzpw = false;
          }
        }
        this.yzpwReset();
      });
    },
    // 删除按钮
    expertHandleDelete(row, index) {
      this.$modal
        .confirm('是否确认删除序号为为"' + (index + 1) + '"的专家组？')
        .then(() => {
          if (this.$route.params.applyId != 0) {
            this.groupIds.push(row.groupId);
          }
          this.expertGroup.splice(index, 1);
        })
        .catch(() => {});
      console.log(row);
    },
    evadeUnitHandleDelete(row, index) {
      this.$modal
        .confirm('是否确认删除序号为为"' + (index + 1) + '"的回避单位信息？')
        .then(() => {
          if (this.$route.params.applyId != 0) {
            this.unitIds.push(row.evadeId);
          }
          this.avoidanceUnit.splice(index, 1);
        })
        .catch(() => {});
    },
    evadeExpertHandleDelete(row, index) {
      this.$modal
        .confirm('是否确认删除序号为为"' + (index + 1) + '"的回避专家信息？')
        .then(() => {
          if (this.$route.params.applyId != 0) {
            this.manualExpertIds.push(row.evadeId);
          }
          this.avoidanceExpert.splice(index, 1);
        })
        .catch(() => {});
    },
    manualExpertHandleDelete(row, index) {
      this.$modal
        .confirm('是否确认删除序号为为"' + (index + 1) + '"的专家信息？')
        .then(() => {
          if (this.$route.params.applyId != 0) {
            this.expertIds.push(row.expertId);
          }
          this.expertTable.splice(index, 1);
        })
        .catch(() => {});
    },
    yzpwHandleDelete(row, index) {
      console.log(111111);
      this.$modal
        .confirm('是否确认删除序号为为"' + (index + 1) + '"的采购人代表信息？')
        .then(() => {
          if (this.$route.params.applyId != 0) {
            this.yzpwIds.push(row.yzpwId);
          }
          this.yzpwGroup.splice(index, 1);
        })
        .catch(() => {});
    },
    // 修改按钮
    expertHandleModify(row, index) {
      this.expertReset();
      console.log(row);
      this.expertForm = row;
      this.isModificationOperation = true;
      this.open = true;
    },
    evadeHandleModify(row, index) {
      this.expertReset();
      this.evadeForm = row;
      this.isModificationOperation = true;
      this.openEvade = true;
    },
    yzpwHandleModify(row, index) {
      this.yzpwReset();
      this.yzpwForm = row;
      this.isModificationOperation = true;
      this.openyzpw = true;
    },
    // 获取可抽取专家个数
    getExtractableExpertNum() {
      // { pageNum: 0, pageSize: 0 }
      listInfo({
        expertCareer: this.expertForm.expertClassificationCode
          ? this.expertForm.expertClassificationCode.join(",")
          : "",
        expertType: this.expertForm.expertType,
        expertMainEvaluatArea: this.expertForm.groupAddress
          ? this.expertForm.groupAddress.join(",")
          : "",
      }).then((response) => {
        this.expertNumber = response.total;
      });
    },
    expertClassificationCodeChange(value) {
      // 赋值名字
      let nodeInfo = this.$refs["expertClassificationCode"].getCheckedNodes();
      console.log(nodeInfo);
      const pathString = nodeInfo[0].pathNodes
        .map((node) => node.label)
        .join("/");
      this.expertForm.expertClassificationName = pathString;
      // 根据条件获取可抽取专家个数
      this.getExtractableExpertNum();
    },
    expertTypeChange() {
      this.getExtractableExpertNum();
    },
    groupAddressChange(value) {
      let nodeInfo = this.$refs["groupAddress"].getCheckedNodes();
      const pathString = nodeInfo[0].pathNodes
        .map((node) => node.label)
        .join("/");
      //this.expertForm.groupAddressStr = pathString;
      getcgxhzj().then((response) => {
        const count = response.data.data.experts.reduce((total, expert) => {
          const pspm = expert.pspm;
          const matches = pspm.match(new RegExp(pathString, "g"));
          return total + (matches ? matches.length : 0);
        }, 0);
        console.log(`包含pathString的数量: `);
        this.expertNumber = count;
      });
      // this.getExtractableExpertNum();
    },
    // 关闭当前页
    close() {
      this.$tab.closePage();
    },
  },
};
</script>

<style>
/* 卡片整体样式 */
.custom-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 卡片头部 */
.card-header {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
}
.header-icon {
  color: #409eff;
  font-size: 18px;
  margin-right: 8px;
}
.header-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 卡片内容区域 */
.card-content {
  padding: 20px;
}

/* 表单行：每行最多放3组 */
.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px; /* 抵消子元素的padding */
}

/* 表单组：标签+输入框 */
.form-group {
  flex: 1 1 33.33%; /* 每行3个，自动平分宽度 */
  max-width: 33.33%;
  padding: 0 8px;
  margin-bottom: 16px;
  box-sizing: border-box;
}

/* 表单标签 */
.form-label {
  display: block;
  margin-bottom: 4px;
  color: #606266;
  font-size: 14px;
}

/* 必选标记 */
.required-mark {
  color: #f56c6c;
  margin-right: 4px;
}

/* 选择器样式 */
.custom-select {
  width: 100%;
  max-width: 100%; /* 自适应父容器 */
}

/* 输入框样式 */
.custom-input {
  width: 100%;
  background: #f8f9fa;
  cursor: not-allowed;
}

/* 数字输入框样式 */
.custom-input-number {
  width: 100%;
}
.committee {
  padding: 0 50px;
}
.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: rgb(255, 255, 255);

  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}

.option {
  text-align: center;
}
</style>
