.layout-main {
  max-width: 30rem;
  min-width: 16rem;
  display: flex;
  flex-direction: column;
  font-size: 0.14rem;
  overflow: hidden;
}

.layout-header {
  width: 100%;
  // height: 62px; // 如果不需要，保持注释或删除
}

.header-menu {
  width: 100%;
  border-bottom: 1px solid #e3e3e3;
  padding: 0;
  margin: 0;
  display: flex;
  padding: 0.04rem 0;

  li {
    list-style: none;
    height: 0.7rem;
  }

  .header-logo {
    width: 4rem;
    justify-content: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;

    img {
      height: 0.62rem;
    }
  }

  .header-btn {
    flex: 1;
    min-width: 8rem;
    justify-content: center;
    align-items: center;
    display: flex;
  }

  .header-external {
    width: 4rem;
    justify-content: center;
    align-items: center;
    display: flex;
  }
}

.custom-ant-button {
  width: 1.4rem;
  font-size: 0.16rem !important;
  height: 0.4rem;
  padding: 0;
}

.layout-body {
  height: calc(100% - 80px);
  width: 100%;
  display: flex;

  .sign-configure {
    width: 4rem;
    height: 100%;
    padding: 0.2rem 0.4rem 0 0.4rem;
    box-sizing: border-box;

    h3 {
      text-align: center;
    }

    .mode-location {
      padding: 0.2rem 0;
      font-size: 0.14rem;
      line-height: 0.25rem;
      text-indent: 0.28rem;
    }

    .signer-list {
      ul,
      .mode-keyword ul {
        padding: 0;
      }

      li,
      .mode-keyword li {
        list-style: none;
        padding: 0.02rem 0;
      }

      .li-style::before {
        content: "";
        width: 0.06rem;
        height: 0.06rem;
        display: inline-block;
        border-radius: 50%;
        background: #999;
        vertical-align: middle;
        margin-right: 0.1rem;
      }

      .li-entp-seal {
        padding: 0.1rem 0.5rem;

        .entp-seal {
          width: 180px;
          height: 180px;
          border: 1px dashed #bbb;
        }
      }
    }
  }

  .sign-description {
    width: 4rem;
    height: 100%;
    padding: 0.2rem 0;

    ul li {
      list-style: none;
    }

    .description-scrollbar {
      padding: 0 0.2rem 0.3rem 0.2rem;
      box-sizing: border-box;
    }

    .description-title {
      line-height: 0.3rem;

      &::before {
        content: "";
        width: 0.06rem;
        height: 0.06rem;
        display: inline-block;
        border-radius: 50%;
        background: #666;
        vertical-align: middle;
        margin-right: 0.1rem;
      }
    }

    .description-text {
      padding-left: 0.16rem;
      line-height: 0.2rem;
    }
  }

  .sign-configure .configure-title {
    font-weight: 600;
    line-height: 0.3rem;
  }

  .sign-content {
    flex: 1;
    height: 100%;
    min-width: 800px;
    border-left: 1px solid #e3e3e3;
    border-right: 1px solid #e3e3e3;
    background-color: #f1f1f1;
    padding: 4px 0 20px 0;
    box-sizing: border-box;

    .document-content {
      flex: 1;
      height: 100%;
    }

    .document-list {
      position: relative;
      margin: 0 auto;
      width: 800px;
    }

    .document-page {
      position: absolute;
    }
  }
}

.entp-seal {
  width: 170px;
  height: 170px;
  border: 1px dashed #bbb;
  background-color: #eee;
  justify-content: center;
  align-items: center;
  display: flex;
  padding: 5px;
  user-select: none;

  img {
    width: 160px;
  }
}

.person-seal {
  width: 170px;
  height: 80px;
  border: 1px dashed #bbb;
  background-color: #eee;
  justify-content: center;
  align-items: center;
  display: flex;
  padding: 5px;
  user-select: none;

  img {
    width: 160px;
  }
}

.drag-class {
  .unmover,
  .ghost .unmover {
    display: none !important;
  }

  li,
  .ghost li {
    list-style: none !important;
  }
}
