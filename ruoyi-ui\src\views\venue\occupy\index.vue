<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="场地占用id" prop="occupyId">
        <el-input
          v-model="queryParams.occupyId"
          placeholder="请输入场地占用id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告id" prop="noticeId">
        <el-input
          v-model="queryParams.noticeId"
          placeholder="请输入公告id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场地id" prop="venueId">
        <el-input
          v-model="queryParams.venueId"
          placeholder="请输入场地id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场地名称" prop="venueName">
        <el-input
          v-model="queryParams.venueName"
          placeholder="请输入场地名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="场地类型 (1开标 2评标)" prop="venueType">
        <el-select v-model="queryParams.venueType" placeholder="请选择场地类型 (1开标 2评标)" clearable>
          <el-option
            v-for="dict in dict.type.busi_venue_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="占用开始时间">
        <el-date-picker
          v-model="daterangeOccupyStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="占用结束时间">
        <el-date-picker
          v-model="daterangeOccupyEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="人数" prop="peopleNumber">
        <el-input
          v-model="queryParams.peopleNumber"
          placeholder="请输入人数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评标时间段0上午1下午2全天" prop="bidEvaluationPeriod">
        <el-select v-model="queryParams.bidEvaluationPeriod" placeholder="请选择评标时间段0上午1下午2全天" clearable>
          <el-option
            v-for="dict in dict.type.busi_bid_evaluation_period"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['venue:occupy:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['venue:occupy:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['venue:occupy:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['venue:occupy:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="occupyList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="场地占用id" align="center" prop="occupyId" />
      <el-table-column label="公告id" align="center" prop="noticeId" />
      <el-table-column label="场地id" align="center" prop="venueId" />
      <el-table-column label="场地名称" align="center" prop="venueName" />
      <el-table-column label="场地类型 (1开标 2评标)" align="center" prop="venueType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_venue_type" :value="scope.row.venueType"/>
        </template>
      </el-table-column>
      <el-table-column label="占用开始时间" align="center" prop="occupyStartTime" width="180">
        <template slot-scope="scope">
<!--          <span>{{ parseTime(scope.row.occupyStartTime, '{y}-{m}-{d}') }}</span>-->
          <span>{{ parseTime(scope.row.occupyEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>

        </template>
      </el-table-column>
      <el-table-column label="占用结束时间" align="center" prop="occupyEndTime" width="180">
        <template slot-scope="scope">
<!--          <span>{{ parseTime(scope.row.occupyEndTime, '{y}-{m}-{d}') }}</span>-->
          <span>{{ parseTime(scope.row.occupyEndTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>

        </template>
      </el-table-column>
      <el-table-column label="人数" align="center" prop="peopleNumber" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="评标时间段0上午1下午2全天" align="center" prop="bidEvaluationPeriod">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_bid_evaluation_period" :value="scope.row.bidEvaluationPeriod"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['venue:occupy:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['venue:occupy:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改场地占用对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
<!--        <el-form-item label="场地占用id" prop="occupyId">-->
<!--          <el-input v-model="form.occupyId" placeholder="请输入场地占用id" />-->
<!--        </el-form-item>-->
        <el-form-item label="公告id" prop="noticeId">
          <el-input v-model="form.noticeId" placeholder="请输入公告id" />
        </el-form-item>
        <el-form-item label="场地id" prop="venueId">
          <el-input v-model="form.venueId" placeholder="请输入场地id" />
        </el-form-item>
        <el-form-item label="场地名称" prop="venueName">
          <el-input v-model="form.venueName" placeholder="请输入场地名称" />
        </el-form-item>
        <el-form-item label="场地类型" prop="venueType">
          <el-select v-model="form.venueType" placeholder="请选择场地类型 (1开标 2评标)">
            <el-option
              v-for="dict in dict.type.busi_venue_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="占用开始时间" prop="occupyStartTime">
          <el-date-picker clearable
            v-model="form.occupyStartTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择占用开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="占用结束时间" prop="occupyEndTime">
          <el-date-picker clearable
            v-model="form.occupyEndTime"
            type="datetime"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择占用结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="人数" prop="peopleNumber">
          <el-input v-model="form.peopleNumber" placeholder="请输入人数" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="删除标记" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="评标时间段" prop="bidEvaluationPeriod">
          <el-select v-model="form.bidEvaluationPeriod" placeholder="请选择评标时间段0上午1下午2全天">
            <el-option
              v-for="dict in dict.type.busi_bid_evaluation_period"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOccupy, getOccupy, delOccupy, addOccupy, updateOccupy } from "@/api/venue/occupy";

export default {
  name: "Occupy",
  dicts: ['busi_venue_type', 'base_yes_no', 'busi_bid_evaluation_period'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 场地占用表格数据
      occupyList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 评标时间段0上午1下午2全天时间范围
      daterangeOccupyStartTime: [],
      // 评标时间段0上午1下午2全天时间范围
      daterangeOccupyEndTime: [],
      // 评标时间段0上午1下午2全天时间范围
      daterangeCreateTime: [],
      // 评标时间段0上午1下午2全天时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        occupyId: null,
        noticeId: null,
        venueId: null,
        venueName: null,
        venueType: null,
        occupyStartTime: null,
        occupyEndTime: null,
        peopleNumber: null,
        bidEvaluationPeriod: null,
        remark: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询场地占用列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeOccupyStartTime && '' != this.daterangeOccupyStartTime) {
        this.queryParams.params["beginOccupyStartTime"] = this.daterangeOccupyStartTime[0];
        this.queryParams.params["endOccupyStartTime"] = this.daterangeOccupyStartTime[1];
      }
      if (null != this.daterangeOccupyEndTime && '' != this.daterangeOccupyEndTime) {
        this.queryParams.params["beginOccupyEndTime"] = this.daterangeOccupyEndTime[0];
        this.queryParams.params["endOccupyEndTime"] = this.daterangeOccupyEndTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listOccupy(this.queryParams).then(response => {
        this.occupyList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        occupyId: null,
        noticeId: null,
        venueId: null,
        venueName: null,
        venueType: null,
        occupyStartTime: null,
        occupyEndTime: null,
        peopleNumber: null,
        remark: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        bidEvaluationPeriod: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeOccupyStartTime = [];
      this.daterangeOccupyEndTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.occupyId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加场地占用";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const occupyId = row.occupyId || this.ids
      getOccupy(occupyId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改场地占用";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.occupyId != null) {
            updateOccupy(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOccupy(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const occupyIds = row.occupyId || this.ids;
      this.$modal.confirm('是否确认删除场地占用编号为"' + occupyIds + '"的数据项？').then(function() {
        return delOccupy(occupyIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('venue/occupy/export', {
        ...this.queryParams
      }, `occupy_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
