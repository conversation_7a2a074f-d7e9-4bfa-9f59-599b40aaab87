import request from '@/utils/request'

// 查询在线开标消息记录列表
export function listRecord(query) {
  return request({
    url: '/message/record/list',
    method: 'get',
    params: query
  })
}

// 查询在线开标消息记录详细
export function getRecord(id) {
  return request({
    url: '/message/record/' + id,
    method: 'get'
  })
}

// 新增在线开标消息记录
export function addRecord(data) {
  return request({
    url: '/message/record',
    method: 'post',
    data: data
  })
}

// 修改在线开标消息记录
export function updateRecord(data) {
  return request({
    url: '/message/record',
    method: 'put',
    data: data
  })
}

// 删除在线开标消息记录
export function delRecord(id) {
  return request({
    url: '/message/record/' + id,
    method: 'delete'
  })
}
