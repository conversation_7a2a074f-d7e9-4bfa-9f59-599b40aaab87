<template>
  <div>
<!--    <BidHeadthree></BidHeadthree>-->
	  <div class="title">专家评审系统</div>
    <div class="info">
      <div class="content">
        <confirm
          v-if="node == 'confirm'"
          :project="project"
          @send="handleData"
        ></confirm>
        <reviewDiscipline
          v-if="node == 'reviewDiscipline'"
          :project="project"
          @send="handleData"
        ></reviewDiscipline>
        <avoid
          v-if="node == 'avoid'"
          :project="project"
          @send="handleData"
        ></avoid>
        <leader
          v-if="node == 'leader'"
          @send="handleData"
        ></leader>
        <leaderConfirm
          v-if="node == 'leaderConfirm'"
          @send="handleData"
        ></leaderConfirm>
        <flow
          :projectInfo="project"
          v-if="node == 'flow'"
          @send="handleData"
        ></flow>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
import confirm from "./components/confirm";
import reviewDiscipline from "./components/reviewDiscipline";
import avoid from "./components/avoid";
import leader from "./components/leader";
import leaderConfirm from "./components/leaderConfirm";
import flow from "./components/flow";

import { getProject } from "@/api/tender/project";
import { getInfo2 as getNodeInfo2 } from "@/api/evaluation/expertNodeInfo";
import { getPesponseDocPageByProject } from "@/api/docResponse/entInfo";
import { infoByParams } from "../../api/documents/uinfo";

export default {
  components: { confirm, reviewDiscipline, avoid, leader, leaderConfirm, flow },
  name: "expertInfo",
  data() {
    return {
      project: {},
      node: "confirm",
      expert: { expertCode: "" },
      expertEvaluation: {},
      nodes: {
        1: "confirm",
        2: "reviewDiscipline",
        3: "avoid",
        4: "leader",
        5: "leaderConfirm",
        6: "flow",
      },
    };
  },
  methods: {
    init() {
      // 根据项目id查询项目信息
      getProject(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.project = response.data;
	        // 从 URL 查询参数中获取 tips 参数
	        const query = this.$route.query;
	        if (query.tips == "true" && this.project.tenderMode==1) {
		        // 显示提示消息
		        this.$confirm("是否进行二次报价", "提示", {
			        confirmButtonText: "确定",
			        cancelButtonText: "取消",
			        type: "warning",
		        })
			        .then(() => {
				        this.secondOffer();
			        })
			        .catch(() => {});
	        }
        } else {
          this.$message.warning(response.msg);
        }
      });
      // 根据项目id查询响应文件的页码数据
      getPesponseDocPageByProject(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
					// 把响应文件的页码数据存入本地存储
          localStorage.setItem(
            "entDocResponsePage",
            JSON.stringify(response.data)
          );
        } else {
          this.$message.warning(response.msg);
        }
      });
			// 根据项目id查询采购文件的页码数据
	    infoByParams({projectId:this.$route.query.projectId}).then((response) => {
		    console.log(response)
		    if (response.code == 200) {
					// 把采购文件的页码数据存入本地存储
          localStorage.setItem('entDocProcurementPage', response.data.remark);
        } else {
          this.$message.warning(response.msg);
        }
			});
	    
      getNodeInfo2({
        projectId: this.project.projectId,
        expertCode: this.expert.expertCode,
      }).then((result) => {
        if (result.code == 200) {
          this.expertEvaluation = result.data;
          console.info("expertEvaluation", this.expertEvaluation);
          this.node = this.nodes[this.expertEvaluation.evalNode];
          localStorage.setItem(
            "expertResultId",
            this.expertEvaluation.expertResultId
          );
          localStorage.setItem(
            "evalExpertEvaluationInfoId",
            this.expertEvaluation.evalExpertEvaluationInfoId
          );
        }
      });
    },
    handleData(data) {
      this.node = data;
    },
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
  },
  mounted() {
    this.expert.expertCode = this.$route.query.zjhm;
    this.project.projectId = this.$route.query.projectId;
    this.init();

    
		
  },
};
</script>

<style lang="scss" scoped>
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 65vh;
  margin: 20px 0;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.title{
	background-color: #c8c9cc;
	padding: 10px 5%;
}
</style>
