<template>
  <div
    class="tender"
    v-loading="loading"
  >
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>项目选择</span>
        </div>
        <el-col
          :span="24"
          class="card-box"
        >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <span>项目名称：</span>
            <span>{{ projectName }}</span>
          </div>
        </el-col>
        <el-col
          :span="24"
          class="card-box"
        >
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <span>合同签署时间： </span>
            <span>{{ contract.contractSigningDate }}</span>
          </div>
        </el-col>
      </el-card>
    </el-col>

    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>项目信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <el-table
            :cell-style="{ 'text-align': 'center' }"
            :header-cell-style="{ 'text-align': 'center' }"
            :data="bidderNotice"
            border
            style="width: 100%"
          >
            <el-table-column
              prop="bidderName"
              label="成交供应商"
              width="200"
            >
            </el-table-column>
            <el-table-column
              prop="bidderAmount"
              label="成交金额(元)"
            >
            </el-table-column>
            <el-table-column
              prop="contactPerson"
              label="联系人"
            >
            </el-table-column>
            <el-table-column
              prop="contactNumber"
              label="联系电话"
            >
            </el-table-column>
            <el-table-column
              prop="createTime"
              label="中选日期"
            >
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </el-col>
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i> 附件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr
                v-for="dict in dict.type.busi_expert_transaction_contract_attachment"
                :key="dict.label"
              >
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;">*</strong>{{ dict.label }}
                  </div>
                </td>
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    <template>
                      <FileUpload
                        :value="getImgPath(dict)"
                        @input="handleInput(dict, $event)"
                        :fileType="['pdf', 'doc', 'docx']"
                        :isShowTip="false"
                        :showOnly="true"
                      ></FileUpload>
                    </template>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <div style="text-align: center">
      <el-button
        type="primary"
        @click="closeCard()"
      >关闭</el-button>
    </div>
  </div>
</template>
<script>
import { supplierisWinProdect, listProject } from "@/api/tender/project";
import {
  listContract,
  getContract,
  delContract,
  addContract,
  updateContract,
} from "@/api/transaction/contract";
import { formatDate } from "@/utils/index";

export default {
  components: {},
  dicts: ["busi_expert_transaction_contract_attachment"],
  props: [],
  data() {
    return {
      // 遮罩层
      loading: true,
      projectId: "",
      projectName: "",
      datetime: "",
      contract: {},
      projectIdOptions: [],
      bidOption: [],
      formData: {
        contractId: null,
        projectId: null,
        contractSigningDate: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      },
      fileList: "",
      attachmentsMap: {},
      bidderNotice: [],
    };
  },
  computed: {},
  watch: {},
  created() {},
  mounted() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      // const promise1 = supplierisWinProdect().then((response) => {
      //   this.bidOption = response.data;
      // });
      // TODO 只查加上当前用户的项目
      // const promise2 = listProject({ delFlag: 0 }).then((response) => {
      //   this.projectIdOptions = response.rows;
      // });
      const promise3 = getContract(this.$route.params.contractId).then(
        (response) => {
          this.contract = response.data;
          this.datetime = this.contract.contractSigningDate;
          this.projectName = this.contract.project.projectName;
          this.bidderNotice.push(this.contract.busiBidderInfo);
          console.log('this.contract',this.contract);
          // this.datetime = this.contract.
          if (response.data.attachments != null) {
            this.attachmentsMap = response.data.attachments.reduce(
              (accumulator, attachment) => {
                // 如果accumulator中还没有这个fileType的键，则创建一个新数组
                if (!accumulator[attachment.fileType]) {
                  accumulator[attachment.fileType] = [];
                }
                // 将当前attachment添加到对应fileType的数组中
                accumulator[attachment.fileType].push(attachment);
                return accumulator;
              },
              {}
            );
          }
        }
      );

      // 使用 Promise.all 等待两个接口请求都完成
      Promise.all([promise3])
        .then(() => {
          // 两个接口都请求完成后将 this.loading 设置为 false
          this.projectId = this.contract.projectId;
          this.change(this.contract.projectId);
          this.loading = false;
        })
        .catch((error) => {
          // 处理 Promise.all 中任何一个 Promise 请求失败的情况
          console.error(error);
        });
      this.loading = false;
    },
    change(value) {
      if (value) {
        // this.bidderNotice = [];
        // const selectedItem = this.bidOption.find(
        //   (item) => item.projectId === value
        // );
        // this.bidderNotice.push(selectedItem.bidderNotice);
      } else {
      }
    },
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    closeCard() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
