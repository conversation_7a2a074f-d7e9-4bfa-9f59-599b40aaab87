import request from '@/utils/request'

// 查询采购文件编制基础信息列表
export function listInfo(query) {
  return request({
    url: '/documents/info/list',
    method: 'get',
    params: query
  })
}

// 查询采购文件编制基础信息详细
export function getInfo(projectFileId) {
  return request({
    url: '/documents/info/' + projectFileId,
    method: 'get'
  })
}

// 新增采购文件编制基础信息
export function saveInfo(data) {
  return request({
    url: '/responseFile/saveInfo',
    method: 'post',
    data: data
  })
}