import request from "@/utils/request";

// 查询专家打分详情列表
export function listDetail(query) {
  return request({
    url: "/evaluation/detail/list",
    method: "get",
    params: query,
  });
}

// 查询专家打分详情详细
export function getDetail(expertEvaluationId) {
  return request({
    url: "/evaluation/detail/" + expertEvaluationId,
    method: "get",
  });
}

// 新增专家打分详情
export function addDetail(data) {
  return request({
    url: "/evaluation/detail",
    method: "post",
    data: data,
  });
}

// 修改专家打分详情
export function updateDetail(data) {
  return request({
    url: "/evaluation/detail",
    method: "put",
    data: data,
  });
}

// 删除专家打分详情
export function delDetail(expertEvaluationId) {
  return request({
    url: "/evaluation/detail/" + expertEvaluationId,
    method: "delete",
  });
}
// 汇总评审确认页面
export function confirmList(data) {
  return request({
    url: "/evaluation/detail/reviewSummaryConfirmation",
    method: "post",
    data: data,
  });
}
// 获取评审信息
export function getDetailByPsxx(data) {
  return request({
    url: "/evaluation/detail/getDetailByPsxx",
    method: "post",
    data: data,
  });
}
