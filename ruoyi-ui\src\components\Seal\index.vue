<template>
  <el-dialog
    title="创建印章"
    :visible.sync="modalOpen"
    width="800px"
  >
    <el-form
      ref="selaFromRef"
      :model="sealFrom"
    >
      <!-- <div style="width: 350px;">
        <el-form-item
          prop="entpName"
          label="企业名称"
          :rules="[{ required: true, message: '请输入企业名称' }]"
        >
          <el-input
            v-model="sealFrom.entpName"
            size="medium"
            placeholder="请输入企业名称/生成用于签署的测试数字证书"
            class="input-width"
          />
        </el-form-item>
      </div> -->
      <el-row style="width: 100%;padding-top: 0px;display: flex; align-items: stretch;">
        <el-col
          :span="24"
          style="display: flex; flex-direction: column;"
        >
          <el-tabs
            v-model="sealFrom.createType"
            type="card"
            @tab-click="createTypeChange"
          >
            <el-tab-pane
              label="印模生成"
              name="first"
            >
              <div class="seal-make-layout">

                <div class="seal-upload">
                  <div
                    v-if="fileList.length == 0"
                    class="upload-before"
                  >
                    <el-upload
                      :action="uploadFileUrl"
                      :headers="headers"
                      :file-list="fileList"
                      :show-file-list="false"
                      :auto-upload="false"
                      :on-change="handleChange"
                    >
                      <div class="local-upload pointer">
                        <div style="width:100%">点击上传</div>
                      </div>
                    </el-upload>
                    <div class="prompt">
                      <p style="font-weight: 600;">印模上传生成印章的三个步骤：</p>
                      <p>步骤一：在白纸上加盖印章；</p>
                      <p>步骤二：拍照或扫描上传，对印模图片进行裁剪；</p>
                      <p>步骤三：对裁剪后的印模图片进行自动透明化抠图，生成透明印章；</p>
                    </div>
                  </div>
                  <div
                    class="upload-img"
                    v-else
                  >
                    <img
                      :src="uploadSeal"
                      ref="sealRef"
                      @load="imgeLoad"
                      style="height:100%;display:none"
                    />
                  </div>
                  <div
                    class="SealActions"
                    v-if="fileList.length > 0"
                  >
                    <el-row>
                      <el-col
                        :span="4"
                        class="center"
                      >
                        <i
                          class="pointer el-icon-refresh-left"
                          @click="sealActions(-1)"
                        ></i>
                      </el-col>
                      <el-col
                        :span="4"
                        class="center"
                      >
                        <i
                          class="pointer el-icon-refresh-right"
                          @click="sealActions(1)"
                        ></i>
                      </el-col>
                      <el-col
                        :span="4"
                        class="center"
                      >
                        <i
                          class="pointer el-icon-minus"
                          @click="sealActions(-2)"
                        ></i>
                      </el-col>
                      <el-col
                        :span="4"
                        class="center"
                      >
                        <i
                          class="pointer el-icon-plus"
                          @click="sealActions(2)"
                        ></i>
                      </el-col>
                      <el-col :span="5">
                        <el-upload
                          :action="uploadFileUrl"
                          :headers="headers"
                          :file-list="fileList"
                          :show-file-list="false"
                          :on-change="handleChange2"
                        >
                          <el-button type="text">重新上传</el-button>
                        </el-upload>
                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="8">
                        <h4 style="line-height: 32px;">旋转角度：</h4>
                      </el-col>
                      <el-col :span="16">
                        <div style="margin: 18px 0;">
                          <el-slider
                            @change="sealRotateChange"
                            v-model="sealOptions.sealRotate"
                            :min="-180"
                            :max="180"
                          />
                        </div>

                      </el-col>
                    </el-row>
                    <el-row>
                      <el-col :span="8">
                        <h4 style="line-height: 32px;">背景透明度：</h4>
                      </el-col>
                      <el-col :span="16">
                        <div style="margin: 18px 0;">
                          <el-slider
                            @change="buildCropperImage"
                            v-model="sealOptions.sealBackground"
                            :min="0"
                            :max="100"
                          />
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
                <div class="seal-preview">
                  <div class="title">签章预览</div>
                  <div class="seal-preview-img">
                    <img
                      :src="'data:image/png;base64,'+sealFrom.sealPreview"
                      style="width:100%;height:100%"
                      alt="暂未生成签章"
                      v-if="sealFrom.sealPreview"
                    />
                    <span
                      style="color: #aaa;"
                      v-else
                    >暂未生成签章</span>
                  </div>
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </el-col>
      </el-row>

    </el-form>
    <el-divider></el-divider>
    <div style="text-align: center;">
      <el-button
        key="back"
        @click="closeModal"
      >取消</el-button>
      <el-button
        type="primary"
        @click="sealPreviewInfo"
        :loading="sealPreviewLoading"
      >预览</el-button>
      <el-button
        key="submit"
        type="primary"
        @click="handleOk"
        v-if="sealFrom.sealPreview"
      >使用</el-button>
    </div>
  </el-dialog>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { getToken } from "@/utils/auth";
import "cropperjs/dist/cropper.css";
import Cropper from "cropperjs";
import { templateGz } from "./data/image";
import request from "@/utils/request";

function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = (error) => reject(error);
  });
}
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    sealModalShow: {
      type: Boolean,
      default: false,
    },
    entId: {
      type: Number,
      default: 0,
    },
    type: {
      type: String,
      default: "",
    },
  },
  computed: {
    modalOpen: {
      get: function () {
        return this.sealModalShow;
      },
      set: function (newValue) {
        this.$emit("update:sealModalShow", newValue); // 触发更新事件，父组件的showDialog会自动更新
      },
    },
  },

  data() {
    //这里存放数据
    return {
      sealPreviewLoading: false,
      //印章表单属性
      sealFrom: {
        createType: "first",
        sealStyle: 1,
        entpName: "entpName",
        middleText: "测试印章",
        sealPreview: false,
      },
      uploadFileUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传文件服务器地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      fileList: [],
      uploadSeal: null,
      firstUpload: true,
      sealCropper: null,
      sealRef: null,
      sealOptions: {
        sealRotate: 0,
        sealBackground: 50,
        imageWidth: 400,
        imageHeight: 400,
      },
      templateGz: templateGz,
    };
  },
  //监听属性 类似于data概念
  // computed: {},

  //方法集合
  methods: {
    //印章制作完成确定按钮提交
    async handleOk() {
      try {
        const values = await this.$refs.selaFromRef.validate();
        const data = {
          sealImage: this.sealFrom.sealPreview,
          entpName: this.sealFrom.entpName,
          type: this.type,
        };
        this.$emit("success", data);
        this.modalOpen = false;
      } catch (errorInfo) {
        console.log("Failed:", errorInfo);
        this.$message.warning("企业名称不能为空，请输入企业名称");
      }
    },

    //关闭弹窗
    closeModal() {
      this.modalOpen = false;
    },

    //印章生成方式切换
    createTypeChange(tab, event) {
      this.sealFrom.sealPreview = false;
    },
    /**
     * 签章预览
     */
    async sealPreviewInfo() {
      // sealFrom.value.sealPreview = true;
      this.sealPreviewLoading = true;
      if (this.sealFrom.createType == 1) {
        try {
          const values = await this.$refs.selaFromRef.validateFields();
          const response = await request({
            url: "/kaifangqian/generate/seal",
            method: "post",
            data: {
              middleText: this.sealFrom.middleText,
              topText: this.sealFrom.entpName,
            },
          });
          this.sealFrom.sealPreview = response.result.entSeal;
        } catch (errorInfo) {
          console.log("Failed:", errorInfo);
          this.$message.warning("企业名称和横排文字不能为空");
        }
      } else {
        if (this.fileList.length == 0) {
          this.$message.warning("请先上传印模在进行预览");
        } else {
          await this.buildCropperImage();
        }
      }
      this.sealPreviewLoading = false;
    },
    //首次上传印模图片
    handleChange(file, fileList) {
      getBase64(file.raw).then((res) => {
        this.fileList = [file];
        this.uploadSeal = res;
      });
    },
    //重新上传印模图片
    async handleChange2(file, fileList) {
      this.firstUpload = false;
      await this.handleChange(file, fileList);
    },
    //印章图片旋转
    sealActions(type) {
      switch (type) {
        case -1:
          this.sealCropper.rotate(-90);
          break;
        case 1:
          this.sealCropper.rotate(90);
          break;
        case -2:
          this.sealCropper.zoom(-0.1);
          break;
        case 2:
          this.sealCropper.zoom(0.1);
          break;
      }
      setTimeout(() => {
        this.buildCropperImage();
      }, 100);
    },
    //修改印章的背景透明度
    sealRotateChange(to) {
      this.sealCropper.rotateTo(to);
      setTimeout(() => {
        this.buildCropperImage();
      }, 100);
    },
    //将上传的图片加载到 图片处理工具中
    sealPreviewCropper() {
      this.sealCropper = new Cropper(this.$refs.sealRef, {
        viewMode: 1,
        dragMode: "move",
        preview: ".before",
        initialAspectRatio: 1,
        aspectRatio: 1,
        background: true,
        autoCrop: true,
        autoCropArea: 0.7,
        zoomOnWheel: true,
        zoomOnTouch: true,
        cropBoxResizable: false,
        cropBoxMovable: false,
        wheelZoomRatio: 0.05,
        cropend: this.sealCropend,
      });
    },
    //图片处理完成后调用后端服务进行处理
    sealCropend(end) {
      this.buildCropperImage();
    },

    //印模图片加载完成事件
    imgeLoad(img) {
      console.log("load", img);
      if (!this.firstUpload) {
        this.sealCropper.destroy();
      }
      this.sealPreviewCropper();
    },
    //图片缩放、旋转、背景透明度修改后调用次方法重新生成
    async buildCropperImage() {
      if (!this.sealCropper) {
        return;
      } else {
      }
      const seal = this.sealCropper
        .getCroppedCanvas({
          width: this.sealOptions.imageWidth,
          height: this.sealOptions.imageHeight,
          imageSmoothingQuality: "high",
        })
        .toDataURL("image/jpeg");

      const data = {
        image: seal.split(",")[1],
        colorRange: this.sealOptions.sealBackground + 120,
        entId: this.entId,
        type: this.type,
      };
      //   TODO  获取处理的印章
      //   const result = await templateGenerateSeal(data);
      const response = await request({
        url: "/kaifangqian/clip/seal",
        method: "post",
        data: data,
      });

      // this.sealFrom.sealPreview = response.result.entSeal;
      this.sealFrom.sealPreview = response.data.entSeal;
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {
    console.log(this.modalOpen);
  },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.seal-make-layout {
  display: flex;
  .seal-preview {
    padding-left: 40px;
    .title {
      font-size: 14px;
      font-weight: 600;
      height: 30px;
    }
  }

  .seal-upload {
    padding: 20px 0;
    width: 442px;
  }
  .seal-preview-img {
    width: 160px;
    height: 160px;
    padding: 10px;
    border: 1px solid #ededed;
  }
}

.template-make {
  width: 100%;
  border: 1px solid #ededed;
  margin-top: 10px;
  padding: 5px 20px;
  .temlate-item .title {
    font-weight: 600;
  }
  .seal-style {
    //height:100px;
    padding-top: 15px;
    ul {
      display: flex;
      padding: 0;
      margin: 0;
    }
    ul li {
      user-select: none;
      cursor: pointer;
      .seal-img {
        padding: 6px;
        width: 100px;
        height: 100px;
        border: 1px solid #ededed;
        border-radius: 4px;
      }
      .signature-style {
        display: flex;
        width: 116px;
        height: 116px;
        padding: 8px 6px;
        justify-items: center;
        align-items: center;
        border: 1px solid;
        border-color: inherit;
        border-radius: 4px;
      }
      .signature-style img {
        user-select: none;
      }
      .seal-name {
        text-align: center;
        line-height: 30px;
        color: #9e9e9e;
      }
    }
    ul li.active {
      .seal-img {
        border-color: #1890ff;
      }
      .seal-name {
        color: #1890ff;
      }
    }
    ul li:nth-child(n + 2) {
      margin-left: 10px;
    }
  }
}
.local-upload {
  width: 160px;
  height: 160px;
  padding: 10px;
  border: 1px dashed rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-items: center;
  text-align: center;
}
.upload-img {
  width: 300px;
  height: 300px;
}
.SealActions {
  width: 300px;
  padding: 8px 15px;
  background-color: rgba(0, 0, 0, 0.07);
}
.center {
  text-align: center;
  line-height: 32px;
}
.pointer {
  cursor: pointer;
}
.upload-before {
  display: flex;
}
.upload-before .prompt {
  flex: 1;
  height: 100%;
  font-size: 12px;
  padding: 0 10px 0 20px;
}
</style>