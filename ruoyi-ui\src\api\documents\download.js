import request from '@/utils/request'

// 查询采购文件下载记录列表
export function listDownload(query) {
  return request({
    url: '/documents/download/list',
    method: 'get',
    params: query
  })
}

// 查询采购文件下载记录列表
export function myOrder(query) {
  return request({
    url: '/documents/download/myOrder',
    method: 'get',
    params: query
  })
}

// 查询采购文件下载记录详细
export function getDownload(downloadId) {
  return request({
    url: '/documents/download/' + downloadId,
    method: 'get'
  })
}

// 新增采购文件下载记录
export function addDownload(data) {
  return request({
    url: '/documents/download',
    method: 'post',
    data: data
  })
}

// 修改采购文件下载记录
export function updateDownload(data) {
  return request({
    url: '/documents/download',
    method: 'put',
    data: data
  })
}

// 删除采购文件下载记录
export function delDownload(downloadId) {
  return request({
    url: '/documents/download/' + downloadId,
    method: 'delete'
  })
}
