<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
          <el-input type="textarea" :rows="25" placeholder="1、项目基本情况:
1）鹤壁市中医院淮河路院区：门诊楼 1-5 层的卫生保洁、病房楼、 康复楼、电车自行车棚、发热门诊、外围环境等区域卫生。环境消杀、 巡逻岗（兼顾污水处理、电梯救援、卡点、车棚）、停车场秩序维护等。
2）鹤壁市中医院南海路院区：门诊楼、病房楼、电车自行车棚、 发热门诊等区域卫生及外围环境等区域卫生。环境消杀、大门口门岗（兼顾白天卡点）、巡逻岗（兼顾制氧站、卡点、污水站加药、电梯 救援、自行车棚）、停车场秩序维护等。" v-model="requirement">
          </el-input>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      itemContent: {
        projectOverview:{
          requirement:""
        },
        standardsAndReq:{
          requirement:""
        }
      },
      requirement: "",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      console.log(itemInfo);
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        this.requirement = this.itemContent.projectOverview.requirement;
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      console.log(this.requirement)
      this.itemContent.projectOverview.requirement = this.requirement;
      let newContent = JSON.stringify(this.itemContent);
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newContent,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
  mounted() { },
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
.text {
  color: rgba(255, 87, 51, 1);
  font-size: 16px;
  line-height: 100%;
  text-align: left;
  text-indent: 2em;
}
</style>
