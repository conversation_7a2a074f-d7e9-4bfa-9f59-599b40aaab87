<template>
  <div class="app-container home">
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col
        :sm="24"
        :lg="12"
      >
        <el-card
          shadow="never"
          class="data-statistics"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>数据统计</span>
          </div>
          <div class="date">
            <div
              class="number"
              style="background-color: #d91d18"
            >
              <div class="name">{{ dataStatistics[0].name }}</div>
              <div class="value">{{ dataStatistics[0].value }}</div>
            </div>
            <div
              class="number"
              style="background-color: #176adb"
            >
              <div class="name">{{ dataStatistics[1].name }}</div>
              <div class="value">{{ dataStatistics[1].value }}</div>
            </div>
            <div
              class="number"
              style="background-color: #4dc01a"
            >
              <div class="name">{{ dataStatistics[2].name }}</div>
              <div class="value">{{ dataStatistics[2].value }}</div>
            </div>
            <div
              class="number"
              style="background-color: #03a98c"
            >
              <div class="name">{{ dataStatistics[3].name }}</div>
              <div class="value">{{ dataStatistics[3].value }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col
        :sm="24"
        :lg="12"
      >
        <el-card
          shadow="never"
          class="todo-list"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>待办事项</span>
          </div>
          <el-table
            :show-header="false"
            :data="todoList"
            style="width: 100%"
            :row-class-name="tableRowClassName"
          >
            <el-table-column
              prop="name"
              label="名字"
            >
              <template slot-scope="scope">
                <router-link
                  :to="todoPage(scope.row)"
                  class="link-type"
                >
                  <span>{{ scope.row.name }}</span>
                </router-link>
              </template>
            </el-table-column>
            <el-table-column
              prop="date"
              label="日期"
              width="100"
            >
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col
        :sm="24"
        :lg="24"
      >
        <el-card
          shadow="never"
          class="haveSignedUp"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>{{ bodyCenterTitle }}</span>
             <!-- <el-button
              type="text"
              @click="handleMore('cgxx/project')"
              class="more"
              >更多></el-button
            > -->
          </div>
          <el-carousel height="250px" :interval="5000" indicator-position="outside"><el-carousel-item v-for="item in haveLength" :key="item">
          <div class="date">
            <div
              @click="cardGoto(open)"
              v-for="open in waitOpens[item-1]"
              class="main"
              :key="open.intentionId"
            >
              <div class="content">
                <div class="title">{{ open.title }}</div>
                <div class="price">
                  <div class="one">¥{{ open.price }}</div>
                  <div class="two">{{ open.serveType }}</div>
                </div>
                <div class="info">
                  <div style="padding-bottom: 7px">
                    <span>发布时间 </span>
                    <span style="color: #666666">{{ open.releaseTime }} </span>
                  </div>
                  <div>
                    <span>采购单位 </span>
                    <span style="color: #666666">{{
                      open.purchasingUnit
                    }} </span>
                  </div>
                </div>
              </div>
              <div class="time">
                <el-statistic
                  v-if="!open.deadline"
                  class="word"
                  format="距结束 DD 天 HH 时 mm 分 ss 秒"
                  :value="open.deadline5"
                  time-indices
                  title=""
                >
                </el-statistic>
                <div
                  v-if="open.deadline"
                  style="color: #fff; text-align: center;"
                > 开标中 </div>
              </div>
            </div>
          </div></el-carousel-item></el-carousel>
        </el-card>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col
        :sm="24"
        :lg="24"
      >
        <el-card
          shadow="never"
          class="informationPublicity"
        >
          <div
            slot="header"
            class="announcement"
          >
            <div class="info">
              <div class="clearfix">公示信息</div>
              <div class="type">
                <div
                  class="select"
                  :class="{ active: activeName === '采购' }"
                  @click="toggleActive('采购', 1)"
                >
                  采购公告
                </div>
                <!-- <div
                  class="select"
                  :class="{ active: activeName === '变更' }"
                  @click="toggleActive('变更', 2)"
                >
                  变更公告
                </div> -->
                <div
                  class="select"
                  :class="{ active: activeName === '结果' }"
                  @click="toggleActive('结果', 3)"
                >
                  结果公告
                </div>
                <div
                  class="select"
                  :class="{ active: activeName === '取消' }"
                  @click="toggleActive('取消', 4)"
                >
                  取消公告
                </div>
              </div>
            </div>

<!--            <el-button
              type="text"
              class="more"
              @click="handleMoreByType()"
            >更多></el-button>-->
          </div>
          <el-table
            :data="informationPublicity"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            :cell-style="{ 'text-align': 'center' }"
            :header-cell-style="{
              background: '#176ADB',
              color: '#FFFFFF',
              'text-align': 'center',
            }"
          >
            <el-table-column label="公告名称">
              <template slot-scope="scope">
                <div class="name">
                  <div class="label">{{ scope.row.noticeType==1?'公告':'变更' }}</div>
                  <div
                    style="cursor: pointer"
                    @click="goto(scope.row)"
                  >
                    {{ scope.row.name }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              prop="unit"
              label="采购单位"
            > </el-table-column>
            <el-table-column
              prop="method"
              label="采购方式"
            > </el-table-column>
            <el-table-column
              prop="date"
              label="发布时间"
              width="150"
            >
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align:right"
            layout="prev, pager, next"
            :current-page.sync="info.currentPage"
            :page-size="info.pageSize"
            :total="info.totalRecords"
            @current-change="handlePageChangeInfo"
          >
          </el-pagination>
        </el-card>
      </el-col>
    </el-row>
    <el-row
      :gutter="20"
      class="el-row"
    >
      <el-col
        :sm="24"
        :lg="24"
      >
        <el-card
          shadow="never"
          class="myOrder"
        >
          <div
            slot="header"
            class="order"
          >
            <div class="info">
              <div class="clearfix">我的项目</div>
              <div class="type">
                <div
                  class="select"
                  style="
                    color: #176adb;
                    border-left: #176adb 2px solid;
                    text-align: center;
                    width: 65px;
                    margin-right: 0;
                  "
                >
                  状态：
                </div>
                <div
                  class="select"
                  :class="{ active: activeOrder === '全部' }"
                  @click="orderActive('全部')"
                >
                  全部
                </div>
                <div
                  class="select"
                  :class="{ active: activeOrder === '未开标' }"
                  @click="orderActive('未开标')"
                >
                  未开标
                </div>
                <div
                  class="select"
                  :class="{ active: activeOrder === '已开标' }"
                  @click="orderActive('已开标')"
                >
                  已开标
                </div>
              </div>
            </div>

            <div class="tail">
              <el-input
                class="search"
                placeholder="项目名称 或 项目代码"
                suffix-icon="el-icon-search"
                v-model="input"
                @keyup.enter.native="getMyOrder"
              >
              </el-input>
              <div class="mark">
                <div class="mark-content">
                  <div
                    class="status-point"
                    style="background-color: #f98319"
                  />
                  <span style="color: #f98319">进行中</span>
                </div>

                <div class="mark-content">
                  <div
                    class="status-point"
                    style="background-color: #999999"
                  />
                  <span style="color: #999999">未开始</span>
                </div>
                <div class="mark-content">
                  <div
                    class="status-point"
                    style="background-color: #176adb"
                  />
                  <span style="color: #176adb">已完成</span>
                </div>
              </div>
            </div>
          </div>
          <el-table
            :data="myOrder"
            style="width: 100%"
            :row-class-name="tableRowClassName"
            :cell-style="{ 'text-align': 'center' }"
            @row-click="handleRowClick"
            :header-cell-style="{
              background: '#176ADB',
              color: '#FFFFFF',
              'text-align': 'center',
            }"
          >
            <el-table-column
              prop="name"
              label="项目信息"
            > </el-table-column>
            <!-- <el-table-column label="投标（包)"></el-table-column> -->
            <el-table-column label="项目类型">
              <template slot-scope="scope">
                <div
                  class="bid"
                  style="display: flex; justify-content: center"
                >
                  <div style="margin-right: 12px">
                    <div
                      class="bidName"
                      style="margin-bottom: 4px"
                    >
                      <span>{{ scope.row.bidName }}</span>
                    </div>
                    <!-- <div class="bidCode">
                      <span>{{ scope.row.bidCode }}</span>
                    </div> -->
                  </div>
                  <div>
                    <div
                      class="label"
                      style="background: #4694ff; margin-bottom: 3px"
                    >
                      <span>{{ scope.row.bidType }}</span>
                    </div>
                    <div
                      class="label"
                      style="background: #03a98c"
                    >
                      <span>{{ scope.row.bidProfession }}</span>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="进程"
              width="600"
            >
              <template slot-scope="scope">
                <div class="timelineProcessBox">
                  <el-timeline class="timeline">
                    <el-timeline-item
                      class="lineitem"
                      :class="activity.done ? 'active' : 'inactive'"
                      v-for="(activity, index) in scope.row.activities"
                      :key="index"
                    >
                      <!-- :timestamp="activity.time" -->
                      <span style="display: flex; flex-direction: column">
                        <span style="margin: 10px 0; font-size: 16px">
                          {{ activity.content }}
                        </span>
                        <!-- <span style="color: #8c8c8c; font-size: 14px">
                          {{ activity.people }}
                        </span> -->
                      </span>
                    </el-timeline-item>
                  </el-timeline>
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="历时（天）"
              width="100"
            >
              <template slot-scope="scope">
                <span class="day">{{ scope.row.day }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="进入工作台"
              width="100"
            >
              <template slot-scope="scope">
                <svg-icon
                  style="cursor: pointer"
                  icon-class="computer"
                  @click="toPage(scope.row)"
                />
              </template>
            </el-table-column>
          </el-table>
          <el-pagination
            style="text-align:right"
            layout="prev, pager, next"
            :current-page.sync="orderPage.currentPage"
            :page-size="orderPage.pageSize"
            :total="orderPage.totalRecords"
            @current-change="handlePageChange"
          >
          </el-pagination>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { statistics, todoList, myProject } from "@/api/home/<USER>";
import { listIntention } from "@/api/tender/intention";
import { listNotice as listTenderNotice } from "@/api/tender/notice";
import { listNotice as listBidNotice } from "@/api/bidder/notice";
import { listProject as cancelNotice } from "@/api/cancel/project";

export default {
  name: "Index",
  dicts: ["busi_tender_mode"],
  data() {
    return {
      moreByTypePath: "cgxx/noticeInfo",
      searchProjectStatus: [],
      bodyCenterTitle: "待开标项目",
      companyInfo: {
        avatar:
          "https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png",
        companyName: "XXXXX公司名称",
        name: "李小明",
        WeChat: "XXXXXXXXXL",
        businessScope:
          "办公用品、工程服务、办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务、办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务、办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务、工程服务办公用品、工程服务办公用品、工程服务办公用品、工程服务。",
      },
      todoList: [
        {
          name: "待办事项A",
          date: "2024-06-17",
          data: { projectId: 0, intentionId: 0 },
          type: "0",
        },
      ],
      dataStatistics: [
        {
          name: "累计成交金额",
          value: 0,
        },
        {
          name: "累计成交项目",
          value: 0,
        },
        {
          name: "累计节约资金",
          value: 0,
        },
        {
          name: "累计发布项目",
          value: 0,
        },
      ],
      haveLength : 3,
      waitOpens:{},
      haveSignedUp: [
        // {
        //   title: "鹤壁市培红学校建设项目造价咨询、结算审",
        //   price: "130000.00",
        //   serveType: "审计服务",
        //   releaseTime: "2024-05-20",
        //   purchasingUnit: "鹤壁市教育体育局",
        //   deadline5: new Date("2024-06-15T18:00:00"),
        // },
        // {
        //   title: "鹤壁市培红学校建设项目造价咨询、结算审",
        //   price: "130000.00",
        //   serveType: "审计服务",
        //   releaseTime: "2024-05-20",
        //   purchasingUnit: "鹤壁市教育体育局",
        //   deadline5: new Date("2024-06-15T18:00:00"),
        // },
        // {
        //   title: "鹤壁市培红学校建设项目造价咨询、结算审",
        //   price: "130000.00",
        //   serveType: "审计服务",
        //   releaseTime: "2024-05-20",
        //   purchasingUnit: "鹤壁市教育体育局",
        //   deadline5: new Date("2024-06-15T18:00:00"),
        // },
        // {
        //   title: "鹤壁市培红学校建设项目造价咨询、结算审",
        //   price: "130000.00",
        //   serveType: "审计服务",
        //   releaseTime: "2024-05-20",
        //   purchasingUnit: "鹤壁市教育体育局",
        //   deadline5: new Date("2024-06-15T18:00:00"),
        // },
      ],
      activeName: "采购",
      informationPublicity: [],
      info: {
        currentPage: 1,
        pageSize: 5,
        totalRecords: 10,
      },
      input: "",
      activeOrder: "全部",
      myOrder: [
        // {
        //   name: "鹤壁市鹿鸣小学工程监理服务10KV新建变压器工程监理…",
        //   bidName: "测试多标段0929-B标段",
        //   bidCode: "E45000000280000246001002",
        //   bidType: "竞争性谈判",
        //   bidProfession: "房屋建筑工程",
        //   activities: [
        //     {
        //       content: "报名",
        //       time: "2018-04-12 20:46",
        //       people: "五六七",
        //       done: true,
        //     },
        //     {
        //       content: "评审",
        //       people: "吉吉国王",
        //       done: false,
        //       time: "2018-04-03 20:46",
        //     },
        //     {
        //       content: "中标结果",
        //       done: false,
        //       people: "熊大",
        //       time: "2018-04-03 20:46",
        //     },
        //     {
        //       content: "中标通知书",
        //       people: "",
        //       done: false,
        //       time: "",
        //     },
        //     {
        //       content: "合同签署",
        //       people: "",
        //       done: false,
        //       time: "",
        //     },
        //   ],
        //   day: "1",
        //   noticeType: 1,
        // },
      ],
      noticeType:1,
      orderPage: {
        currentPage: 1,
        pageSize: 5,
        totalRecords: 10,
      },
      treeDictListMap: {},
      failUrl:
        "https://cube.elemecdn.com/e/fd/0fc7d20532fdaf769a25683617711png.png",
    };
  },
  created() {
    this.treeDict
      .getdataTreeListMap({ isEnabled: 1 })
      .then((result) => {
        console.log("created", result);
        this.treeDictListMap = result;
      })
      .then(() => {
        this.init();
      });
  },
  activated() {
    this.init();
  },
  computed: {
    tenderModeMap() {
      // 使用reduce方法将数组转换为普通对象
      const obj = this.dict.type.busi_tender_mode.reduce(
        (accumulator, item) => {
          item.value = item.value + "";
          accumulator[item.value] = item.label;
          return accumulator;
        },
        {}
      );
      return obj;
    },
  },
  methods: {
    handleRowClick(row) {
      this.toPage(row);
    },
    handleMoreByType() {
      this.$router.push(this.moreByTypePath);
    },
    handleMore(path) {
      this.$router.push(path);
    },
    toPage(row) {
      this.$router.push(
        this.todoPage({
          type: row.projectStatus,
          data: { projectId: row.projectId },
        })
      );
    },
    todoPage(row) {
      let r = {};
      //控制跳转页面
      switch (row.type) {
        case 0:
          //发布采购项目
          r = {
            path: "tender/project/add/0",
            query: { projectIntentionId: row.data.intentionId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 5:
          //发布采购项目
          r = {
            path: "tender/project/add/" + row.data.projectId,
          };
          break;
        case 10:
          //发布采购公告
          r = {
            path: "tender/notice/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 15:
          //发布采购公告
          r = {
            path: "tender/notice/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 20:
          //待抽取评审专家
          r = {
            path: "expert/apply/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 30:
          //待处理开标情况
          r = {
            path: "bid/opening/add",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 40:
          //待处理评审情况
          r = {
            path: "bid/evaluation/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 50:
          //待发布中标结果公告
          r = {
            path: "bidder/notice/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 70:
          //待合同签署
          r = {
            path: "transaction/contract/add/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 80:
          //待归档
          r = {
            path: "process/info/edit/0",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
        case 90:
          //响应文件上传
          r = {
            path: "bidding/record/index",
            query: { projectId: row.data.projectId }, // 假设 row.id 是你要传递的参数
          };
          break;
      }
      return r;
    },
    getStatisticsData() {
      //获取数据统计
      statistics().then((result) => {
        if (result.code == 200) {
          this.dataStatistics[0].value = result.data.totalAmount;
          this.dataStatistics[1].value = result.data.totalNum;
          this.dataStatistics[2].value = result.data.economizeAmount;
          this.dataStatistics[3].value = result.data.procjetNum;
        }
      });
    },
    getTodoListData() {
      todoList().then((result) => {
        this.todoList = result.data.map((item) => {
          return {
            name: item.title,
            date: item.dueDate,
            data: item.data,
            type: item.type,
          };
        });
      });
    },
    getTenderIntentionData() {
      this.bodyCenterTitle = "采购意向";
      //获取采购意向
      listIntention({
        pageNum: 1,
        pageSize: 5,
        params: {
          returnEntName: true,
          orderByAsc: "intention_end_time",
          geIntentionEndTime: this.parseTime(
            new Date(),
            "{y}-{m}-{d} {h}:{i}:{s}"
          ),
        },
      }).then((result) => {
        this.haveSignedUp = result.rows.map((item) => {
          return {
            title: item.intentionName,
            price: item.budgetAmount,
            serveType: "服务工程",
            releaseTime: item.intentionStartTime,
            purchasingUnit: item.tendererName,
            deadline5: new Date(`${item.intentionEndTime}T23:59:59`),
          };
        });
      });
    },
    getWaitOpenBids() {
      this.bodyCenterTitle = "待开标项目";
      listTenderNotice({
        pageNum: 1,
        pageSize: 99,
        noticeStats: 1,
        params: {
          returnProject: true,
          orderByAsc: "bid_opening_time",
          // geBidOpeningTime: this.parseTime(
          //   new Date(),
          //   "{y}-{m}-{d} {h}:{i}:{s}"
          // ),
          projectStatus: 40,
        },
      })
        .then((result) => {
          //处理走马灯分页
          if(result.rows.length%4==0){
            this.haveLength = parseInt(result.rows.length/4);
          }else{
            this.haveLength = parseInt(result.rows.length/4)+1;
          }
          for(var i=0;i<result.rows.length;i++){
            console.log("i:", i);
            let item = result.rows[i];
            console.log("item:", item);
            let hl = 0;
            hl = parseInt(i/4);
            if(this.waitOpens[hl]==undefined){
              this.waitOpens[hl] = [];
            }
            console.log("waitOpens:", this.waitOpens[hl]);
            let industryArr = item.project.projectIndustry.split(",");
            let industry = this.treeDictListMap.get(
              parseInt(industryArr[industryArr.length - 1])
            );
            this.waitOpens[hl].push({
              projectId: item.projectId,
              title: item.project.projectName,
              price: item.project.budgetAmount,
              serveType: industry,
              releaseTime: item.bidOpeningTime,
              purchasingUnit: item.project.tendererName,
              deadline5: new Date(`${item.bidOpeningTime}`),
              deadline: new Date(`${item.bidOpeningTime}`) < new Date(),
            });
          }
          console.log("waitOpen:", this.waitOpens);
          this.haveSignedUp = result.rows.slice(0,4).map((item) => {
            let industryArr = item.project.projectIndustry.split(",");
            let industry = this.treeDictListMap.get(
              parseInt(industryArr[industryArr.length - 1])
            );
            return {
              projectId: item.projectId,
              title: item.project.projectName,
              price: item.project.budgetAmount,
              serveType: industry,
              releaseTime: item.bidOpeningTime,
              purchasingUnit: item.project.tendererName,
              deadline5: new Date(`${item.bidOpeningTime}`),
              deadline: new Date(`${item.bidOpeningTime}`) < new Date(),
            };
          });
        })
        .catch((err) => {});
    },
    getListNotice(type) {
      listTenderNotice({
        pageNum: this.info.currentPage,
        pageSize: this.info.pageSize,
        noticeStats: 1,

        // noticeType: type,
        params: { returnProject: true, isScope: true },
      }).then((result) => {
        this.info.totalRecords = result.total;
        this.informationPublicity = result.rows.map((item) => {
          return {
            id: item.noticeId,
            name: item.noticeName,
            unit: item.project.tendererName,
            method: this.tenderModeMap[item.project.tenderMode],
            date: item.noticeStartTime,
            noticeType: item.noticeType,
            projectId: item.projectId,
          };
        });
      });
    },
    getListBidNotice() {
      listBidNotice({
        pageNum: this.info.currentPage,
        pageSize: this.info.pageSize,
        params: { returnProject: true, isScope: true },
      })
        .then((result) => {
          this.info.totalRecords = result.total;
          this.informationPublicity = result.rows.map((item) => {
            return {
              id: item.noticeId,
              name: item.noticeName,
              unit: item.project.tendererName,
              method: this.tenderModeMap[item.project.tenderMode],
              date: item.noticeStartTime,
            };
          });
        })
        .catch((err) => {});
    },

    getListCancellNotice() {
      cancelNotice({
        pageNum: this.info.currentPage,
        pageSize: this.info.pageSize,
        params: { returnProject: true, isScope: true },
      })
        .then((result) => {
          this.info.totalRecords = result.total;
          this.informationPublicity = result.rows.map((item) => {
            console.log(result)
            return {
              id: item.noticeId,
              name: item.noticeName,
              unit: item.project.tendererName,
              method: this.tenderModeMap[item.project.tenderMode],
              date: item.noticeStartTime,
            };
          });
        })
        .catch((err) => {});
    },
    getMyOrder() {
      myProject({
        pageNum: this.orderPage.currentPage,
        pageSize: this.orderPage.pageSize,
        params: {
          projectStatusList: this.searchProjectStatus,
          key: this.input,
        },
      }).then((result) => {
        if (!result || !result.rows || result.rows.length === 0) {
          console.log("未获取到项目数据");
          return;
        }
        this.orderPage.totalRecords = result.total;
        this.myOrder = result.rows.map((item) => {
          let industryArr = "";
          if (
            item.projectIndustry != undefined ||
            item.projectIndustry != null
          ) {
            industryArr = item.projectIndustry.split(",");
          }
          let industry = this.treeDictListMap.get(
            parseInt(industryArr[industryArr.length - 1])
          );
          let yc = "结束";
          let ycd = false;
          if (item.projectStatus == -1) {
            yc = "取消采购";
            ycd = true;
          } else if (item.projectStatus == -2) {
            yc = "流标";
            ycd = true;
          } else if (item.projectStatus == 200) {
            yc = "结束";
            ycd = true;
          }
          return {
            name: item.projectName,
            projectId: item.projectId,
            bidCode: item.projectCode,
            bidType: this.tenderModeMap[item.tenderMode],
            bidProfession: industry,
            projectStatus: item.projectStatus,
            day: item.lastDay,
            activities: [
              {
                content: "报名中",
                done: item.projectStatus >= 20,
              },
              {
                content: "评审中",
                done:
                  item.projectStatus >= 20 &&
                  new Date(item.bidOpeningTime) <= new Date(),
              },
              {
                content: "中标结果",
                done: item.projectStatus >= 60,
              },
              {
                content: "中标通知书",
                done: item.projectStatus >= 70,
              },
              {
                content: "合同签署",
                done: item.projectStatus >= 80,
              },
              {
                content: yc,
                done: ycd,
              },
            ],
          };
        });
      });
    },
    init() {
      //获取数据统计
      this.getStatisticsData();
      //获取待办事项
      this.getTodoListData();
      //获取采购意向
      // this.getTenderIntentionData();
      //获取待开标项目
      this.getWaitOpenBids();
      //获取公示信息
      this.getListNotice(1);
      //获取公示信息
      //获取我的项目
      this.getMyOrder();
    },
    errorHandler() {
      return true;
    },
    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return "even-row";
      } else {
        return "odd-row";
      }
    },
    toggleActive(type, noticeType) {
    this.info.currentPage = 1;
    this.info.pageSize= 5,
    this.info.totalRecords= 0,
    console.log("------toggleActive start-------");
    console.log("info: ", this.info);

      this.noticeType = noticeType;
      this.activeName = type;
      if (noticeType === 1 || noticeType === 2) {
        this.getListNotice(noticeType);
        this.moreByTypePath = "cgxx/noticeInfo";
      } else if (noticeType === 3) {
        //采购结果公告
        this.getListBidNotice();
        this.moreByTypePath = "bhgl/notice";
      } else if (noticeType === 4) {
        //采购公告取消
        this.informationPublicity = [];
        this.getListCancellNotice();
      }
      console.log("------toggleActive end-------");
    },
    orderActive(type) {
      this.activeOrder = type;
      if ("未开标" == type) {
        this.searchProjectStatus = [10, 20];
      } else if ("已开标" == type) {
        this.searchProjectStatus = [30, 40, 50, 60];
      } else {
        this.searchProjectStatus = [];
      }
      this.getMyOrder();
    },
    cardGoto(item) {
      console.info("----------cardGoto----------");
      console.info(item);
      this.$router.push(
        "/tender/notice/noticeview/" +
          item.projectId +
          "?handleType=purchaserMain"
      );
    },
    goto(row) {
      if (this.noticeType == 1 || this.noticeType == undefined) {
        this.$router.push(
          "/tender/notice/noticeview/" +
            row.projectId +
            "?handleType=purchaserMain"
        );
        // }else if(this.noticeType==2){
        //   this.$router.push('/tender/notice/changeview/'+row.id);
      } else if (this.noticeType == 3) {
        this.$router.push("/bidder/notice/view/" + row.id);
      }
    },
    // 处理页码变化
    handlePageChange(page) {
      this.orderPage.currentPage = page;
      this.getMyOrder();
    },
    // 公示信息处理页码变化
    handlePageChangeInfo(page) {
      this.info.currentPage = page;
      if (this.noticeType === 1 || this.noticeType === 2) {
        this.getListNotice(this.noticeType);
        this.moreByTypePath = "cgxx/noticeInfo";
      } else if (this.noticeType === 3) {
        //采购结果公告
        this.getListBidNotice();
        this.moreByTypePath = "bhgl/notice";
      } else if (this.noticeType === 4) {
        //采购公告取消
        this.informationPublicity = [];
        this.getListCancellNotice();
      }
    },
  },
};
</script>

<style scoped lang="scss">
.home {
  background-color: #f5f5f5;

  .el-row {
    margin-bottom: 20px;
  }
  .companyInfo {
    .information {
      display: flex;
      margin-bottom: 15px;
    }
  }
  .todo-list {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
      height: 256px;
      overflow-y: auto;
    }
  }
  .data-statistics {
    height: 306px;
    ::v-deep .el-card__body {
      padding: 0 0 20px 0;
    }
    .date {
      display: grid;
      grid-template-rows: auto auto;
      grid-template-columns: auto auto;
      gap: 21px;
      justify-content: center;
      height: 235px;
      .number {
        width: 310px;
        height: 107px;
        padding: 15px 20px 20px 20px;
        .name {
          font-family: SourceHanSansSC-Medium;
          font-weight: 500;
          font-size: 15px;
          color: #ffffff;
          letter-spacing: 0;
          padding-left: 15px;
        }
        .value {
          font-family: SourceHanSansSC-Heavy;
          font-weight: 900;
          font-size: 37px;
          color: #ffffff;
          letter-spacing: 0;
          padding-top: 15px;
        }
      }
    }
  }
  .haveSignedUp {
    .date {
      display: flex;
      flex-direction: flex-start;
      flex-wrap: nowrap;
      align-content: center;
      justify-content: flex-start;
      gap: 1vw;
      width: 100%;
      align-items: center;
      .main {
        flex: none; /* 均匀分配剩余空间 */
        min-width: 0; /* 允许内容溢出以适应空间分配 */
        box-sizing: border-box; /* 确保边框和填充不会增加元素宽度 */
        width: 24%;
        height: 239px;
        background: #ffffff;
        border: 1px solid #176adb;
        position: relative;
        margin-bottom: 10px;
        .content {
          padding: 20px 22px 57px 22px;
          .title {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 显示的行数 */
            overflow: hidden;
            text-overflow: ellipsis;

            margin: 0;
            font-family: SourceHanSansSC-Heavy;
            font-weight: 900;
            font-size: 17px;
            color: #333333;
            letter-spacing: 0.7px;
            margin-bottom: 16px;
            text-align: center;
            line-height: 1.5; /* 设置行高 */
            height: 51px; /* 固定两行的高度，17px * 2 */
          }
          .price {
            display: flex;
            padding: 16px 0 24px 0;
            align-items: center;
            justify-content: space-between;
            align-content: center;
            flex-wrap: nowrap;
            flex-direction: row;
            .one {
              font-family: SourceHanSansSC-Heavy;
              font-weight: 900;
              font-size: 17px;
              color: #d91d18;
              letter-spacing: 1.6px;
            }
            .two {
              width: 86px;
              height: 20px;
              background: #176adb;
              border-radius: 3px;

              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 13px;
              color: #ffffff;
              letter-spacing: 0;
              text-align: center;
            }
          }
          .info {
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #9a9a9a;
            letter-spacing: 0;
          }
        }
        .time {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 250px;
          height: 38px;
          background: #176adb;
          border-radius: 68px 0 0 0;
          padding: 8px 18px 8px 49px;
          .word {
            ::v-deep .con .number {
              color: #ffffff;
              font-size: 15px;
              font-face: SourceHanSansSC;
              font-weight: 500;
              line-height: 0;
              letter-spacing: 0;
              paragraph-spacing: 0;
              text-align: left;
              line-height: 17px;
              white-space: nowrap;
            }
          }
          .word {
            color: #ffffff;
            font-size: 15px;
            font-face: SourceHanSansSC;
            font-weight: 500;
            line-height: 0;
            letter-spacing: 0;
            paragraph-spacing: 0;
            text-align: left;
          }
        }
      }
    }
  }
  .informationPublicity {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
    }
    ::v-deep .el-table .cell {
      .name {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        .label {
          width: 45px;
          height: 23px;
          background: #4694ff;
          border-radius: 3px;
          color: #ffffff;
          margin-right: 15px;
        }
      }
    }
    .announcement {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      .info {
        display: flex;
        .type {
          display: flex;
          flex-wrap: wrap;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          margin-left: 31px;
          .select {
            width: 60px;
            height: 22px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 15px;
            color: #666666;
            letter-spacing: 0;

            margin-right: 26px;
            cursor: pointer;
          }
          .active {
            color: #f98319;
          }
        }
      }
    }
  }
  .myOrder {
    ::v-deep .el-card__body {
      padding: 0 0 20px 0;
    }
    .timelineProcessBox {
      .timeline {
        display: flex;
        width: 95%;
        margin: 40px auto;
        .lineitem {
          transform: translateX(50%);
          width: 25%;
        }
      }
    }
    .order {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      .info {
        display: flex;
        .type {
          display: flex;
          flex-wrap: wrap;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          margin-left: 31px;
          .select {
            height: 22px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 15px;
            color: #666666;
            letter-spacing: 0;

            margin-right: 20px;
            cursor: pointer;
          }
          .active {
            color: #f98319;
          }
        }
      }
      .tail {
        margin-right: 30px;
        .search {
          width: 298px;
          border: 1px solid #176adb;
          border-radius: 5px;
        }
        .mark {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          justify-content: flex-end;
          align-items: center;
          margin-top: 8px;
          .mark-content {
            .status-point {
              display: inline-block;
              width: 6px;
              height: 6px;
              border-radius: 50%;
              margin-right: 5px;
            }
            span {
              width: 42px;
              height: 20px;
              font-family: SourceHanSansSC-Medium;
              font-weight: 500;
              font-size: 14px;
              letter-spacing: 0;
            }
          }
          .mark-content:not(:last-child) {
            margin-right: 20px;
          }
        }
      }
    }
    .bid {
      .bidName {
        span {
          font-family: SourceHanSansSC-Medium;
          font-weight: 500;
          font-size: 17px;
          color: #666666;
          letter-spacing: 0;
        }
      }
      .bidCode {
        span {
          font-family: SourceHanSansSC-Medium;
          font-weight: 500;
          font-size: 15px;
          color: #999999;
          letter-spacing: 0;
        }
      }
      .label {
        width: 102px;
        height: 24px;
        border-radius: 3px;
        span {
          font-family: SourceHanSansSC-Regular;
          font-weight: 400;
          font-size: 13px;
          color: #ffffff;
          letter-spacing: 0;
        }
      }
    }
    .day {
      font-family: SourceHanSansSC-Medium;
      font-weight: 500;
      font-size: 17px;
      color: #176adb;
      letter-spacing: 0;
    }
  }
  .clearfix {
    font-family: SourceHanSansSC-Bold;
    font-weight: 700;
    font-size: 22px;
    color: #333333;
    letter-spacing: 0;
  }
  .businessScope {
    text-align: justify;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 5; /* 显示的行数 */
    -webkit-box-orient: vertical;
  }
  .more {
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #176adb;
    letter-spacing: 0;
    float: right;
  }
}
</style>
<style scoped lang="scss">
::v-deep .el-card__header {
  border-bottom: none;
}
::v-deep .el-table .even-row {
  background: #eff6ff;
}

::v-deep .el-table .odd-row {
  background: #ffffff;
}

::v-deep .el-timeline-item__tail {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}
::v-deep .el-timeline-item__wrapper {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}
::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}
.active {
  ::v-deep .el-timeline-item__node {
    background-color: #176adb;
  }
  ::v-deep .el-timeline-item__tail {
    border-color: #176adb;
  }
}
// 有active样式的下一个li
// .active + li {
//   ::v-deep .el-timeline-item__node {
//     background-color:  rgb(249, 131, 25);
//   }
// }
</style>
