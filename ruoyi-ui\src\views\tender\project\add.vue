<template>
  <div style="margin-top: 20px" class="project" v-loading="loading">
    <el-form ref="busiTenderProject" :model="formData" :rules="rules" size="medium" label-width="0">
      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">项目意向</div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item label="" prop="projectIntentionId">
                        <el-select v-model="formData.projectIntentionId" ref="selectProjectIntention" placeholder="请选择" clearable :style="{ width: '100%' }" @change="handleProjectIntentionUpdate">
                          <el-option v-for="(item, index) in projectIntentionIdOptions" :key="index" :label="item.intentionName" :value="item.intentionId" :disabled="item.disabled"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目名称
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item label="" prop="projectIntentionId">
                        <el-input v-model="formData.projectName" placeholder="请输入项目名称" clearable :style="{ width: '100%' }">
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>采购方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="tenderMode">
                        <el-select v-model="formData.tenderMode" placeholder="请选择" clearable :style="{ width: '100%' }" @change="changeTenderMode($event)">
                          <el-option v-for="dict in dict.type.busi_tender_mode" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>

                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目行业
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="projectIndustryShow">
                        <el-cascader style="width: 100%" filterable clearable :props="{
                            ...cascaderSetting,
                            label: 'name',
                            expandTrigger: 'hover',
                          }" v-model="formData.projectIndustryShow"  :options="getTreeDataOptions(5)" @change="handleChange"></el-cascader>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目预计开始时间
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item label="" prop="projectStartTime">
                        <el-date-picker v-model="formData.projectStartTime" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="pickerOptionsOne" :style="{ width: '100%' }" placeholder="请选择日期" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>

                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目工期<br>
                      (服务期/供货期)
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="projectDuration">
                        <el-input placeholder="请输入项目工期（服务期/供货期）" v-model="formData.projectDuration" class="input-with-select">
                          <el-select disabled style="width: 100px" v-model="durationUnit" slot="append" placeholder="请选择">
                            <el-option label="日历日 " value="日历日"></el-option>
                            <el-option label="工作日" value="工作日"></el-option>
                          </el-select>
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>采购金额
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="budgetAmount">
                        <el-input v-model="formData.budgetAmount" clearable :style="{ width: '100%' }">
                          <template slot="append">元</template>
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>

                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>资金来源
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="tenderFundSource">
                        <el-select v-model="formData.tenderFundSource" placeholder="请选择" clearable :style="{ width: '100%' }">
                          <el-option v-for="dict in dict.type.tender_project_fundsource" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目所属地
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="projectAreaShow">
                        <el-cascader style="width: 100%" filterable clearable :props="{ label: 'name', ...cascaderSetting }" v-model="formData.projectAreaShow" :options="getTreeDataOptions(9)"></el-cascader>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目类型
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="projectType">
                        <el-select v-model="formData.projectType" placeholder="请选择" clearable :style="{ width: '100%' }"  :disabled="isProjectTypeDisabled" >
                          <el-option v-for="dict in dict.type.busi_project_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>获取投标人方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="projectType">
                        <el-select v-model="formData.getBidderMode" placeholder="请选择" clearable :style="{ width: '100%' }" >
                          <el-option v-for="dict in dict.type.get_bidder_mode" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>采购方信息</span>
            <span style="font-size: 12px;margin-left:20px;color: red;">如已委托代理机构，仅需填写项目名称，选择相应的代理机构后，点击推送给代理机构即可</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>委托代理采购
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="agencyFlag" style="float:left;">
                        <el-switch :disabled="isAgency" :active-value=1 :inactive-value=0 v-model="formData.agencyFlag" @change="changeAgencyFlag"></el-switch>
                      </el-form-item>

                      <el-button type="success" v-if="!isAgency && formData.agencyFlag == 1" style="float:left;margin-left:30px;" @click="saveForm">推送给代理机构</el-button>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.agencyFlag == 1">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>代理机构
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="agencyId">
                        <el-select v-model="formData.agencyId" placeholder="请选择" clearable filterable :style="{ width: '100%' }" @change="changeAgency($event)" :disabled="isAgency">
                          <el-option v-for="(item, index) in agencyIdOptions" :key="index" :label="item.entName" :value="item.entId" :disabled="item.disabled"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.agencyFlag == 1">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目联系人
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="agencyContactPerson">
                        <el-input v-model="formData.agencyContactPerson" placeholder="输入内容" clearable :style="{ width: '100%' }"></el-input>
                      </el-form-item>
                    </div>
                  </td>

                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>联系方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="agencyPhone">
                        <el-input v-model="formData.agencyPhone" placeholder="输入内容" clearable :style="{ width: '100%' }">
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>采购人
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="tendererId">
                        <el-select v-model="formData.tendererId" placeholder="请选择" clearable disabled :style="{ width: '100%' }" @change="changeTender($event)">
                          <el-option v-for="(item, index) in tendererIdOptions" :key="index" :label="item.entName" :value="item.entId" :disabled="item.disabled"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目联系人
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="tendererContactPerson">
                        <el-input v-model="formData.tendererContactPerson" clearable :style="{ width: '100%' }"></el-input>
                      </el-form-item>
                    </div>
                  </td>

                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>联系方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item prop="tendererPhone">
                        <el-input v-model="formData.tendererPhone" clearable :style="{ width: '100%' }"></el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box" v-if="formData.agencyFlag == 1">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>代理服务费收费标准</span>
            <span style="font-size: 12px;margin-left:20px;color: red;">注：默认值为参考格式，可根据实际情况自行自拟</span>
          </div>
          <div>
            <el-form-item prop="chargingStandard">
              <el-input type="textarea" :rows="4" placeholder="请输入代理服务费收费标准" v-model="formData.chargingStandard">
              </el-input>
            </el-form-item>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>采购项目概述</span>
            <span  style="font-size: 12px;margin-left:20px;color: red">采购项目概述限 100 字，请谨慎填写，确认后不可改。<!--剩余可输入{{ 100 - formData.projectContent.length }} 字--></span>
<!--            v-if="formData.projectContent.length <= 100"-->
          </div>
          <div>
            <el-form-item prop="projectContent">
<!--              <editor v-model="formData.projectContent" :min-height="192" />-->
              <textarea v-model="formData.projectContent" rows="8" style="width: 100%;" @input="limitTextLength">{{}}</textarea>
            </el-form-item>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>投标人资格</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td colspan="4" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>是否落实中小微企业等政策
                    </div>
                  </td>
                  <td colspan="8" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <!-- <el-select v-model="formData.toSme" placeholder="请选择是否针对小微企业" clearable>
                      <el-option
                        v-for="dict in dict.type.busi_tender_to_sme"
                        :key="dict.value"
                        :label="dict.label"
                        :value="dict.value"
                      />
                    </el-select> -->

            <el-form-item prop="toSme">
                    <el-radio v-model="formData.toSme" label="1">是</el-radio>
                    <el-radio v-model="formData.toSme" label="0">否</el-radio>
                  </el-form-item>
                  <!-- <span style="color: #999; font-size: 14px">请填写特定资格要求所需的证书文件的正式名称，多个证书文件之间使用;(分号)分割。如没有要求填 无</span> -->
                    </div>
                  </td>
                </tr>
                <tr >
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>特定资格要求
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
            <!-- <el-form-item prop="bidderQualification">
              <editor v-model="formData.bidderQualification" :min-height="100" />
              <span style="color: #999; font-size: 14px">温馨提示：如本项目有其他特定资格要求，可在现有的基础模板上进行修改。</span>
            </el-form-item> -->

            <el-form-item prop="bidderQualification">
              <el-input type="textarea" :rows="4" v-model="formData.bidderQualification" placeholder="请输入特定资格证书的正式名称" clearable :style="{ width: '100%' }"></el-input>
              <!-- <span style="color: #999; font-size: 14px">请填写特定资格要求所需的证书文件的正式名称，多个证书文件之间使用;(分号)分割。如没有要求填 无</span> -->
              <span style="color: #999; font-size: 14px">请填写特定资格要求所需内容，如没有要求填 无</span>
            </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
              </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr v-for="dict in dict.type.tender_project_attachment" :key="dict.label">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red" v-if="dict.raw.isEquals == 1">*</strong>{{ dict.label }}
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item class="upload">
                        <template>
                          <FileUpload :value="getFiles(dict)" @input="handleInput(dict, $event)" :fileType="['pdf', 'doc', 'docx']" :isShowTip="false"></FileUpload>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-form>

    <div slot="footer" class="option">
      <el-button type="primary" @click="submitForm">提交</el-button>
      <el-button @click="saveForm">保存</el-button>
      <el-button @click="close">关闭</el-button>
    </div>
  </div>
</template>
<script>
import { listIntention } from "@/api/tender/intention";
import {
  getProject,
  addProject,
  updateProject,
  getProjectInfo2,
} from "@/api/tender/project";
import { listInfo } from "@/api/ent/info";
import { getUserProfile } from "@/api/system/user";
import store from "@/store";

export default {
  components: {},
  dicts: [
    "busi_tender_mode",
    "base_yes_no",
    "get_bidder_mode",
    "busi_project_type",
    "tender_project_attachment",
    "tender_project_fundsource",
    "busi_tender_to_sme",
  ],
  props: [],
  data() {
    return {
      isAgency: false,
      projectIntentionIdBan: false,
      // 用户信息
      userInfo: null,
      //树形字典
      dataTreeOptionsMap: new Map(),
      dataTreeMap: new Map(),
      cascaderSetting: { value: "id", label: "name", children: "childrens" },
      // 遮罩层
      loading: true,
      intentionList: [],
      durationUnit: "日历日",
      isProjectTypeDisabled: false,
      formData: {
        projectIntentionId: "",
        isEmergencyProject: 0,
        chargingStandard:
          "按照《河南省招标代理服务收费指导意见》豫招协[2023]002号文件的规定收取招标代理服务费，由成交人在领取成交通知书时向代理机构一次性足额支付，否则，代理机构有权拒绝发放成交通知书，由此造成的后果由成交人自行承担；",
        projectName: "",
        projectStartTime: "",
        projectDuration: "",
        projectIndustry: "",
        projectArea: "",
        projectType: "0",
        tenderMode: "",
        agencyFlag: 0,
        agencyId: "",
        agencyCode: "",
        agencyName: "",
        agencyContactPerson: "",
        agencyPhone: "",
        tendererId: "",
        tendererCode: "",
        tendererName: "",
        tendererContactPerson: "",
        tendererPhone: "",
        projectContent: "",
        bidderQualification:"无",
        budgetAmount: "",
        controlPrice: "",
        tenderFundSource: "",
        tenderSelfFund: "",
        tenderFinancialFund: "",
        attachments: [],
        attachmentMap: {},
        getBidderMode: "",
        toSme: "0",
      },
      gcBidderQualification:"<p>1、符合《中华人民共和国政府采购法》第二十二条规定，且已在本系统注册的供应商。</p>" +
        "<p>2、落实政府采购政策满足的资格要求：（如属于专门面向中小企业采购的项目,供应商应为中小微企业、监狱企业、残疾人福利性单位)；</p>" +
        "<p>3、本项目的特定资格要求：（如属于特定行业项目,供应商应当具备特定行业法定准入要求)；</p>" +
        "<p>4、信用查询：以“中国政府采购网(www.ccgp.gov.cn)”—政府采购严重违法失信行为记录查询、“中国执行信息公开网（http://zxgk.court.gov.cn）”—失信被执行人查询和“信用中国（www.creditchina.gov.cn”）—信用服务—重点领域严重失信主体名单查询—政府采购严重违法失信行为记录名单、“全国建筑市场监管公共服务平台（https://jzsc.mohurd.gov.cn/）—信用建设—黑名单及失信联合惩戒记录”查询结果为准，对列入失信被执行人、政府采购严重违法失信行为记录名单、黑名单、失信联合惩戒记录名单的投标人，其投标无效，供应商可自行查询截图或提供信用承诺函</p>" +
        "<p>5、本项目不允许联合体投标</p>",

      qtBidderQualification:"<p>1、符合《中华人民共和国政府采购法》第二十二条规定，且已在本系统注册的供应商。</p>" +
        "<p>2、落实政府采购政策满足的资格要求：（如属于专门面向中小企业采购的项目,供应商应为中小微企业、监狱企业、残疾人福利性单位)；</p>" +
        "<p>3、本项目的特定资格要求：（如属于特定行业项目,供应商应当具备特定行业法定准入要求)；</p>" +
        "<p>4、信用查询：以“中国政府采购网(www.ccgp.gov.cn)”—政府采购严重违法失信行为记录查询、“中国执行信息公开网（http://zxgk.court.gov.cn）”—失信被执行人查询和“信用中国（www.creditchina.gov.cn”）—信用服务—重点领域严重失信主体名单查询—政府采购严重违法失信行为记录名单查询结果为准，对列入失信被执行人、政府采购严重违法失信行为记录名单的投标人，其投标无效，供应商可自行查询截图或提供信用承诺函；</p>" +
        "<p>5、本项目不允许联合体投标</p>",
      chargingStandard:
        "按照《河南省招标代理服务收费指导意见》豫招协[2023]002号文件的规定收取招标代理服务费，由成交人在领取成交通知书时向代理机构一次性足额支付，否则，代理机构有权拒绝发放成交通知书，由此造成的后果由成交人自行承担；",
      rules: {
        chargingStandard: [
          {
            required: true,
            message: "请选择",
            trigger: "blur",
          },
        ],
        projectIntentionId: [
          {
            required: false,
            message: "请选择",
            trigger: "change",
          },
        ],
        projectName: [
          {
            required: true,
            message: "请输入项目名称",
            trigger: "blur",
          },
        ],
        getBidderMode: [
          {
            required: true,
            message: "请选择获取投标人方式",
            trigger: "blur",
          },
        ],
        projectType: [
          {
            required: true,
            message: "请选择项目类型",
            trigger: "blur",
          },
        ],
        projectStartTime: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        projectDuration: [
          {
            required: true,
            message: "请输入项目工期",
            trigger: "blur",
          },
          {
            validator: this.validateProjectDuration,
            trigger: "blur",
          },
        ],
        projectIndustryShow: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        projectAreaShow: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        tenderMode: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        agencyId: [
          {
            required: !this.agencyFlag == 1 ? true : false,
            message: "请输入代理机构名称",
            trigger: "blur",
          },
        ],
        agencyContactPerson: [
          {
            required: !this.agencyFlag == 1 ? true : false,
            message: "请输入联系人",
            trigger: "blur",
          },
        ],
        agencyPhone: [
          {
            required: !this.agencyFlag == 1 ? true : false,
            message: "请输入联系方式",
            trigger: "blur",
          },
        ],
        tendererId: [
          {
            required: true,
            message: "请输入采购人名称",
            trigger: "blur",
          },
        ],
        tendererContactPerson: [
          {
            required: true,
            message: "请输入联系人",
            trigger: "blur",
          },
        ],
        tendererPhone: [
          {
            required: true,
            message: "请输入联系方式",
            trigger: "blur",
          },
        ],
        projectContent: [
          {
            required: true,
            message: "请输入采购内容",
            trigger: "blur",
          },
        ],
        bidderQualification: [
          {
            required: true,
            message: "请输入投标人资格",
            trigger: "blur",
          },
        ],
        budgetAmount: [
          {
            required: true,
            message: "请输入预算金额",
            trigger: "blur",
          },
        ],
        controlPrice: [
          {
            required: false,
            message: "请输入控制价",
            trigger: "blur",
          },
        ],
        tenderFundSource: [
          {
            required: false,
            message: "请输入资金来源",
            trigger: "blur",
          },
        ],
        tenderSelfFund: [
          {
            required: false,
            message: "请输入自筹资金",
            trigger: "blur",
          },
        ],
        tenderFinancialFund: [
          {
            required: false,
            message: "请输入财政资金",
            trigger: "blur",
          },
        ],
        toSme: [
          {
            required: true,
            message: "请选择是否针对小微企业",
            trigger: "blur",
          },
        ],
      },
      projectIntentionIdOptions: [],
      agencyIdOptions: [],
      tendererIdOptions: [],
      // 公告开始时间日期选择的禁用日期
      pickerOptionsOne: {
        disabledDate: (time) => {
          return time.getTime() < new Date() - 8.64e7;
        },
      },
      attachmentsMap: {},
    };
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.query.projectIntentionId) {
      this.projectIntentionIdBan = true;
    }
    this.treeDict
      .getdataTreeOptionsMap({ isEnabled: 1 })
      .then((result) => {
        this.dataTreeOptionsMap = result;
        return this.treeDict.getdataTreeListMap({ isEnabled: 1 });
      })
      .then((result) => {
        this.dataTreeMap = result;
        return this.getList();
      })
      .catch((err) => {
        console.error(" expert/info init err ", err);
      });
  },
  mounted() {
    this.getList().then(() => {
      if (this.$route.query.projectIntentionId) {
        this.formData.projectIntentionId = parseInt(
          this.$route.query.projectIntentionId
        );
        this.$refs.selectProjectIntention.$emit(
          "change",
          this.formData.projectIntentionId
        );
      }

      getProjectInfo2(this.$route.params.projectId).then((result) => {
        if (result.code == 200) {
          this.formData = result.data.tenderProject;
          this.formData.toSme = result.data.tenderProject.toSme+"";
          this.$nextTick(() => {
            window.scrollTo(0, 0); // 确保页面滚动到顶部
          });
          if (result.data.tenderProject.getBidderMode!=null){
            this.formData.getBidderMode=this.formData.getBidderMode+"";
          }
        }
      });
    });
  },
  methods: {
    changeProjectType(value){
      console.log("ProjectType",value)
      if(value==0){
        this.formData.bidderQualification = this.gcBidderQualification;
      }else{
        this.formData.bidderQualification = this.qtBidderQualification;
      }
    },
    // changeGetBidderMode(value){
    //   console.log("ProjectType",value)
    //   if(value==0){
    //     this.formData.getBidderMode = this.gcBidderQualification;
    //   }else{
    //     this.formData.bidderQualification = this.qtBidderQualification;
    //   }
    // },

    limitTextLength() {
      if (this.formData.projectContent.length > 100) {
        this.formData.projectContent = this.formData.projectContent.slice(0, 100);
      }
    },
    handleChange(value) {
      console.log('Selected:', value);
    },
    changeAgencyFlag() {
      if (this.formData.agencyFlag == 1) {
        this.formData.agencyId = "";
        this.formData.agencyCode = "";
        this.formData.agencyName = "";
        this.formData.agencyContactPerson = "";
        this.formData.agencyPhone = "";
        this.formData.chargingStandard = this.chargingStandard;
      }
    },
    validateProjectDuration(rule, value, callback) {
      if (value === "") {
        callback(new Error("请输入项目工期"));
      } else if (!/^[1-9]\d*(\.\d{1})?$/.test(value)) {
        callback(new Error("项目工期必须为大于 0 的小数，小数点保留一位"));
      } else {
        callback();
      }
    },
    cancel() {
      this.$tab.closePage();
    },
    getFiles(dict) {
      return this.formData.attachmentMap[parseInt(dict.value)];
    },
    handleProjectIntentionUpdate(val) {
      const selectedOption = this.projectIntentionIdOptions.find((option) => {
        return option.intentionId == this.formData.projectIntentionId;
      });

      // 如果找到了匹配的对象，selectedOption 将包含该对象，否则为 undefined
      if (selectedOption) {
        this.formData.projectName = selectedOption.intentionName + "采购项目";
        this.formData.tendererId = selectedOption.tendererId;
        this.formData.tendererName = selectedOption.tendererName;
      } else {
        console.log("没有找到匹配的选项");
      }
    },
    async getList() {
        // 根据项目ID判断是否添加addProject参数
        const intentionParams = { delFlag: 0 };
        if (this.$route.params.projectId == '0') {
            intentionParams.params = { addProject: true };
        }else {
            intentionParams.params = { addProject: false };
        }
        // 获取项目意向
      await listIntention(intentionParams).then(
        (response) => {
          this.projectIntentionIdOptions = response.rows;
        }
      );
      const entType = await this.getDicts("base_ent_type").then((res) => {
        return res.data;
      });
      const agencyType = entType.find((item) => item.dictLabel === "代理机构");
      const tendererType = entType.find(
        (item) => item.dictLabel === "采购单位"
      );
      // 获取代理机构选项
      await listInfo({ entType: agencyType.dictSort }).then((response) => {
        this.agencyIdOptions = response.rows;
      });
      // 获取采购人选项
      await listInfo({ entType: tendererType.dictSort }).then((response) => {
        this.tendererIdOptions = response.rows;
      });
      // 获取用户信息
      getUserProfile().then((response) => {
        if (response.data.ent) {
          if (this.$route.params.projectId == 0) {
            if (response.data.ent.entType == 1) {
              // this.formData.agencyFlag = 0;
              this.formData.tendererId = response.data.ent.entId;
              this.formData.tendererCode = response.data.ent.entCode;
              this.formData.tendererName = response.data.ent.entName;
              this.formData.tendererContactPerson =
                response.data.ent.entLinkman;
              this.formData.tendererPhone = response.data.ent.entContactPhone;
            } else if (response.data.ent.entType == 2) {
              // this.formData.agencyFlag = 1;
              this.formData.agencyId = response.data.ent.entId;
              this.formData.agencyCode = response.data.ent.entCode;
              this.formData.agencyName = response.data.ent.entName;
              this.formData.agencyContactPerson = response.data.ent.entName;
              this.formData.agencyPhone = response.data.ent.entName;
            }
          }
          if (response.data.ent.entType == 2) {
            this.isAgency = true;
          }
        }
      });
      this.loading = false;
    },
    changeAgency(value) {
      if (value) {
        const agency = this.agencyIdOptions.find(
          (item) => item.entId === value
        );
        this.formData.agencyCode = agency.entCode;
        this.formData.agencyName = agency.entName;
        this.formData.agencyContactPerson = agency.entLinkman;
        this.formData.agencyPhone = agency.entContactPhone;
        this.formData.chargingStandard = this.chargingStandard;
      } else {
        this.formData.agencyCode = "";
        this.formData.agencyName = "";
        this.formData.agencyContactPerson = "";
        this.formData.agencyPhone = "";
        this.formData.chargingStandard = this.chargingStandard;
      }
    },
    changeTenderMode(value) {
      if (["0", "4"].includes(value)) {
        this.$alert('未开放，请重新选择！', '提示', {
          confirmButtonText: '确定'
        });
        this.formData.tenderMode = "";
        this.formData.tenderModeName = "";
        return;
      }

      let dict = this.dict.type.busi_tender_mode;
      if (value) {
        let mode = dict.find((item) => item.value === value);
        this.formData.tenderModeName = mode.label;
        //如果是询价，则项目类型只能是货物
        if (value==3){
          this.formData.projectType="2"
          this.isProjectTypeDisabled = true;
        }else {
          this.isProjectTypeDisabled = false;
          this.formData.projectType=""
        }

      } else {
        this.formData.tenderMode = "";
        this.formData.tenderModeName = "";
      }
    },

    changeTender(value) {
      if (value) {
        const tender = this.tendererIdOptions.find(
          (item) => item.entId === value
        );

        this.formData.tendererCode = tender.entCode;
        this.formData.tendererName = tender.entName;
        this.formData.tendererContactPerson = tender.entLinkman;
        this.formData.tendererPhone = tender.entContactPhone;
      } else {
        this.formData.tendererCode = "";
        this.formData.tendererName = "";
        this.formData.tendererContactPerson = "";
        this.formData.tendererPhone = "";
      }
    },
    submitFormAndCreateNotice() {
      this.formData.busiState = 1;
      this.$refs["busiTenderProject"].validate((valid) => {
        if (!valid) return;
        if (Array.isArray(this.formData.projectIndustryShow)) {
          this.formData.projectIndustry =
            this.formData.projectIndustryShow.join(",");
        }
        if (Array.isArray(this.formData.projectAreaShow)) {
          this.formData.projectArea = this.formData.projectAreaShow.join(",");
        }

        this.formData.attachments = [].concat(
          ...Object.values(this.attachmentsMap)
        );

        addProject(this.formData).then((response) => {
          this.$modal.msgSuccess("新增成功");

          const projectId = response.data.projectId;
          // 关闭当前页签
          this.$tab.closeOpenPage(
            "/tender/notice/add/0?projectId=" + projectId
          );
          // this.$router.push();
        });
        //   }
      });
    },
    submitForm() {
      this.loading = true;
      this.formData.busiState = 1;
      for (let item of this.dict.type.tender_project_attachment) {
        if (
          item.raw.isEquals == 1 &&
          this.formData.attachmentMap[item.value] == undefined
        ) {
          this.$message.error(item.label + " 文件不能为空");
          return;
        }
      }
      this.$refs["busiTenderProject"].validate((valid) => {
        if (!valid) return;
        if (Array.isArray(this.formData.projectIndustryShow)) {
          this.formData.projectIndustry =
            this.formData.projectIndustryShow.join(",");
        }
        if (Array.isArray(this.formData.projectAreaShow)) {
          this.formData.projectArea = this.formData.projectAreaShow.join(",");
        }
        if (this.formData.agencyFlag == 0) {
          this.formData.agencyContactPerson = "";
          this.formData.agencyPhone = "";
          this.formData.agencyId = "";
          this.formData.agencyName = "";
          this.formData.agencyCode = "";
          this.formData.chargingStandard = this.chargingStandard;
        }

        addProject(this.formData).then((response) => {
          this.$modal.msgSuccess("新增成功");
          this.close();
        });
      });
      this.loading = false;
    },
    resetForm() {
      this.$refs["busiTenderProject"].resetFields();
    },
    saveForm() {
      this.loading = true;
      this.formData.busiState = 0;
      // 判断是否选择了代理机构
      if (this.formData.agencyFlag === 1 && (!this.formData.agencyId || this.formData.agencyId === "")) {
        this.$message.error("请选择代理机构");
        this.loading = false;
        return;
      }

      if (Array.isArray(this.formData.projectIndustryShow)) {
        this.formData.projectIndustry = this.formData.projectIndustryShow.join(",");
      }
      if (Array.isArray(this.formData.projectAreaShow)) {
        this.formData.projectArea = this.formData.projectAreaShow.join(",");
      }
      if (
        this.formData.projectName == "" ||
        this.formData.projectName == undefined
      ) {
        this.$modal.alertError("项目名称不能为空");
        return;
      }
      addProject(this.formData).then((response) => {
        this.$modal.msgSuccess("新增成功");
        this.close();
      });
      this.loading = false;
    },
    handleInput(dict, data) {
      //新增操作
      this.formData.attachmentMap[parseInt(dict.value)] = data;
    },
    /** 从map取值 */
    getTreeDataOptions(key) {
      let result = this.dataTreeOptionsMap.get(key)
        ? this.dataTreeOptionsMap.get(key).childrens
        : [];
      if (key == 9 && result && result.length > 0) {
        let a = result.find((item) => item.id == 5172);
        result = a.childrens;
      }
      return result;
    },
    // 关闭当前页
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.project {
  padding: 0 50px;
}
.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
  margin-bottom: 20px;
}
</style>
<style scoped>
.el-form-item {
  margin-bottom: 0px;
}

/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
