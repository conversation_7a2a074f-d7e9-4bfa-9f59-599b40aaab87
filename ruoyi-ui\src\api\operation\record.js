import request from '@/utils/request'

// 查询开标操作记录列表
export function listRecord(query) {
  return request({
    url: '/operation/record/list',
    method: 'get',
    params: query
  })
}

// 查询开标操作记录详细
export function getRecord(operationId) {
  return request({
    url: '/operation/record/' + operationId,
    method: 'get'
  })
}

// 新增开标操作记录
export function addRecord(data) {
  return request({
    url: '/operation/record',
    method: 'post',
    data: data
  })
}

// 修改开标操作记录
export function updateRecord(data) {
  return request({
    url: '/operation/record',
    method: 'put',
    data: data
  })
}

// 删除开标操作记录
export function delRecord(operationId) {
  return request({
    url: '/operation/record/' + operationId,
    method: 'delete'
  })
}
