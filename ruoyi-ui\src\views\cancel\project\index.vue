<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="取消时间">
        <el-date-picker
          v-model="daterangeCancelDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['cancel:project:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['cancel:project:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['cancel:project:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['cancel:project:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="取消id" align="center" prop="cancelId" /> -->
      <el-table-column label="项目名称" align="center" prop="project.projectName" />
      <!-- <el-table-column label="取消原因" align="center" prop="cancelReason" /> -->
<!--      <el-table-column label="取消状态" align="center" prop="cancelStatus" />-->
      <!-- <el-table-column label="取消状态" align="center" prop="cancelStatus" :formatter="formatCancelStatus" /> -->
      <el-table-column label="审核状态" align="center" prop="busiState" :formatter="formatCancelStatus" />

      <el-table-column label="取消时间" align="center" prop="cancelDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.cancelDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['cancel:project:query']"
          >查看详情</el-button>
<!--          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['cancel:project:edit']"
          >修改</el-button>-->
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['cancel:project:remove']"
          >删除</el-button>
          <!-- <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleAudit(scope.row)"
            v-hasPermi="['cancel:project:audit']"
          >审批</el-button> -->

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改取消采购项目对话框 -->
<!--    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item
          label="项目ID"
          prop="projectId"
        >
&lt;!&ndash;          <el-input
            v-model="form.projectId"
            placeholder="请输入项目ID"
          />&ndash;&gt;
          <el-select
            v-model="form.projectId"
            placeholder="请选择项目"
            ref="selectProject"
            filterable
            clearable
            :style="{ width: '100%' }"
            @change="change($event)"
          >
            <el-option
              v-for="(item, index) in projectIdOptions"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
              :disabled="item.projectStatus != 10"
            ></el-option>
          </el-select>
        </el-form-item>
&lt;!&ndash;        <el-form-item label="取消时间" prop="cancelDate">
          <el-date-picker clearable
                          v-model="form.cancelDate"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择取消时间">
          </el-date-picker>
        </el-form-item>&ndash;&gt;
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="form.cancelReason" type="textarea" placeholder="请输入内容" />
        </el-form-item>
&lt;!&ndash;        <el-form-item label="删除标记" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>&ndash;&gt;
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>-->
    <el-dialog
      :close-on-click-modal="false"
      title="审批项目"
      :visible.sync="open"
      width="80%"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
<!--        <el-form-item label="项目ID" prop="projectId">
          <el-input
            v-model="form.projectId"
            placeholder="请输入项目ID"
            :disabled="true"
          />
        </el-form-item>-->
        <el-form-item label="项目名称">
          <el-input
            v-model="form.project.projectName"
            placeholder="请输入项目名称"
            :disabled="true"
          />
        </el-form-item>
<!--        <el-form-item label="关闭项目ID" prop="closeProjectId">
          <el-input
            v-model="form.cancelId"
            placeholder="请输入关闭项目ID"
          />
        </el-form-item>-->
        <el-form-item label="审核状态" prop="busiState">
          <el-select v-model="form.busiState" placeholder="请选择审核状态">
            <el-option label="审核中" :value="0"></el-option>
            <el-option label="审核通过" :value="1"></el-option>
            <el-option label="审核不通过" :value="2"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProject, getProject, delProject,approveRequest, listTenderProject } from "@/api/cancel/project";

export default {
  name: "Project",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 取消采购项目表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记时间范围
      daterangeCancelDate: [],
      projectIdOptions: [],
      // 删除标记时间范围
      daterangeCreateTime: [],
      // 删除标记时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        cancelReason: null,
        cancelDate: null,
      },
      // 表单参数
      form: {
        project:{
          projectName:''
        }
      },
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    formatCancelStatus(row, column, cellValue) {
      switch (cellValue) {
        case 0:
          console.info(row)
          console.info(Object.keys(row.lastAuditProcess).length === 0)
          if(Object.keys(row.lastAuditProcess).length === 0){
            return '待提交';
          }else{
            return '被退回';
          }
        case 1:
          return '待审核';
        case 10:
          return '审核通过';
        default:
          return '未知状态';
      }
    },
    /** 查询取消采购项目列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCancelDate && '' != this.daterangeCancelDate) {
        this.queryParams.params["beginCancelDate"] = this.daterangeCancelDate[0];
        this.queryParams.params["endCancelDate"] = this.daterangeCancelDate[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      //获取可选项目list
        listTenderProject({ delFlag: 0}).then((response) => {
            this.projectIdOptions = response.rows;
            this.loading = false;
        });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      this.reset();
      const cancelId = row.cancelId || this.ids;
    var roles = this.$store.getters.roles;
    let isAdmin = false;
    for(var index in roles){
      if(roles[index]=='xiaoheAI'){
        isAdmin = true;
        break;
      }
    }
      if(row.busiState>0 || isAdmin){
      this.$router.push('/cancel/project/view/' + cancelId);
      }else{
        this.$router.push('/cancel/project/add/' + cancelId);
      }
    },
    handleAudit(row) {
      //this.reset();
      this.open = true;
      this.form=row;

    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        cancelId: null,
        projectId: null,
        cancelReason: null,
        cancelDate: null,
        delFlag: null,
        createTime: null,
        cancelStatus: null,
        busiState: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        project:{
          projectName:''
        }
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCancelDate = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.cancelId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
     /* this.reset();
      this.open = true;
      this.title = "添加取消采购项目";*/
        this.$router.push({
            path: "/cancel/project/add/0",
        });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const cancelId = row.cancelId || this.ids
      getProject(cancelId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改取消采购项目";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        approveRequest(this.form).then(response => {
          this.$modal.msgSuccess("审批成功");
          this.open = false;
          this.getList();
        });
       /* if (valid) {
          if (this.form.cancelId != null) {
            updateProject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }*/
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const cancelIds = row.cancelId || this.ids;
      this.$modal.confirm('是否确认删除取消采购项目编号为"' + cancelIds + '"的数据项？').then(function() {
        return delProject(cancelIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('cancel/project/export', {
        ...this.queryParams
      }, `project_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
