<template>
  <div class="discipline">
    <div class="titel">评审流程</div>
    <div class="clearfix">
      <div class="tips">
        <div class="module">
          <div class="demo" style="background-color: #15984b"></div>
          <div style="font-weight: 700">评审结束</div>
        </div>
        <div class="module">
          <div class="demo" style="background-color: #cc9c00"></div>
          <div style="font-weight: 700">评审中</div>
        </div>
        <div class="module">
          <div
            class="demo"
            style="background-color: #fff; border: 1px solid #176adb"
          ></div>
          <div style="font-weight: 700">未开始评审</div>
        </div>
      </div>
      <div style="width: 100%">
        <div class="line-1">
          <div
            id="qualification"
            class="item"
            @click="handleClick('资格性评审')"
          >
            <div>①</div>
            <div>资格性评审</div>
          </div>
        </div>
        <div style="height: 20px; display: flex">
          <div
            style="width: 50%; border-right: 1px solid #176adb; height: 100%"
          ></div>
          <div style="width: 50%; height: 100%"></div>
        </div>
        <div class="line-2">
          <div id="compliance" class="item" @click="handleClick('符合性评审')">
            <div>②</div>
            <div>符合性评审</div>
          </div>
        </div>


          <div style="height: 20px; display: flex" >
            <div
              style="width: 50%; border-right: 1px solid #176adb; height: 100%"
            ></div>
            <div style="width: 50%; height: 100%"></div>
          </div>
          <div v-if="projectInfo.tenderMode != 3" style="height: 20px; display: flex; margin-bottom: 10px" >
            <div
              style="width: 25%; border-right: 1px solid #176adb; height: 100%"
            ></div>
            <div
              style="
                width: 25%;
                border-right: 1px solid #176adb;
                border-top: 1px solid #176adb;
                height: 100%;
              "
            ></div>
            <div
              style="
                width: 25%;
                border-right: 1px solid #176adb;
                border-top: 1px solid #176adb;
                height: 100%;
              "
            ></div>
            <div style="width: 25%; height: 100%"></div>
          </div>
          <div class="line-3">
            <div v-if="projectInfo.tenderMode != 3" id="technical" class="item" @click="handleClick('技术标评审')">
              <div>④</div>
              <div>技术标评审</div>
            </div>
            <div v-if="projectInfo.tenderMode != 3" id="business" class="item" @click="handleClick('商务标评审')">
              <div>⑤</div>
              <div>商务标评审</div>
            </div>
<!--            <div
              id="tenderOffer"
              class="item"
              @click="handleClick('投标报价打分')"
            >
              <div>③</div>
              <div>投标报价打分</div>
            </div>-->
            <div
              id="tenderOffer"
              class="item"
              @click="handleClick(projectInfo.tenderMode != 3 ? '投标报价打分' : '投标报价打分')"
            >
              <div>③</div>
              <div>{{ projectInfo.tenderMode != 3 ? '投标报价打分' : '投标报价' }}</div>
            </div>
          </div>
          <div v-if="projectInfo.tenderMode != 3" style="height: 20px; display: flex; margin-top: 10px">
            <div
              style="width: 25%; border-right: 1px solid #176adb; height: 100%"
            ></div>
            <div
              style="
                width: 25%;
                border-right: 1px solid #176adb;
                border-bottom: 1px solid #176adb;
                height: 100%;
              "
            ></div>
            <div
              style="
                width: 25%;
                border-right: 1px solid #176adb;
                border-bottom: 1px solid #176adb;
                height: 100%;
              "
            ></div>
            <div style="width: 25%; height: 100%"></div>
          </div>

        <div style="height: 20px; display: flex">
            <div
              style="width: 50%; border-right: 1px solid #176adb; height: 100%"
            ></div>
            <div style="width: 50%; height: 100%"></div>
          </div>
        <div class="line-3">
          <div
            id="reviewSummary"
            class="item"
            style="
              border: #176adb solid 1px;
              background-color: #176adb;
              color: #fff;
            "
            @click="handleClick('专家复核')"
          >
            专家复核
          </div>
        </div>
      </div>
    </div>
    <div style="text-align: center; margin-top: 30px">
      <el-button  type="primary" plain @click="reviewSummary()"
        >进入评审汇总</el-button
      >
    </div>
    <el-dialog :visible.sync="dialogVisible" width="20%">
      <span>开始{{ this.currentReview.itemName }}</span>
      <span slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="StartReview()">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  approvalProcess,
  reviewStatus,
  expertInfoById,
} from "@/api/expert/review";
import { listInfo } from "@/api/evaluation/info";

export default {
  name: "comfirm",
  data() {
    return {
      // 评审项目及其状态
      reviewProject: [],
      leader: {},
      currentReview: {},
      evaluation: [],
      dialogVisible: false,
      isLeader: false,
      interval: null,
    };
  },
  props: {
    projectInfo: {},
  },
  methods: {
    init() {
      console.log(123, this.projectInfo);

      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));

      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(
        (response) => {
          if (response.code == 200) {
            console.log("log：",response.data.scoringMethodUinfo.scoringMethodItems)

            this.reviewProject =
              response.data.scoringMethodUinfo.scoringMethodItems;
           // if(this.projectInfo.tenderMode!=3){
              // 首先找到投标报价打分的item
              const item = this.reviewProject.find((item) => {
                return item.itemName == "投标报价打分";
              });
              // 存储scoringMethodItemID到localStorage里面
              localStorage.setItem(
                "tenderOfferScoringMethodItems",
                item.scoringMethodItemId
              );
              this.setState("tenderOffer", "投标报价打分"); // 投标报价打分 设置为状态 3


            // 设置不同的状态
            this.setState("qualification", "资格性评审"); // 资格性评审 设置为状态 1
            this.setState("compliance", "符合性评审"); // 符合性评审 设置为状态 2

            if(this.projectInfo.tenderMode!=3){
              this.setState("technical", "技术标评审"); // 技术标评审 设置为状态 3
              this.setState("business", "商务标评审"); // 商务标评审 设置为状态 3
            }

            // 如果是评审完成的状态，则返回到home页面
          } else {
            this.$messgae.warning(response.msg);
          }
        }
      );
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.leader = response.data.find((item) => {
            return item.expertLeader == 1;
          });
          if (this.leader.zjhm == this.$route.query.zjhm) {
            this.isLeader = true;
          }
        } else {
          this.$message.warning(response.msg);
        }
      });
      listInfo({ projectId: this.$route.query.projectId }).then((response) => {
        if (response.code == 200) {
          this.evaluation = response.rows;
          let currentTime = new Date();
          console.log(this.evaluation[0].evaluationEndTime != null);
          console.log(this.evaluation[0].evaluationEndTime != undefined);
          console.log(this.evaluation[0].evaluationEndTime != "");
          if (
            this.evaluation[0].evaluationEndTime != null &&
            this.evaluation[0].evaluationEndTime != undefined &&
            this.evaluation[0].evaluationEndTime != ""
          ) {
            let evaluationEndTime = new Date(
              this.evaluation[0].evaluationEndTime
            );
            if (currentTime > evaluationEndTime) {
              this.$alert("评审结束", "系统提示", {
                dangerouslyUseHTMLString: true,
                type: "success",
              })
                .then(() => {
                  this.$router.push({
                    path: "/expertHome",
                    query: { zjhm: this.$route.query.zjhm },
                  });
                })
                .catch(() => {});
            }
          }
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    setState(id, itemName) {
      const stateMap = { 0: "state3", 1: "state2", 2: "state1" };

      const item = this.reviewProject.find((item) => item.itemName == itemName);
      const element = document.getElementById(id);
      var state = "";
      if (!item) {
        element.remove();
        return;
      } else {
        const state = stateMap[item.status] || "";
        element.className = "item " + state;
      }
    },
    handleClick(type) {
      // 查找不同类型的评审对象
      const item = this.reviewProject.find((project) => {
        return project.itemName == type;
      });
      console.log(111,item)
      if (item) {
        this.currentReview = item;
        switch (item.status) {
          case 0:
            this.prompt(type);
          //  this.skipReview();
            break;
          case 1:
            this.enterReview(type);
            break;
          case 2:
            this.summaryInformation(type);
            break;
          default:
            break;
        }
      } else {
        this.$router.push({
          path: "/summaryConfirm",
          query: {
            projectId: this.$route.query.projectId,
            zjhm: this.$route.query.zjhm,
          },
        });
      }
    },
    // 查看评审汇总信息
    summaryInformation(type) {
      // 查找不同类型的评审对象
      const item = this.reviewProject.find((project) => {
        return project.itemName == type;
      });
      console.log(333,item)
      this.currentReview = item;
      console.log("this.currentReview", this.currentReview);
      this.finishSkip();
    },
    // 进入评审
    enterReview(type) {
      // 查找不同类型的评审对象
      const item = this.reviewProject.find((project) => {
        return project.itemName == type;
      });
      this.currentReview = item;
      this.skipReview();
    },
    // 提示未开始评审
    prompt(type) {
      // 判断用户是否为专家组长
      var leader_copy = false;
      if (this.leader.zjhm == this.$route.query.zjhm) {
        leader_copy = true;
      }
      if (leader_copy) {
        const result = this.commencingReview(type);
        if (result) {
          this.dialogVisible = true;
        }
      } else {
        this.$message.warning(type + "未开始评审");
      }
    },
    // 判断是否可以开始评审
    commencingReview(type) {
      // 首先判断所有节点中是否有评审中状态
      const res_one = this.reviewProject.some(
        (project) => project.status == "1"
      );
      if (res_one) {
        if (res_one.itemName !== "投标报价打分") {
          // 不进行操作
        } else {
          this.$message.warning("有评审中节点，不能开始评审");
          return false;
        }
      }
      // 判断其上层节点是否为评审结束状态
      const res_two = this.checkState(type);
      if (res_two) {
        return true;
      } else {
        this.$message.warning("上一评审节点未完成，不能开始评审");
        return false;
      }
    },
    // 判断上层评审的状态
    checkState(type) {
      // 查找不同类型的评审对象
      const qualificationReview = this.reviewProject.find(
        (project) => project.itemName === "资格性评审"
      );
      const conformityReview = this.reviewProject.find(
        (project) => project.itemName === "符合性评审"
      );
      const technicalReview = this.reviewProject.find(
        (project) => project.itemName === "技术标评审"
      );
      const businessReview = this.reviewProject.find(
        (project) => project.itemName === "商务标评审"
      );
      const tenderOfferReview = this.reviewProject.find(
        (project) => project.itemName === "投标报价打分"
      );
      switch (type) {
        case "资格性评审":
          return true;
        case "符合性评审":
          if (qualificationReview && qualificationReview.status == "2") {
            return true;
          }
          break;
        case "技术标评审":
        case "商务标评审":
        case "投标报价打分":
          if (conformityReview && conformityReview.status == "2") {
            return true;
          }
          break;
        default:
          return false;
      }
    },
    // 开始评审
    StartReview() {
      const data = {
        projectEvaluationId: this.evaluation[0].projectEvaluationId,
        scoringMethodItemId: this.currentReview.scoringMethodItemId,
        evaluationState: 1,
      };
      reviewStatus(data).then((response) => {
        if (response.code == 200) {
          this.skipReview();
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 选择跳转的评审
    skipReview() {
      // 将评审流程信息存入 localStorage
      localStorage.setItem(
        "evalProjectEvaluationProcess",
        JSON.stringify(this.currentReview.evalProjectEvaluationProcess)
      );

      // 构建公共的路由参数
      const routeParams = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: this.currentReview.scoringMethodItemId,
      };

      // 根据评审类型跳转
      const routes = {
        资格性评审: "/qualification",
        符合性评审: "/compliance",
        技术标评审: "/technical",
        商务标评审: "/business",
        投标报价打分: "/tenderOffer",
      };

      const path = routes[this.currentReview.itemName];
      if (path) {
        if (this.currentReview.itemName == "投标报价打分") {
          if (this.isLeader) {
            this.$router.push({ path, query: routeParams });
          } else {
            this.$message.warning("不是专家组长");
          }
        } else {
          this.$router.push({ path, query: routeParams });
        }
      }
    },
    // 进入评审汇总
    reviewSummary() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
      };
      this.$router.push({ path: "/summary", query: query });
    },
    // 节点完成进行跳转
    finishSkip() {
      localStorage.setItem(
        "evalProjectEvaluationProcess",
        JSON.stringify(this.currentReview.evalProjectEvaluationProcess)
      );
      switch (this.currentReview.itemName) {
        case "资格性评审":
          this.$router.push({
            path: "/qualification",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        case "符合性评审":
          this.$router.push({
            path: "/compliance",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        case "技术标评审":
          this.$router.push({
            path: "/technical",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        case "商务标评审":
          this.$router.push({
            path: "/business",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        case "投标报价打分":
          this.$router.push({
            path: "/tenderOffer",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        case "专家复核":
          this.$router.push({
            path: "/tenderOffer",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
              scoringMethodItemId: this.currentReview.scoringMethodItemId,
              finish: true,
            },
          });
          break;
        default:
          break;
      }
    },
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
  },
  mounted() {
    this.init();

    // 设置定时器，每5秒刷新一次页面
    this.interval = setInterval(() => {
      this.init();
    }, 5000);
  },
  beforeDestroy() {
    // 清除定时器
    clearInterval(this.interval);
  },
};
</script>

<style lang="scss" scoped>
.discipline {
  padding: 20px 30px;
  position: relative;
}
.module {
  display: flex;
  width: 200px;
  height: 37px;
  align-items: center;
  margin-bottom: 30px;
}
.demo {
  width: 94px;
  height: 37px;
  margin-right: 10px;
  border-radius: 6px;
  font-size: 14px;
  line-height: 150%;
  text-align: center;
}
.titel {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  padding: 20px 0;
}
.tips {
  margin-right: 20px;
  position: absolute;
  right: 50px;
}
.item {
  display: grid;
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
  justify-items: center;
  width: 260px;
  height: 80px;
  color: rgba(80, 80, 80, 1);
  border-radius: 6px;
  font-size: 18px;
  line-height: 150%;
  margin: 0 20px;
  cursor: pointer;
  border: 1px solid rgb(23, 106, 219);
}
/* 状态 1 */
.item.state1 {
  background-color: #15984b;
  color: #fff;
}

/* 状态 2 */
.item.state2 {
  background-color: #cc9c00;
  color: #fff;
}

/* 状态 3（无色） */
.item.state3 {
  background-color: transparent;
  border: #176adb solid 1px;
}
.line-1 {
  display: flex;
  width: 100%;
  justify-content: center;
}

.line-2 {
  display: flex;
  justify-content: center;
}

.line-3 {
  display: flex;
  justify-content: center;
}
</style>
