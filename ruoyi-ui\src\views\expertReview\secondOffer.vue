<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="info">
      <div class="content">
        <div style="position: relative;right: 60px;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0">二次报价</div>
        <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
          <el-table-column prop="priceNum"> </el-table-column>
          <el-table-column v-for="item in supplierNames" :key="item" :prop="item" :label="item"> </el-table-column>
        </el-table>
        <div class="operation">
          <el-button style="background: #F5F5F5;color:#176ADB" @click="open()" class="item-button">发起二次报价</el-button>
          <el-button @click="back()" class="item-button">返回</el-button>
        </div>
      </div>
    </div>
    <Foot></Foot>
  </div>
</template>

<script>
import { getUserProfile } from "@/api/system/user";
import { getSecondPriceInfoVos } from "@/api/again/quote";
import { addProcess } from "@/api/evaluation/process";
import { listInfo } from "@/api/evaluation/info";
import { formatTimestamp } from "@/utils/date";
export default {
  name: "expertInfo",
  data() {
    return {
      supplierNames: [],
      baseUrl: process.env.VUE_APP_BASE_API,
      userInfo: {},
      expert: {},
      tableData: [],
      node: "confirm",
      text_content: "",
      ws: null,
      timeoutObj: null,
      serverTimeoutObj: null,
      message: "",
      evaluationInfo: {},
      getDataFunction: null,
      getDataTime: 30000,
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  mounted() {
    this.init();
  },
  beforeDestroy() {
    // 组件销毁前清除定时任务
    if (this.getDataFunction) {
      clearInterval(this.getDataFunction);
      this.getDataFunction = null;
    }
  },
  methods: {
    // 连接websocket
    init() {
      // 获取用户信息
      const promise1 = getUserProfile().then((response) => {
        this.userInfo = response.data;
        this.expert = JSON.parse(localStorage.getItem("expertInfo"));
      });
      promise1
        .then(() => {
          this.join();
        })
        .catch((err) => { });
      const promise2 = listInfo({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code === 200) {
          if (response.rows && response.rows.length > 0) {
            this.evaluationInfo = response.rows[response.rows.length - 1];
          } else {
            this.evaluationInfo = {};
            this.$message.error("未查询到项目评审信息");
          }
        } else {
          this.$message.error(response.msg);
        }
      });

      promise2
        .then(() => {
          // 获取报价列表
          this.getListQuote();
        })
        .catch((err) => { });
      //加入聊天室
      this.getDataFunction = setInterval(() => {
        this.getListQuote();
      }, this.getDataTime);
      // this.getDataFunction = setInterval(function () {
      //   this.getListQuote();
      // }, this.getDataTime);
    },
    getListQuote() {
      getSecondPriceInfoVos(this.evaluationInfo.projectEvaluationId).then(
        (response) => {
          console.log("promise3", response);
          if (response.code == 200) {
            this.supplierNames = response.columns;
            this.tableData = response.data;
          } else {
            this.$message.error(response.msg);
          }
        }
      );
    },
    open() {
      this.$prompt('请输入二次报价等候时间', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d+$/,
        inputErrorMessage: '时间格式不正确'
      }).then(({ value }) => {
        this.sendSecondQuote(value);
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消输入'
        });
      });
    },
    sendSecondQuote(value) {
      this.message = "evalAgainQuote";
      // 请求接口保存 quote
      addProcess({
        projectEvaluationId: this.evaluationInfo.projectEvaluationId,
        scoringMethodItemId: this.$route.query.scoringMethodItemId,
        evaluationState: 1,
        remark: "二次报价",
        minutes: value,
      })
        .then((result) => {
          if (result.code === 200) {
            this.send();
          } else {
            this.$message.error(result.msg);
          }
        })
        .catch((err) => { });
    },
    join() {
      let socketUrl = this.baseUrl.replace("http", "ws");
      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.expert.resultId}/${this.$route.query.projectId}/0`;
      console.log(this.url);
      const wsuri = this.url;
      this.ws = new WebSocket(wsuri);
      const self = this;
      // 心跳检测函数
      const ws_heartCheck = {
        timeout: 60000, // 60秒
        timeoutObj: null,
        serverTimeoutObj: null,
        start: function () {
          this.timeoutObj = setTimeout(() => {
            // 这里发送一个心跳包
            self.ws.send("ping");
            this.serverTimeoutObj = setTimeout(() => {
              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接
            }, this.timeout);
          }, this.timeout);
        },
        reset: function () {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
          this.start();
        },
        stop: function () {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
        }
      };
      this.ws.onopen = function (event) {
        ws_heartCheck.start();
        self.text_content = self.text_content + "已经打开连接!" + "\n";
        console.log(self.text_content);
      };
      this.ws.onmessage = function (event) {
        if (event.data == "ping" || event.data == "连接成功") {
          // 表示心跳检测不做任何操作
        } else if (event.data == "signIn") {
        } else if (event.data == "bidPublicity") {
        } else if (event.data.includes("decryption")) {
        } else if (event.data == "supDecrytion") {
        } else if (event.data == "bidAnnouncement") {
        } else if (event.data == "quote") {
        } else if (event.data == "end") {
        } else {
        }
      };
      this.ws.onclose = function (event) {
        self.text_content = self.text_content + "已经关闭连接!" + "\n";
        console.log(self.text_content);
        //断开后自动重连
        ws_heartCheck.stop();
        self.join();
      };
    },
    // 断开websocket连接
    exit() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    },
    // 发送消息
    send() {
      if (this.ws) {
        this.ws.send(this.message);
        this.message = "";
      } else {
        alert("未连接到服务器");
      }
    },
    // 发送消息
    operateSend(message) {
      if (this.ws) {
        this.ws.send(message);
      } else {
        alert("未连接到服务器");
      }
    },
    // 返回到上个页面
    back() {
			this.$router.back();
      // this.$router.push({
      //   path: "/expertInfo",
      //   query: {
      //     projectId: this.$route.query.projectId,
      //     zjhm: this.$route.query.zjhm,
      //   },
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 64vh;
  margin: 20px 0;
  padding: 20px 110px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
