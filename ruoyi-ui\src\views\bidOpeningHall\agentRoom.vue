<template>
  <div>
    <BidHeadtwo ref="head" @updateStatus="handleStatus"></BidHeadtwo>
    <div class="bidOpeningHall">
      <el-card id="main" class="box-card">
        <div style="height: 10px;">
        <div style="padding: 5px 0 0 20px; float: left;">平台当前时间：<span >{{ currentTime }}</span></div>
        <div style="padding: 5px 20px 0 0; float: right;">
          聊天室连接状态：<span :style="`color:${isLink ? 'green' : 'red'}`">{{
            isLink ? "已连接" : "已断连，请刷新重连"
          }}</span>
        </div></div>
        <ready ref="ready" v-if="node == 'ready' && projectInfo" :projectInfo="projectInfo" @sendMessage="operateSend"></ready>
        <publicity ref="publicity" v-if="node == 'publicity'" :projectInfo="projectInfo" @sendMessage="operateSend"></publicity>
        <decryption ref="decryption" v-if="node == 'decryption' && projectInfo" :projectInfo="projectInfo" :userInfo="userInfo" @sendMessage="operateSend"></decryption>
        <bidAnnouncement ref="bidAnnouncement" v-if="node == 'bidAnnouncement'" @sendMessage="operateSend"></bidAnnouncement>
        <end ref="end" v-if="node == 'end'" @sendMessage="operateSend"></end>
      </el-card>
      <el-card class="box-card" style="width: 15%;">
        <div class="im">
          <div class="im-title">{{ userInfo.nickName }}</div>
          <div ref="messagesContainer" class="im-content" :style="{height: syncedHeight }">
            <div v-for="(itemc, indexc) in recordContent" :key="indexc">
              <div class="sysMessage" v-if="itemc.type == 0">
              </div>
              <div v-else>
                <div class="word" v-if="itemc.sendId !== userInfo.entId">
                  <div class="info">
                    <div class="message_time">
                      {{anonymous? "*******":itemc.sendName }}
                    </div>
                    <div class="info-content">{{ itemc.content }}</div>
                    <div class="message_time">
                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}
                    </div>
                  </div>
                </div>
                <div class="word-my" v-else>
                  <div class="info">
                    <div class="info-content">{{ itemc.content }}</div>
                    <div class="Sender_time">
                      {{ formatBidOpeningTimeTwo(itemc.sendTime) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="im-operation">
            <div style="margin-right:5px">
              <el-input v-model="message" placeholder="输入内容"></el-input>
            </div>
            <el-button style="height: 36px;background: #176ADB;color:#fff" @click="send">发送</el-button>
          </div>
        </div>
      </el-card>
    </div>
    <Foot></Foot>
  </div>
</template>

<script>
import Ready from "./agentComponent/ready.vue";
import publicity from "./agentComponent/publicity.vue";
import decryption from "./agentComponent/decryption.vue";
import bidAnnouncement from "./agentComponent/bidAnnouncement.vue";
import end from "./agentComponent/end.vue";

import {
  formatDateOption,
  getTodayStartWithDate,
  getTodayEndWithDate,
} from "@/utils/index";
import { bidInfo, chatHistory } from "@/api/onlineBidOpening/info";
import { getUserProfile } from "@/api/system/user";
import { getSystemTime } from "@/api/bid/opening";
export default {
  components: { Ready, publicity, decryption, bidAnnouncement, end },
  data() {
    return {
      node: "ready",
      userInfo: {},
      projectInfo: null,
      baseUrl: process.env.VUE_APP_BASE_API,
      message: "",
      text_content: "",
      ws: null,
      anonymous: true,
      recordContent: [
        {
          sendId: 1,
          content: "Nice to meet you.",
          sendTime: "2024-7-17 11:00:00",
          Sender: "张三",
        },
        {
          sendId: 2,
          content: "Nice to meet you.too",
          sendTime: "2024-7-17 11:01:00",
          Sender: "李四",
        },
        {
          sendId: 2,
          content: "How are you? ",
          sendTime: "2024-7-17 11:02:00",
          Sender: "李四",
        },
        {
          sendId: 1,
          content: "I'am fine,Thank you.",
          sendTime: "2024-7-17 11:03:00",
          Sender: "张三",
        },
      ],
      isLink: false,
      syncedHeight: '450px', // 初始高度
      currentTime:null
    };
  },
  watch: {
    node: {
      handler() {
        setTimeout(() => {
          var element = document.getElementById('main');
          console.log('element.clientHeight', element.offsetHeight);
          this.syncedHeight = element.offsetHeight - 120 + 'px'
        }, 10);
      },
      deep: true
    }
  },
  methods: {
    init() {
      // 获取开标项目信息
      const promise1 = bidInfo({
        bidOpeningTime: getTodayStartWithDate(),
        bidOpeningEndTime: getTodayEndWithDate(),
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.projectInfo = response.data;
        } else {
          this.$modal.msgwarning(response.msg);
        }
      });
      // 获取用户信息
      const promise2 = getUserProfile().then((response) => {
        this.userInfo = response.data;
      });

      Promise.all([promise1, promise2]).then((result) => {
        this.join();
      });
    },

    // 获取当前开标室流程
    handleStatus(data) {
      this.anonymous = this.$store.getters.agentBidOpenStatus >= 2 ? false : true;
      switch (data) {
        case "签到":
          this.node = "signIn";
          break;
        case "开标准备":
          this.node = "ready";
          break;
        case "投标人公示":
          this.node = "publicity";
          break;
        case "标书解密":
          this.node = "decryption";
          break;
        case "唱标":
          this.node = "bidAnnouncement";
          break;
        case "开标结束":
          this.node = "end";
          break;
      }
    },
    // 节点更新通知
    updateStatus() {
      this.$refs.head.getBidStatus();
    },
    // 格式化开标时间显示 年-月-日
    formatBidOpeningTime(time) {
      return formatDateOption(time, "date");
    },
    // 格式化开标时间显示 时-分-秒
    formatBidOpeningTimeTwo(time) {
      return formatDateOption(time, "time");
    },

    // 连接websocket
    join() {
			debugger
      let socketUrl = this.baseUrl.replace("http", "ws");
      console.log("socketUrl", socketUrl);
      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.userInfo.entId}/${this.$route.query.projectId}/0`;
      const wsurl = this.url;
      this.ws = new WebSocket(wsurl);
      const self = this;
      // 心跳检测函数
      const ws_heartCheck = {
        timeout: 5000, // 5秒
        timeoutObj: null,
        serverTimeoutObj: null,
        start: function () {
          this.timeoutObj = setTimeout(() => {
            // 这里发送一个心跳包
            self.ws.send("ping");
            this.serverTimeoutObj = setTimeout(() => {
              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接
            }, this.timeout);
          }, this.timeout);
        },
        reset: function () {
          clearTimeout(this.timeoutObj); // 在连接打开时启动心跳检测
          clearTimeout(this.serverTimeoutObj);
          this.start();
        },
        stop: function () {
          clearTimeout(this.timeoutObj);
          clearTimeout(this.serverTimeoutObj);
        }
      };
      this.ws.onopen = function (event) {
				debugger
        ws_heartCheck.start();
        self.text_content = self.text_content + "已经打开开标室连接!" + "\n";
        self.isLink = true;
        console.log(self.text_content);
      };
      this.ws.onmessage = function (event) {
				debugger
        console.log(event.data);
        if (event.data == "ping") {
          ws_heartCheck.reset(); // 收到消息后重置心跳检测
        } else if (event.data == "连接成功") {
        } else if (event.data == "signIn") {
          self.$refs.publicity.initdataList();
        } else if (event.data == "supDecrytion") {
          self.$refs.decryption.initdataList();
        } else if (
          event.data == "ready" ||
          event.data == "bidPublicity" ||
          event.data == "decryption" ||
          event.data == "nextStep" ||
          event.data == "bidAnnouncement" ||
          event.data == "end" ||
          event.data == "flowLabel"
        ) {
          self.updateStatus();
        } else {
          self.initChat();
        }
      };
      this.ws.onclose = function (event) {
        self.text_content = self.text_content + "已经关闭开标室连接!" + "\n";
        self.isLink = false;
        clearTimeout(ws_heartCheck.timeoutObj);
        clearTimeout(ws_heartCheck.serverTimeoutObj);
        //断开后自动重连
        ws_heartCheck.stop();
				
				debugger
        self.join();
      };
    },
    // 断开websocket连接
    exit() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    },
    // 发送消息
    send() {
      if (this.ws) {
        this.ws.send(this.message);
        this.message = "";
        this.scrollToBottom();
      } else {
        alert("未连接到开标室服务器");
      }
    },
    // 发送消息
    operateSend(message) {
      if (this.ws) {
        this.ws.send(message);
      } else {
        alert("未连接到开标室服务器");
      }
    },
    // 初始化聊天记录
    initChat() {
      chatHistory(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.recordContent = response.data;
        } else {
          this.recordContent = [];
        }
      });
    },
    // 处理滚动
    scrollToBottom() {
      this.$nextTick(() => {
        const container = this.$refs.messagesContainer;
        container.scrollTop = container.scrollHeight;
      });
    },
    updateTime() {
      var _this = this;
      getSystemTime().then((result) => {
        if(result.code==200){
          var ct = new Date(result.data);
          setInterval(function(){
            ct.setSeconds(ct.getSeconds() + 1);
            _this.currentTime = formatDateOption(ct, "cdatetime");
          }, 1000); // 每秒更新时间
        }
      });
    }
  },
  created() { },
  mounted() {
    this.init();
    this.initChat();
    
    this.updateTime();
  },
  updated() {
    this.scrollToBottom();
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-card__body {
  padding: 0;
}
</style>

<style scoped lang="scss">
.active {
  background-color: rgba(149, 250, 190, 1);
}
.bidOpeningHall {
  position: relative;
  background-color: #f5f5f5;
  display: flex;
  flex-wrap: nowrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.box-card {
  min-height: 600px;
  width: 50%;
  margin: 15px 5px;
}
.im {
  .im-title {
    width: 100%;
    height: 50px;
    background: #176adb;

    font-weight: 500;
    font-size: 16px;
    color: #ffffff;
    letter-spacing: 0;

    line-height: 50px;
    text-align: center;
  }
  .im-content {
    margin: 10px;
    background: #f5f5f5;
    height: 450px;
    overflow-y: auto;
  }
  .im-operation {
    display: flex;
    margin: 0 10px;
    margin-bottom: 10px;
    overflow: auto;
  }
}
.im-content {
  .word {
    display: flex;
    margin-bottom: 20px;

    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .info {
      width: 47%;
      margin-left: 10px;
      .Sender_time {
        padding-right: 12px;
        padding-top: 5px;
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
      }
      .message_time {
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
        line-height: 20px;
        margin-top: -5px;
        margin-top: 5px;
      }
      .info-content {
        word-break: break-all;
        // max-width: 45%;
        display: inline-block;
        padding: 10px;
        font-size: 14px;
        background: #fff;
        position: relative;
        margin-top: 8px;
        background: #dbdbdb;
        border-radius: 4px;
      }
      //小三角形
      .info-content::before {
        position: absolute;
        left: -8px;
        top: 8px;
        content: "";
        border-right: 10px solid #dbdbdb;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
      }
    }
  }

  .word-my {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 20px;
    img {
      width: 40px;
      height: 40px;
      border-radius: 50%;
    }
    .info {
      width: 90%;
      // margin-left: 10px;
      text-align: right;
      // position: relative;
      display: flex;
      align-items: flex-end;
      flex-wrap: wrap;
      flex-direction: column;
      .info-content {
        word-break: break-all;
        max-width: 45%;
        padding: 10px;
        font-size: 14px;
        // float: right;
        margin-right: 10px;
        position: relative;
        margin-top: 8px;
        background: #a3c3f6;
        text-align: left;
        border-radius: 4px;
      }
      .Sender_time {
        padding-right: 12px;
        padding-top: 5px;
        font-size: 12px;
        color: rgba(51, 51, 51, 0.8);
        margin: 0;
        height: 20px;
      }
      //小三角形
      .info-content::after {
        position: absolute;
        right: -8px;
        top: 8px;
        content: "";
        border-left: 10px solid #a3c3f6;
        border-top: 8px solid transparent;
        border-bottom: 8px solid transparent;
      }
    }
  }
}
</style>
