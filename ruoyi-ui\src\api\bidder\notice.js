import request from '@/utils/request'

// 查询中标结果公告信息列表
export function listNotice(query) {
  return request({
    url: '/bidder/notice/list',
    method: 'get',
    params: query
  })
}

// 查询中标结果公告信息详细
export function getNotice(noticeId) {
  return request({
    url: '/bidder/notice/' + noticeId,
    method: 'get'
  })
}

// 新增中标结果公告信息
export function addNotice(data) {
  return request({
    url: '/bidder/notice',
    method: 'post',
    data: data
  })
}

// 修改中标结果公告信息
export function updateNotice(data) {
  return request({
    url: '/bidder/notice',
    method: 'put',
    data: data
  })
}

// 删除中标结果公告信息
export function delNotice(noticeId) {
  return request({
    url: '/bidder/notice/' + noticeId,
    method: 'delete'
  })
}

// 生成公告内容
export function createNoticeContent(projectId, bidderInfoId) {
  return request({
    url: '/bidder/notice/createNoticeContent?projectId=' + projectId +'&bidderInfoId='+bidderInfoId,
    method: 'get'
  })
}
export function getBidderInfoByProjectID(projectId) {
  return request({
    url: '/bidding/info/getBidderInfoByProjectID/' + projectId,
    method: 'get'
  })
}
// 查询中标结果公告信息详细
export function view(noticeId) {
  return request({
    url: '/bidder/notice/view?noticeId=' + noticeId,
    method: 'get'
  })
}

// 查询中标结果公告信息详细
export function abortiveTenderNotice(data) {
  return request({
    url: '/bidder/notice/abortiveTenderNotice',
    method: 'post',
    data: data
  })
}
