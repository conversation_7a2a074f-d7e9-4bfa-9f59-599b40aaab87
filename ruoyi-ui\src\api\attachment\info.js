import request from '@/utils/request'

// 查询附件列表
export function listInfo(query) {
  return request({
    url: '/attachment/info/list',
    method: 'get',
    params: query
  })
}

// 查询附件详细
export function getInfo(attachmentId) {
  return request({
    url: '/attachment/info/' + attachmentId,
    method: 'get'
  })
}

// 新增附件
export function addInfo(data) {
  return request({
    url: '/attachment/info',
    method: 'post',
    data: data
  })
}

// 修改附件
export function updateInfo(data) {
  return request({
    url: '/attachment/info',
    method: 'put',
    data: data
  })
}

// 删除附件
export function delInfo(attachmentId) {
  return request({
    url: '/attachment/info/' + attachmentId,
    method: 'delete'
  })
}
