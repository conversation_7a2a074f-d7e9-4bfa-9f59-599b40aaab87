<template>
  <div
    style="margin-top: 20px;"
    v-loading="loading"
  >
    <el-form
      ref="elForm"
      :model="formData"
      :rules="rules"
      label-width="0"
      :disabled="isFormDisabled"
    >
      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>意向信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table
              cellspacing="0"
              style="width: 100%;table-layout:fixed;"
            >
              <tbody>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>意向名称
                    </div>
                  </td>
                  <td
                    colspan="22"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <el-form-item
                        prop="intentionName"
                        label=""
                      >
                        <el-input
                          v-model="formData.intentionName"
                          placeholder="请输入意向名称"
                        />
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>预算金额
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell cell-right-border">
                      <el-form-item
                        prop="budgetAmount"
                        label=""
                      >
                        <el-input-number
                          v-model="formData.budgetAmount"
                          :min="0.1"
                          :step="0.01"
                          :precision="2"
                          step-strictly
                          placeholder="请输入预算金额"
                          :style="{ width: '100%' }"
                        ></el-input-number></el-form-item>
                    </div>
                  </td>

                  <td
                    colspan="2"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell">
                      <strong style="color:red;">*</strong>计划开始时间
                    </div>
                  </td>
                  <td
                    colspan="10"
                    class="el-table__cell is-leaf"
                  >
                    <div class="cell cell-right-border">
                      <el-form-item
                        prop="budgetAmount"
                        label=""
                      >
                        <el-date-picker
                          clearable
                          v-model="formData.intentionStartTime"
                          type="date"
                          value-format="yyyy-MM-dd"
                          placeholder="请选择项目计划开始时间"
                          :style="{ width: '100%' }"
                          :picker-options="startDatePickerOptions"
                        >
                        </el-date-picker></el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>意向内容</span>
          </div>
          <div>
            <el-form-item
              label=""
              prop="intentionContent"
            >
              <editor
                v-model="formData.intentionContent"
                :read-only="isFormDisabled"
                :min-height="192"
              />
            </el-form-item>
          </div>
        </el-card>
      </el-col>

      <el-col
        :span="24"
        class="card-box"
      >
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div>
            <el-form-item
              v-for="dict in dict.type.busi_tender_intention_attachment"
              :key="dict.label"
              label-width="120px"
              :label="dict.label"
              class="upload"
            >
              <template>
                <FileUpload
                  :value="getImgPath(dict)"
                  @input="handleInput(dict, $event)"
                  :fileType="['pdf', 'doc', 'docx']"
                  :isShowTip="false"
                  :showOnly="isFormDisabled"
                ></FileUpload>
              </template>
            </el-form-item>
          </div>
        </el-card>
      </el-col>
    </el-form>

    <div
      slot="footer"
      class="dialog-footer"
      style="text-align: center;"
    >
      <el-button
        type="primary"
        @click="submitForm"
        v-show="!isFormDisabled"
      >确 定</el-button>
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script>
import {
  addIntention,
  getIntention,
  listIntention,
  updateIntention,
} from "@/api/tender/intention";
import { download } from "@/utils/request";
import { getDicts } from "@/api/system/dict/data";

export default {
  name: "add",
  dicts: [
    "busi_tender_mode",
    "busi_tender_intention_attachment",
    "base_yes_no",
  ],
  props: {
    // 定义props
  },
  data() {
    return {
      isFormDisabled: true,
      fixedDays: 0,
      // 遮罩层
      loading: false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购意向表格数据
      intentionList: [],
      attachmentTypes: [],
      evaluationList: [],
      attachmentMap: {},
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 (0正常 1删除)时间范围
      daterangeIntentionStartTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeIntentionEndTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeCreateTime: [],
      // 删除标记 (0正常 1删除)时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        intentionId: null,
        intentionCode: null,
        intentionName: null,
        intentionContent: null,
        budgetAmount: null,
        projectDuration: null,
        tenderMode: null,
        intentionStartTime: null,
        intentionEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      },
      // 表单参数
      formData: {
        intentionName: "",
        intentionStartTime: null,
        intentionEndTime: null,
        attachments: [],
      },
      // 表单校验
      rules: {
        intentionName: [
          { required: true, message: "请输入意向名称", trigger: "blur" },
        ],
        budgetAmount: [
          { required: true, message: "请输入预算金额", trigger: "blur" },
        ],
        intentionContent: [
          { required: true, message: "请输入意向内容", trigger: "blur" },
        ],
      },
      startDatePickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < new Date() - 8.64e7;
        },
      },
      endDatePickerOptions: {
        disabledDate: (time) => {
          const startTime = new Date(
            this.formData.intentionStartTime
          ).getTime();
          const endTime = startTime + this.fixedDays * 24 * 60 * 60 * 1000;
          return time.getTime() < endTime;
        },
      },
      attachmentsMap: {},
    };
  },
  created() {
    //1接收intentionId
    //2.新增是intentionId==0
    //3.openType == ck

    this.checkIntentionId();

    this.getAttachmentTypes();
    this.getConfigKey("sys.intention.fixedDays").then((response) => {
      this.fixedDays = response.msg;
    });
  },
  mounted() {
    this.getList();
  },
  methods: {
    checkIntentionId() {
      const intentionId = this.$route.params.intentionId;
      this.isFormDisabled =
        this.$route.params.isFormDisabled == "true" ? true : false;
      if (this.isFormDisabled) {
        this.$route.meta.title = "查看采购意向";
      } else {
        this.$route.meta.title = "修改采购意向";
      }
      if (intentionId == 0) {
        this.$route.meta.title = "新增采购意向";
      }
      if (intentionId) {
        // intentionId 存在，可以进行后续操作，比如加载详情
        this.loadIntentionDetail(intentionId);
      } else {
        // intentionId 不存在，可能是新增操作
        //.initNewIntention();
      }
    },
    loadIntentionDetail(intentionId) {
      // 加载已有意图详情的逻辑
      getIntention(intentionId).then((response) => {
        this.formData = response.data;
        // 使用reduce方法将attachments数组收集成一个映射
        this.attachmentsMap = response.data.attachments.reduce(
          (accumulator, attachment) => {
            // 如果accumulator中还没有这个fileType的键，则创建一个新数组
            if (!accumulator[attachment.fileType]) {
              accumulator[attachment.fileType] = [];
            }
            // 将当前attachment添加到对应fileType的数组中
            accumulator[attachment.fileType].push(attachment);
            return accumulator;
          },
          {}
        );
        console.log(this.attachmentMap);
      });
    },
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
      console.log(this.attachmentsMap);
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    /** 查询采购意向列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (
        null != this.daterangeIntentionStartTime &&
        "" != this.daterangeIntentionStartTime
      ) {
        this.queryParams.params["beginIntentionStartTime"] =
          this.daterangeIntentionStartTime[0];
        this.queryParams.params["endIntentionStartTime"] =
          this.daterangeIntentionStartTime[1];
      }
      if (
        null != this.daterangeIntentionEndTime &&
        "" != this.daterangeIntentionEndTime
      ) {
        this.queryParams.params["beginIntentionEndTime"] =
          this.daterangeIntentionEndTime[0];
        this.queryParams.params["endIntentionEndTime"] =
          this.daterangeIntentionEndTime[1];
      }
      if (null != this.daterangeCreateTime && "" != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] =
          this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && "" != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] =
          this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listIntention(this.queryParams).then((response) => {
        this.intentionList = response.rows;
        this.total = response.total;
      });
      this.loading = false;
    },
    /** 提交按钮 */
    submitForm() {
      this.loading = true;

      this.formData.attachments = [].concat(
        ...Object.values(this.attachmentsMap)
      );
      for (let item of this.dict.type.busi_tender_intention_attachment) {
        if (
          item.raw.isEquals == 1 &&
          this.attachmentsMap[item.value] == undefined
        ) {
          this.$message.error(item.label + " 文件不能为空");
          return;
        }
      }
      this.$refs["elForm"].validate((valid, error) => {
        if (valid) {
          if (this.formData.intentionId != null) {
            updateIntention(this.formData).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
              this.reset();
              this.$tab.closePage();
            });
          } else {
            addIntention(this.formData).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
              this.reset();
              this.$tab.closePage();
            });
          }
        }
      });
      this.loading = false;
    },
    // 表单重置
    reset() {
      this.formData = {
        intentionId: null,
        intentionCode: null,
        intentionName: null,
        intentionContent: null,
        budgetAmount: null,
        projectDuration: null,
        tenderMode: null,
        intentionStartTime: null,
        intentionEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        attachments: [], // 清空附件内容
      };
      this.resetForm("elForm");
      this.attachmentMap = {}; // 清空附件映射
    },

    downLoadFile(id, value) {
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length - 1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId: id,
        fileName: fileName,
        filePath: value,
        resource: value,
      };

      download("/common/download/resource", params, fileName);

      // downLoadFile(params).then(response => {
      //   if (response["code"]==200){
      //   }
      // });

      //根据文件路径参数，按斜杠进行分割，取得文件名，这是download函数需要的第三个参数
      /*
        /!** request里面的download下载函数 *!/
        //download函数是若依自带的，第一个参数是请求的url路径，不需要变，这个路径下的controller后台方法也是若依写好封装好了的。
        console.log("文件名");*/
    },

    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50;
      if (!isRightSize) {
        this.$message.error("文件大小超过 50MB");
      }
      // let isAccept = new RegExp('.pdf').test(file.type)
      // if (!isAccept) {
      //   this.$message.error('应该选择.pdf类型的文件')
      // }
      return isRightSize && isAccept;
    },
    getAttachmentTypes() {
      getDicts("busi_tender_intention_attachment").then((result) => {
        console.info(result);
        if (result.code == 200) {
          console.info("attachment result" + result);
          console.info(this.attachmentTypes);
          console.info(this.attachmentMap);
          this.attachmentTypes = result.data;
        }
      });
    },
    cancel() {
      // this.open = false;
      // this.reset();
      this.$tab.closePage();
    },
  },
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 0px;
}
/* 添加样式 */
</style>
