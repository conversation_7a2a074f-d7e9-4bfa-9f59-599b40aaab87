<!-- 代理人投标人公示 -->
<template>
  <div class="decryption" v-loading="loading">
    <el-table :data="decryption" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
      <el-table-column type="index" label="序号" width="100">
      </el-table-column>
      <el-table-column prop="bidderName" label="投标供应商">
      </el-table-column>
      <el-table-column prop="attachments[0].fileMd5" label="投标文件识别码">
      </el-table-column>
      <el-table-column prop="busiBidderInfo.decodeFlag" label="解密状态" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.busiBidderInfo == null">未签到</div>
          <div v-if="scope.row.busiBidderInfo != null && scope.row.busiBidderInfo.decodeFlag == 1">已解密</div>
          <div v-if="scope.row.busiBidderInfo != null &&scope.row.busiBidderInfo.decodeFlag == 0" style="color:#176ADB">未解密</div>
          <div v-if="scope.row.busiBidderInfo != null &&scope.row.busiBidderInfo.decodeFlag == -1" style="color:#ff0000">解密失败</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="decryption-line-one">
      <div class="decryption-time">
        输入解密时间:
        <el-input v-model="minutes" class="countdown-style" placeholder="00">
        </el-input>
        分钟
      </div>
      <div class="decryption-countdown">
        <el-statistic format="解密结束倒计时：HH小时mm分ss秒" :value="deadline" time-indices>
        </el-statistic>
      </div>
    </div>
    <div class="decryption-line-two">
      <el-button v-if="showOne" class="decryption-button" @click="startDecryption">开始解密</el-button>
      <el-button style="background-color:#db1717" v-if=" showTwo && failureOfBid" class="decryption-button" @click="failureBid">流标</el-button>
      <el-button v-if="showTwo && !failureOfBid" class="decryption-button" @click="nextStep">下一步</el-button>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { dataList, operationRecord } from "@/api/onlineBidOpening/info";
import { formatDateOption } from "@/utils/index";
import { failureOfBid as failure, getSystemTime } from "@/api/bid/opening";
import { listRecord } from "@/api/operation/record";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    projectInfo: {},
    userInfo: "",
  },
  data() {
    //这里存放数据
    return {
      loading: true,
      decryption: [],
      headStyle: {
        "text-align": "center",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
      },
      cellStyle: {
        "text-align": "center",
        height: "90px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
      minutes: "",
      decryptionDeadline: "",
      deadline: "",
      systemTime: "",
      showOne: false,
      showTwo: false,
      operationInfo: [],
      failureOfBid: false,
      currentTime:null
    };
  },

  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 计算解密结束时间
    calculateDecryptionTime() {
      const currentDate = new Date(); // 获取当前时间
      const decryptionDate = new Date(
        currentDate.getTime() + this.minutes * 60000
      ); // 当前时间 + 指定分钟数
      this.decryptionDeadline = formatDateOption(decryptionDate); // 格式化计算后的时间
    },
    checkTime() {
      var _this = this;
      if(this.deadline!=null && this.deadline!=""){
            var ct = new Date(this.currentTime);
            setInterval(function(){
              ct.setSeconds(ct.getSeconds() + 1);
              _this.currentTime = formatDateOption(ct, "cdatetime");
              console.log(_this.currentTime);
            }, 1000); // 每秒更新时间
          }
    },
    // 初始化投标人公示+标书解密列表+唱标列表
    initdataList() {
            console.log("deadLine1 : ", this.deadline);
            console.log("currentTime1 : ", this.currentTime);
      
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.decryption = response.data;
          var successNum = 0;
          var allNum = 0;
          this.decryption.map((item) => {
            allNum++;
            if (
              item.busiBidderInfo != null &&
              item.busiBidderInfo.decodeFlag == 1
            ) {
              successNum++;
            }
          });
            console.log("successNum : ", successNum);
            console.log("allNum : ", allNum);
          this.failureOfBid = true;
          if(this.currentTime > this.deadline){
            if(successNum > 2){
              this.failureOfBid = false;
              this.showTwo = true;
            }
          }else if(successNum == allNum){
            this.failureOfBid = false;
            this.showTwo = true;
          }else{
            this.showTwo = false;
          }
              
          this.loading = false;
        }
      });
    },
    // 开始解密
    startDecryption() {
      this.calculateDecryptionTime();
      // 记录操作
      if (this.minutes != "") {
        operationRecord({
          projectId: this.$route.query.projectId,
          operationType: 3,
          operationTime: formatDateOption(new Date()),
          minutes: this.minutes,
        }).then((response) => {
          if (response.code == 200) {
            // 广播开始解密，提醒供应商跳转到解密页面，并把解密时间传过去，开始倒计时
            //this.deadline = new Date(this.decryptionDeadline);
            this.systemTime =new Date(response.data.systemTime);
            this.deadline =new Date(response.data.decryptionTime);

            this.buttonNextStep();
            this.initdataList();
            this.showOne = false;
            this.$emit("sendMessage", "decryption");
          } else {
            this.$modal.msgwarning(msg);
          }
        });
      } else {
        this.$message.warning("请填写解密结束倒计时");
      }
    },
    // 解密下一步
    nextStep() {
      operationRecord({
        projectId: this.$route.query.projectId,
        operationType: 4,
        operationTime: formatDateOption(new Date()),
        decryptionTime: formatDateOption(this.decryptionDeadline),
      }).then((response) => {
        if (response.code == 200) {
          this.$emit("sendMessage", "nextStep");
        } else {
          this.$modal.msgwarning(msg);
        }
      });
    },
    formatterDeleteStatus(row, column, cellValue, index) {
      if (cellValue === 1) {
        return "已解密";
      } else if (cellValue === 0) {
        return "未解密";
      } else if (cellValue === -1) {
        return "解密失败";
      }
    },
    // 开始解密按钮是否显示
    buttonShow() {
      listRecord({
        projectId: this.$route.query.projectId,
        operationType: 3,
      }).then((response) => {
        if (response.code == 200) {
          if (response.rows.length > 0) {
            this.deadline = new Date(response.rows[0].decryptionTime);
            this.currentTime = new Date(response.rows[0].systemTime);
          }
          var operationInfo = response.rows;
          if (
            this.$store.getters.agentBidOpenStatus == 3 &&
            operationInfo.length == 0
          ) {
            this.showOne = true;
          }
          this.initdataList();
        }
      });
    },
    // 下一步按钮是否显示
    buttonNextStep() {
      listRecord({
        projectId: this.$route.query.projectId,
        operationType: 4,
      }).then((response) => {
        if (response.code == 200) {
          var operationInfo = response.rows;
          if (
            this.$store.getters.agentBidOpenStatus == 3 &&
            operationInfo.length == 0
          ) {
            this.showTwo = true;
          }
        }
      });
    },
    // 流标操作
    failureBid() {
      const data = {
        projectId: this.$route.query.projectId,
        abortiveType: 4,
      };
      this.$modal
        .confirm("是否进行流标操作？")
        .then(() => {
          failure(data).then((res) => {
            if (res.code == 200) {
              this.$modal.msgSuccess("流标成功");
              this.$emit("sendMessage", "flowLabel");
            }
          });
        })
        .catch(() => { });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.buttonShow();
    this.buttonNextStep();
    // this.initdataList();
  },
  beforeCreate() { }, //生命周期 - 创建之前
  beforeMount() { }, //生命周期 - 挂载之前
  beforeUpdate() { }, //生命周期 - 更新之前
  updated() { }, //生命周期 - 更新之后
  beforeDestroy() { }, //生命周期 - 销毁之前
  destroyed() { }, //生命周期 - 销毁完成
  activated() { }, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.decryption {
  padding: 25px 25px;
  .decryption-line-one {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 25px 0;
    .decryption-time {
      width: 45%;
      height: 105px;

      background: #ededed;
      color: #333333;
      font-weight: 700;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      .countdown-style {
        background-color: transparent;
        border: none;
        width: 100px;
        ::v-deep .el-input__inner {
          background-color: transparent;
          border: none;
          font-weight: 700;
          font-size: 20px;
        }
      }
    }
    .decryption-countdown {
      width: 45%;
      height: 105px;

      background: #176adb;
      display: flex;
      align-items: center;
      justify-content: center;
      ::v-deep .number {
        color: #fff;
        font-weight: 700;
      }
      .append-text {
        background-color: transparent; /* 背景透明 */
        border: none; /* 去掉边框 */
        font-weight: 700;
        font-size: 20px;
      }
    }
  }
  .decryption-line-two {
    display: flex;
    align-items: center;
    justify-content: center;
    .decryption-button {
      width: 164px;
      height: 45px;
      background: #176adb;

      color: #fff;
      font-weight: 700;
    }
  }
}
</style>
