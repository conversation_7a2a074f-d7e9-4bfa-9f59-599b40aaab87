<template>
  <div class="three">
    <div style="width:70%">
      <div style="position: relative;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;">商务标评审</div>
      <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
        <el-table-column prop="供应商名称" width="180">
        </el-table-column>
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.xm" :label="item.xm">
        </el-table-column>
      </el-table>

      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin:20px">评审结果：</div>
        <div style="display: flex;">
          <div style="display:flex;margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;" v-for="(item,index) in result" :key="index">
            <div style="display:flex;align-items: center;white-space:nowrap;margin-right:10px;">{{ item.gys }}</div> <el-input disabled v-model="item.result">
            </el-input>
          </div>
        </div>
      </div>

      <div class="operation" v-if="!finish">
        <el-button
          class="item-button"
          style="background-color: #f5f5f5;color: #176adb;"
          @click="reviewed"
        >重新评审</el-button>
        <el-button class="item-button" @click="completed">节点评审完成</el-button>
      </div>
      <div v-else class="operation">
        <el-button class="item-button" @click="back">返回</el-button>
      </div>
    </div>
    <div style="width:30%">
      <div class="result">
        <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;margin-bottom:15px">表决结果</div>

        <el-input class="text" type="textarea" :rows="20" placeholder="请输入表决结果" v-model="votingResults">
        </el-input>
      </div>
    </div>
  </div>

</template>

<script>
import { leaderSummaryQuery } from "@/api/expert/review";
import { updateProcess } from "@/api/evaluation/process";
import { reEvaluationTwo } from "@/api/evaluation/expertStatus";

export default {
  props: {
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      result: [],
      isReadOnly: false,
      votingResults: "",
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
      };
      leaderSummaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.votingResults = response.data.bjjgsb
          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          this.columns = response.data.tableColumns;

          this.result = this.generateResultTable(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );

          // if (response.data.evaluationProcess) {
          //   let psjg = JSON.parse(
          //     response.data.evaluationProcess.evaluationResult
          //   );
          //   this.result = psjg;
          //   if (response.data.evaluationProcess.evaluationState == 2) {
          //     this.isReadOnly = true;
          //   }
          // }
          // if (this.result == null) {
          //   this.result = this.generateResultTable(
          //     response.data.tableColumns,
          //     response.data.busiBidderInfos,
          //     response.data.tableData
          //   );
          // }
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 resultId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.resultId] = column.xm;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.resultId;
          transformedRow[column.xm] = row[itemId] || "0"; // 默认为 '0' 如果没有找到对应值
        });

        return transformedRow;
      });
    },
    // 组装评审结果
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, bidder.bidderName])
      );

      // Generate the result table、
      return tableData.map((row) => {
        return {
          bidder: row.gys,
          gys: bidderMap.get(row.gys),
          result: this.getMode(row),
        };
      });
    },
    // 获取众数
    getMode(row) {
      const data = row;
      // 移除特定的键
      delete data.gys;
      // 提取所有的值
      const values = Object.values(data);
      // 计算每个值出现的次数
      const frequencyMap = values.reduce((acc, value) => {
        acc[value] = (acc[value] || 0) + 1;
        return acc;
      }, {});
      // 找出出现次数最多的值
      let maxFrequency = 0;
      let mode = null;
      for (const [value, frequency] of Object.entries(frequencyMap)) {
        if (frequency > maxFrequency) {
          maxFrequency = frequency;
          mode = value;
        }
      }
      return mode;
    },
    // 节点评审完成
    completed() {
      //检查所有的 result 是否为空
      const allResultsNotEmpty = this.result.every(
        (item) => item.result !== ""
      );
      // const allResultsNotEmpty = true;
      if (!allResultsNotEmpty) {
        console.log("请填写评审结果");
        this.$message.warning("请完善评审结果");
        return false;
      }
      // else {
      const evaluationProcessId = JSON.parse(
        localStorage.getItem("evalProjectEvaluationProcess")
      );
      const data = {
        evaluationProcessId: evaluationProcessId.evaluationProcessId,
        evaluationResult: JSON.stringify(this.result),
        evaluationState: 2,
        evaluationResultRemark: this.votingResults,
      };
      updateProcess(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
            },
          });
        } else {
          this.$message.warning();
        }
      });
      // }
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
    getIconClass(value) {
      return value == "1" ? "el-icon-check" : "el-icon-circle-close";
    },
    // 重新评审
    reviewed() {
      const query = {
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).projectEvaluationId,
        expertResultId: JSON.parse(localStorage.getItem("evalExpertScoreInfo"))
          .expertResultId,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).scoringMethodItemId,
      };
      reEvaluationTwo(query).then((res) => {
        if (res.code == 200) {
          const evaluationProcessId = JSON.parse(
            localStorage.getItem("evalProjectEvaluationProcess")
          );

          reEvaluate(evaluationProcessId.evaluationProcessId).then(
            (response) => {
              if (response.code == 200) {
                this.$emit("send", "one");
              } else {
                this.$message.warning(resposne.msg);
              }
            }
          );
        }
      });
    },
    flowLabel() {
      this.dialogVisible = true;
    },
    // 确认流标
    confirmflow() {
      if (this.reasonFlowBid == "") {
        const data = {
          projectId: this.$route.query.projectId,
          remark: this.reasonFlowBid,
        };
        abortiveTenderNotice(data).then((response) => {
          if (response.code == 200) {
            // 跳转到哪个页面
          } else {
            this.$message.warning(response.msg);
          }
        });
      } else {
      }
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 20px 40px;
  display: flex;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #176adb;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.result {
  text-align: left;
  margin-left: 20px;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 20px;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
