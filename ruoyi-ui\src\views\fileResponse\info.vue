<template>
  <div>
    <el-header height="100px">响应文件制作</el-header>
    <el-container>
      <el-aside width="230px">
        <div style="min-height: 740px">
          <div
            class="title-button"
            :class="{ btnActive: step == '1' }"
            @click="active('1', 'basicInfo', '项目基本信息','开标一览表')"
            :autofocus="true"
          >
            项目基本信息
          </div>
          <div
            class="title-button"
            :class="{ btnActive: step == '4' }"
            @click="active('2', 'bidText', '采购文件正文')"
          >生成响应文件正文</div>
          <div
            class="title-button"
            :class="{ btnActive: step == '3' }"
            @click="active('3', 'biddingDocuments')"
          >响应文件加密</div>
          <!-- <div
            class="title-button"
            :class="{ btnActive: step == '2' }"
            @click="active('2')"
          >开标一览表</div>
          <div
            class="title-button"
            :class="{ btnActive: step == '3' }"
            @click="active('3')"
          >评分办法</div>
          <div
            class="title-button"
            :class="{ btnActive: step == '4' }"
            @click="active('4')"
          >采购文件正文</div>
           -->
        </div>
      </el-aside>
      <el-main>
        <div v-if="step == '1'">
          <basicInfo
            @saveSuccess="handleSaveSuccess"
            ref="basicInfo"
          ></basicInfo>
        </div>
        <div v-if="step == '2'">
          <bidText
            ref="bidText"
            @saveSuccess="handleSaveSuccess"
          ></bidText>
        </div>
        <div v-if="step == '3'">
          <biddingDocuments
            ref="biddingDocuments"
            @saveSuccess="handleSaveSuccess"
          ></biddingDocuments>
        </div>
        <!--
        <div v-if="step == '2'">
          <bidInformation></bidInformation>
        </div>
        <div v-if="step == '3'">
          <scoringMethod></scoringMethod>
        </div>

         -->
      </el-main>
    </el-container>
  </div>
</template>

<script>
import basicInfo from "./components/basicInfo";
// import bidInformation from "./components/bidInformation";
import biddingDocuments from "./components/biddingDocuments";
// import scoringMethod from "./components/scoringMethod";
import bidText from "./components/bidText";
import { infoByParams } from "@/api/documents/uinfo";
export default {
  components: {
    basicInfo,
    // bidInformation,
    biddingDocuments,
    // scoringMethod,
    bidText,
  },
  name: "Page401",
  data() {
    return {
      projectId: "",
      projectInfo: {},
      uItemsMap: {},
      uInfo: {},
      step: "1",
      resultInfo: {},
    };
  },
  mounted() {
    this.init(this.$route.query.projectId);
  },
  methods: {
    handleSaveSuccess(newProjectInfo) {
      this.projectInfo = newProjectInfo;
      localStorage.setItem(
        `fileResponseProjectInfo_${this.projectId}`,
        JSON.stringify(newProjectInfo)
      );
    },
    init(projectId) {
      let projectInfoJsonStr = localStorage.getItem(
        `fileResponseProjectInfo_${this.projectId}`
      );
      if (projectInfoJsonStr) {
        this.projectInfo = JSON.parse(projectInfoJsonStr);
      }
      this.projectId = projectId;
      this.getUinfoByProjectId(projectId)
        .then(() => {
          this.$nextTick(() => {
            this.active("1", "basicInfo", "项目基本信息", "开标一览表");
          });
        })
        .catch((err) => {});
    },
    active(stepNumber, refName, name, extName) {
      let uItem = name ? this.uItemsMap[name] : false;
      let extUitem = extName ? this.uItemsMap[extName] : false;
      this.step = stepNumber;
      //加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(
          this.projectId,
          this.projectInfo,
          uItem,
          this.uInfo,
          extUitem
        );
      });
    },
    getUinfoByProjectId(projectId) {
      // 获取项目uInfo信息
      return infoByParams({
        projectId: projectId,
      })
        .then((result) => {
          this.uInfo = result.data;
          this.uInfo.uItems.forEach((item) => {
            this.uItemsMap[item.itemName] = item;
          });
        })
        .catch((err) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: #409eff !important; /* 激活状态下的字体颜色 */
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;

  .title-button {
    font-size: 20px;
    color: #333;
    margin: 40px 0;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.head {
  display: flex;
  justify-content: space-between;
}
</style>
