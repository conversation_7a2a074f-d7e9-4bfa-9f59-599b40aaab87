<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
          <el-input type="textarea" :rows="25" placeholder="2、 清洁卫生服务范围及要求。
服务范围：
①门诊楼的卫生保洁(4 人)、住院楼卫生保洁(20 人)、院区外围 环境卫生(2 人)、环境消杀、绿化养护(1 人)。
服务要求：
1）门诊楼、住院楼及院区内环境，所有地面、墙面、天花板、灯饰、风口等内容的清洁、保洁、消毒工作以及垃圾收集、清运工作。
2）定期杀灭四害（蚊、蝇、蟑、鼠），定期检查和报告白蚁虫害情况，并做到无滋生源。
3）负责对医院进行防虫、防蛀专业消杀服务。
4）负责制订清洁工作方案、制度和标准，报采购人审定以对工作质量进行考核。
②院区内外秩序维护服务范围及要求
服务范围：
1）院区外围巡逻、急诊楼、病房楼（18 人）。
2）监控、消防控制室管理（4 人，须具有消防中级证书）。
3）院内停车场管理。
服务要求：
管理人员持证上岗，24 小时巡逻值守，停车场值守并负责车辆管理引导。" v-model="requirement">
          </el-input>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      itemContent: {
        projectOverview:{
          requirement:""
        },
        standardsAndReq:{
          requirement:""
        }
      },
      requirement: "",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        if(this.itemContent.standardsAndReq && this.itemContent.standardsAndReq.requirement){
          this.requirement = this.itemContent.standardsAndReq.requirement;
        }
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      console.log("this.requirement.", this.requirement);
      this.itemContent.standardsAndReq.requirement = this.requirement;
      console.log("this.itemContent.", this.itemContent);
      let newContent = JSON.stringify(this.itemContent);
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newContent,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
  mounted() { },
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
.text {
  color: rgba(255, 87, 51, 1);
  font-size: 16px;
  line-height: 100%;
  text-align: left;
  text-indent: 2em;
}
</style>
