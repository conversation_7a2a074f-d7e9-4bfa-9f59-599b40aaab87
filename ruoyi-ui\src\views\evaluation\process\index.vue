<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目评审进度id" prop="evaluationProcessId">
        <el-input
          v-model="queryParams.evaluationProcessId"
          placeholder="请输入项目评审进度id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目评审信息id" prop="projectEvaluationId">
        <el-input
          v-model="queryParams.projectEvaluationId"
          placeholder="请输入项目评审信息id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评分细则id" prop="scoringMethodItemId">
        <el-input
          v-model="queryParams.scoringMethodItemId"
          placeholder="请输入评分细则id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评审状态，0未开始 1评审中 10评审结束" prop="evaluationState">
        <el-input
          v-model="queryParams.evaluationState"
          placeholder="请输入评审状态，0未开始 1评审中 10评审结束"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="评审结果说明，记录对评审结果的说明情况" prop="evaluationResultRemark">
        <el-input
          v-model="queryParams.evaluationResultRemark"
          placeholder="请输入评审结果说明，记录对评审结果的说明情况"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['evaluation:process:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['evaluation:process:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['evaluation:process:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['evaluation:process:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="processList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="项目评审进度id" align="center" prop="evaluationProcessId" />
      <el-table-column label="项目评审信息id" align="center" prop="projectEvaluationId" />
      <el-table-column label="评分细则id" align="center" prop="scoringMethodItemId" />
      <el-table-column label="评审状态，0未开始 1评审中 10评审结束" align="center" prop="evaluationState" />
      <el-table-column label="评审结果，json格式，记录所有供应商的最终评审信息" align="center" prop="evaluationResult" />
      <el-table-column label="评审结果说明，记录对评审结果的说明情况" align="center" prop="evaluationResultRemark" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['evaluation:process:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['evaluation:process:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改项目评审进度对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目评审进度id" prop="evaluationProcessId">
          <el-input v-model="form.evaluationProcessId" placeholder="请输入项目评审进度id" />
        </el-form-item>
        <el-form-item label="项目评审信息id" prop="projectEvaluationId">
          <el-input v-model="form.projectEvaluationId" placeholder="请输入项目评审信息id" />
        </el-form-item>
        <el-form-item label="评分细则id" prop="scoringMethodItemId">
          <el-input v-model="form.scoringMethodItemId" placeholder="请输入评分细则id" />
        </el-form-item>
        <el-form-item label="评审状态，0未开始 1评审中 10评审结束" prop="evaluationState">
          <el-input v-model="form.evaluationState" placeholder="请输入评审状态，0未开始 1评审中 10评审结束" />
        </el-form-item>
        <el-form-item label="评审结果说明，记录对评审结果的说明情况" prop="evaluationResultRemark">
          <el-input v-model="form.evaluationResultRemark" placeholder="请输入评审结果说明，记录对评审结果的说明情况" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProcess, getProcess, delProcess, addProcess, updateProcess } from "@/api/evaluation/process";

export default {
  name: "Process",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 项目评审进度表格数据
      processList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记，0正常 1删除时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        evaluationProcessId: null,
        projectEvaluationId: null,
        scoringMethodItemId: null,
        evaluationState: null,
        evaluationResult: null,
        evaluationResultRemark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询项目评审进度列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listProcess(this.queryParams).then(response => {
        this.processList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        evaluationProcessId: null,
        projectEvaluationId: null,
        scoringMethodItemId: null,
        evaluationState: null,
        evaluationResult: null,
        evaluationResultRemark: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        remark: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.evaluationProcessId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加项目评审进度";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const evaluationProcessId = row.evaluationProcessId || this.ids
      getProcess(evaluationProcessId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改项目评审进度";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.evaluationProcessId != null) {
            updateProcess(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProcess(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const evaluationProcessIds = row.evaluationProcessId || this.ids;
      this.$modal.confirm('是否确认删除项目评审进度编号为"' + evaluationProcessIds + '"的数据项？').then(function() {
        return delProcess(evaluationProcessIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('evaluation/process/export', {
        ...this.queryParams
      }, `process_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
