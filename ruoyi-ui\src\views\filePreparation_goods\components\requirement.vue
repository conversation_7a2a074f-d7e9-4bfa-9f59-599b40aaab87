<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <el-button class="item-button" @click="saveUinfo(step)">保存</el-button>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-aside width="200px">
          <div style="min-height: 600px">
            <div class="title-button" :class="{ btnActive: step == '1' }" @click="active('1', 'substantiveReq', '实质性要求')">

              实质性要求
            </div>
            <div class="title-button" :class="{ btnActive: step == '2' }" @click="active('2', 'quantityParamReq', '可量化参数要求')">
              可量化参数要求
            </div>
            <div class="title-button" :class="{ btnActive: step == '3' }" @click="active('3', 'paramReq', '商务需求')">
              商务需求
            </div>
          </div>
        </el-aside>
        <el-main>
          <substantiveReq v-if="step == '1'" ref="substantiveReq" @saveSuccess="
              (uInfo) =>
                updateInfoMap('实质性要求', 'substantiveReq', '1', uInfo)
                "></substantiveReq>
          <quantityParamReq v-if="step == '2'" ref="quantityParamReq" @saveSuccess="
              (uInfo) =>
                updateInfoMap('可量化参数要求', 'quantityParamReq', '2', uInfo)
            "></quantityParamReq>
          <paramReq v-if="step == '3'" ref="paramReq" @saveSuccess="
              (uInfo) => updateInfoMap('参数要求', 'paramReq', '3', uInfo)
            "></paramReq>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import substantiveReq from "./requirements/substantiveReq.vue";
import quantityParamReq from "./requirements/quantityParamReq.vue";
import paramReq from "./requirements/paramReq.vue";
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
import { listInfo } from "@/api/method/info";
import { listItem } from "@/api/method/item";

export default {
  components: { substantiveReq, quantityParamReq, paramReq },
  data() {
    return {
      uinfo: {},
      projectInfo: {},
      options: [
        {
          value: "选项1",
          label: "选项1",
        },
      ],
      scoringInfoItems: [],
      scoringItemMap: {},
      value: "",
      current: {
        step: "1",
        refName: "substantiveReq",
        name: "实质性要求",
      },
      step: "1",

      itemInfo: {},
    };
  },
  methods: {
    updateInfoMap(name, refName, stepNumber, scoringUinfo) {
      this.$emit("saveSuccess", scoringUinfo);
    },
    active(stepNumber, refName, name) {
      console.log(
        "socring info active",
        this.projectInfo,
        this.itemInfo.itemContent,
        this.uinfo
      );
      this.step = stepNumber;
      this.current = {
        step: stepNumber,
        refName: refName,
        name: name,
      };
      if (this.itemInfo.itemContent == null) {
        let itemContent = {
          substantiveReq: {
            requirement: "",
          },
          quantityParamReq: {
            pdfUrl: "",
            wordUrl: "",
          },
          paramReq: {
            requirement: "",
          },
        };
        this.itemInfo.itemContent = JSON.stringify(itemContent);
      }
      // 加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(this.projectInfo, this.itemInfo, this.uinfo);
      });
    },
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      console.log("scoringMethod init start", projectInfo, itemInfo, uinfo);
      this.projectInfo = projectInfo;
      this.itemInfo = itemInfo;
      this.uinfo = uinfo;
      //加载子页面
      if (this.step == null) {
        this.$nextTick(() => {
          this.active("1", "substantiveReq", "实质性要求");
        });
      } else {
        this.$nextTick(() => {
          this.active(
            this.current.step,
            this.current.refName,
            this.current.name
          );
        });
      }

      console.log("scoringMethod init end", this.itemInfo);
    },
    saveUinfo(step) {
      switch (step) {
        case "1":
          this.$refs.substantiveReq.saveUinfo();
          break;
        case "2":
          this.$refs.quantityParamReq.saveUinfo();
          break;
        case "3":
          this.$refs.paramReq.saveUinfo();
          break;
      }
      return;
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: rgba(152, 200, 253, 1) !important; /* 激活状态下的字体颜色 */
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;
  .title-button {
    font-size: 16px;
    color: #333;
    margin: 25px 0;
    cursor: pointer;
    s &:hover {
      color: rgba(152, 200, 253, 1);
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  //   padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
</style>
