import request from "@/utils/request";

// 查询二次报价列表
export function listQuote(query) {
  return request({
    url: "/again/quote/list",
    method: "get",
    params: query,
  });
}

// 查询二次报价详细
export function getQuote(againQuoteId) {
  return request({
    url: "/again/quote/" + againQuoteId,
    method: "get",
  });
}

// 查询二次报价详细
export function getSecondPriceInfoVos(projectEvaluationId) {
  return request({
    url: "/again/quote/getSecondPriceInfoVos/" + projectEvaluationId,
    method: "get",
  });
}

// 新增二次报价
export function addQuote(data) {
  return request({
    url: "/again/quote",
    method: "post",
    data: data,
  });
}

// 修改二次报价
export function updateQuote(data) {
  return request({
    url: "/again/quote",
    method: "put",
    data: data,
  });
}

// 删除二次报价
export function delQuote(againQuoteId) {
  return request({
    url: "/again/quote/" + againQuoteId,
    method: "delete",
  });
}
