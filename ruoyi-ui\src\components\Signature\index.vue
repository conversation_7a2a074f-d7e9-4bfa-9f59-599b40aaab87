<template>
  <el-dialog
    title="创建手写签名"
    :visible.sync="modalOpen"
    width="800px"
  >
    <div style="width: 720px;padding-left: 40px;">
      <el-form
        :model="signatureForm"
        ref="signatureFormRef"
      >
        <el-form-item
          label="姓名"
          :rules="[{ required: true, message: '请输入姓名' }]"
        >
          <el-input
            v-model="signatureForm.userName"
            size="middle"
            placeholder="请输入姓名/生成用于签署的测试数字证书"
            class="input-width"
          />
        </el-form-item>
        <el-form-item label="">
          <div style="padding-bottom: 20px;">
            <div
              class="signature-box"
              :class="!startSignature?'signature-bg':''"
            >
              <VueSignaturePad
                ref="signaturePad"
                :images="[{ src: 'A.png', x: 0, y: 0 }, { src: 'B.png', x: 0, y: 10 }, { src: 'C.png', x: 0, y: 20 }]"
                :options="options"
              />
            </div>
          </div>
        </el-form-item>
      </el-form>
      <el-divider></el-divider>
      <div style="text-align: center;">
        <div style="text-align: center;">
          <el-button
            type="text"
            @click="undoSignature"
          >撤销</el-button>
          <el-button
            type="text"
            @click="clearSignature"
          >重写</el-button>
          <el-button @click="modalOpen = false">取消</el-button>
          <el-button
            type="primary"
            @click="handleOk"
          >使用</el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { VueSignaturePad } from "vue-signature-pad";
import signatureBg from "@/assets/images/signature-bg.png";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: { VueSignaturePad },
  props: {
    signatureModalShow: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    modalOpen: {
      get: function () {
        return this.signatureModalShow;
      },
      set: function (newValue) {
        this.$emit("update:signatureModalShow", newValue); // 触发更新事件，父组件的showDialog会自动更新
      },
    },
  },

  data() {
    //这里存放数据
    return {
      signatureForm: {
        userName: "",
      },
      startSignature: false,
      //签名面板配置
      options: {
        penColor: "#000",
        dotSize: (1 + 4) / 2,
        minWidth: 1,
        maxWidth: 4,
        throttle: 16,
        minDistance: 5,
        backgroundColor: "rgba(0,0,0,0)",
        velocityFilterWeight: 0.5,
        onBegin: () => {
          this.startSignature = true;
        },
        onEnd: () => {},
      },
    };
  },
  //监听属性 类似于data概念
  // computed: {},

  //方法集合
  methods: {
    /**
     * 提交手写签名
     */
    async handleOk() {
      try {
        //验证姓名是否输入   便于签名证书的生成
        // const values = await this.$refs.signatureFormRef.validateFields();
        //获取签名图片
        const { isEmpty, data } = this.$refs.signaturePad.saveSignature();
        if (isEmpty) {
          this.$message.warning("签名为空，请书写后再进行添加");
          return;
        }
        const resultImg = data.split(",");
        //将签名图片返给main页面
        const result = {
          image: resultImg[1],
          userName: this.signatureForm.userName,
        };
        this.$emit("success", result);
        this.modalOpen = false;
      } catch (errorInfo) {
        console.log("Failed:", errorInfo);
        this.$message.warning("姓名不能为空，请输入姓名");
      }
    },
    /**
     * 撤销
     */
    undoSignature() {
      this.$refs.signaturePad.undoSignature();
    },

    /**
     * 重写
     */
    clearSignature() {
      this.$refs.signaturePad.clearSignature();
      this.startSignature = false;
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {},
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.signature-box {
  width: 100%;
  height: 360px;
  border: 1px solid #dedede;
}
.signature-bg {
  background: url("~@/assets/images/signature-bg.png") no-repeat;
  background-size: 70% 70%;
  background-position: 50% 50%;
}
</style>