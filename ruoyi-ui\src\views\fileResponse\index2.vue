<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item
        label="项目名称"
        prop="projectName"
      >
        <el-input
          v-model="queryParams.projectName"
          placeholder="请输入项目名称"
          clearable
        />
      </el-form-item>
      <el-form-item label="开标时间">
        <el-date-picker
          v-model="queryParams.queryTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
        >搜索</el-button>
        <el-button
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-form-item>
    </el-form>

    <el-table
      v-loading="loading"
      :data="projectList"
    >
      <el-table-column
        label="状态"
        align="center"
        width="180"
      >
        <template slot-scope="scope">
          <span
            class="name"
            v-if="scope.row.id==null"
          >未制作</span>
          <span
            class="name"
            v-if="scope.row.id!=null && scope.row.filePath==null"
          >制作中</span>
          <span
            class="name"
            v-if="scope.row.id!=null && scope.row.filePath!=null"
          >已生成</span>
        </template>
      </el-table-column>
      <el-table-column
        label="项目名称"
        align="center"
        prop="projectName"
      />
      <el-table-column
        label="项目编号"
        align="center"
        prop="projectCode"
      />
      <el-table-column
        label="开标时间"
        align="center"
        prop="bidOpeningTime"
      />
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="addDocResponseEntInfo(scope.row)"
            v-if="scope.row.id==null"
          >制作响应文件</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="addDocResponseEntInfo(scope.row)"
            v-if="scope.row.id!=null && scope.row.filePath==null"
          >编辑响应文件</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="addDocResponseEntInfo(scope.row)"
            v-if="scope.row.id!=null && scope.row.filePath!=null"
          >重新生成响应文件</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getProjectList"
    />

    <!-- 添加或修改开标记录对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      title="确认项目信息"
      :visible.sync="open"
      width="60%"
      append-to-body
    >
      <el-form
        :model="form"
        label-width="80px"
      >
        <el-form-item
          label="项目名称"
          prop="projectName"
        >
          {{projectInfo.projectName}}
        </el-form-item>
        <el-form-item
          label="响应文件递交截止时间和开标时间"
          prop="bidOpeningTime"
          label-width="230px"
        >
          {{projectInfo.bidOpeningTime}}
        </el-form-item>
        <el-form-item
          label="开标地点"
          prop="bidOpeningPlace"
        >
          {{projectInfo.bidOpeningPlace}}
        </el-form-item>
        <el-form-item
          label="二级密码"
          prop="secretKey"
        >
          <el-input
            v-model="secretKey"
            placeholder="请输入二级密码"
            clearable
            show-password
          />
        </el-form-item>
      </el-form>
      <div
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="second"
        >确定制作</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getProject,
  listProject,
  supplierViewProdect,
} from "@/api/tender/project";
import { entList, saveInfo, entInfo } from "@/api/docResponse/entInfo";
import { secondPassword } from "@/api/ent/info";

export default {
  name: "responseIndex",
  dicts: ["busi_tender_mode"],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 开标记录表格数据
      openingList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,

      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        projectName: null,
        compereId: null,
        compereName: null,
        compereCode: null,
        bidOpeningMode: null,
        decodeStartTime: null,
        bidAnnounceTime: null,
        bidOpeningTime: null,
        bidOpeningEndTime: null,
        queryTime: null,
        startTime: null,
        endTime: null
      },
      // 表单参数
      form: {},
      projectId: "",
      fileId: "",
      secretKey: "",
      selectProject: {},
      projectInfo: {
        // projectName: "测试项目111",
        // projectCode: "3784hjids",
        // deadLine: "2024-07-20",
        // procurementWey: "竞争性磋商",
        // purchasName: "测试采购人",
      },
      projectList: [{}],
    };
  },
  computed: {},
  watch: {},
  mounted() {},
  created() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
    },
    getProjectList() {
      // console.log("params11",this.queryParams.queryTime);
      if (this.queryParams.queryTime == null) {
        this.queryParams.startTime = null;
        this.queryParams.endTime = null;
      } else {
        this.queryParams.startTime = this.queryParams.queryTime[0];
        this.queryParams.endTime = this.queryParams.queryTime[1];
      }
      let params = this.queryParams;
      entList(params)
        .then((result) => {
          this.projectList = result.rows;
          this.total = result.total;
          // if (result.code == 200) {
          //   this.projectList = result.data;
          // } else {
          //   this.$message.error(result.msg);
          // }
          this.loading = false;
        })
        .catch((err) => {});
    },
    // confirm() {
    //   if (!this.projectId) {
    //     this.$message({
    //       message: "请先选择一个项目",
    //       type: "warning",
    //     });
    //     return;
    //   }
    //   saveInfo({ projectId: this.projectId }).then((res) => {
    //     if (res.code == 200) {
    //       this.editDocResponseEntInfo(res.data);
    //     }
    //   });
    // },
    cancle() {
      this.$router.go(-1);
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.queryParams.queryTime = null;
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getProjectList(this.queryParams);
    },
    addDocResponseEntInfo(row) {
      console.log(row);
      // 获取项目信息
      entInfo(row.projectId).then((response) => {
        console.log("data",response.data);

        this.projectInfo = {
          projectName: response.data.projectName,
          projectId:row.projectId,
          bidOpeningPlace: response.data.bidOpeningPlace,
          bidOpeningTime: response.data.bidOpeningTime,
          projectType: response.data.projectType,
          tenderMode: response.data.tenderMode,
        };
        this.open = true;
      });
    },
    editDocResponseEntInfo(id) {
      if(this.projectInfo.tenderMode==1){ // 竞争性磋商
        if(this.projectInfo.projectType==0){
          this.$router.push({
            path: "/fileResponse/edit/" + id,
            query: {
              id: id,
              projectName: this.projectInfo.projectName,
              projectCode: this.projectInfo.projectId,
            },
          });
        }else if(this.projectInfo.projectType==1){
          this.$router.push({
            path: "/fileResponse/editService/" + id,
            query: {
              id: id,
              projectName: this.projectInfo.projectName,
              projectCode: this.projectInfo.projectId,
            },
          });
        }else if(this.projectInfo.projectType==2){
          this.$router.push({
            path: "/fileResponse/editgoods/" + id,
            query: {
              id: id,
              projectName: this.projectInfo.projectName,
              projectCode: this.projectInfo.projectId,
            },
          });
        }
      }
      if(this.projectInfo.tenderMode==3){ // 询价
        this.$router.push({
          path: "/fileResponse/inquiryPrice/" + id,
          query: {
            id: id,
            projectName: this.projectInfo.projectName,
            projectCode: this.projectInfo.projectId,
          },
        });
      }
    },
    /** 提交按钮 */
    saveInfo() {
      if (
        this.fileId == "" ||
        this.fileId == undefined ||
        this.fileId == null
      ) {
        saveInfo(this.projectInfo).then((result) => {
          if (result.code == 200) {
            this.editDocResponseEntInfo(result.data);
          }
        });
      } else {
        this.editDocResponseEntInfo(this.fileId);
      }
    },
    /** 提交按钮 */
    second() {
      secondPassword({ secretKey: this.secretKey }).then((result) => {
        if (result.code == 200) {
          this.saveInfo();
        } else {
          this.$message({
            message: "二级密码验证失败！",
            type: "error",
          });
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
  },
};
</script>
