import request from "@/utils/request";

// 查询门户展示公告列表
export function portalNoticeList(query) {
  return request({
    url: `/portal/list`,
    method: "get",
    params: query,
  });
}
// 查询门户展示未开标公告列表
export function notOpeninglist(query) {
  return request({
    url: "/portal/notOpeninglist",
    method: "post",
    data: query,
  });
}

// 查询门户展示公告详情
export function portalNoticeInfo(query) {
  return request({
    url: "/portal/info",
    method: "post",
    data: query,
  });
}

// 查询门户展示公告流程
export function portalNoticeProcess(query) {
  return request({
    url: "/portal/process",
    method: "post",
    data: query,
  });
}
