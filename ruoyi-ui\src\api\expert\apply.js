import request from '@/utils/request'

// 查询专家抽取申请列表
export function listApply(query) {
  return request({
    url: '/expert/apply/list',
    method: 'get',
    params: query
  })
}

// 查询专家抽取申请详细
export function getApply(applyId) {
  return request({
    url: '/expert/apply/' + applyId,
    method: 'get'
  })
}

// 新增专家抽取申请
export function addApply(data) {
  return request({
    url: '/expert/apply',
    method: 'post',
    data: data
  })
}

// 修改专家抽取申请
export function updateApply(data) {
  return request({
    url: '/expert/apply',
    method: 'put',
    data: data
  })
}

// 删除专家抽取申请
export function delApply(applyId) {
  return request({
    url: '/expert/apply/' + applyId,
    method: 'delete'
  })
}
