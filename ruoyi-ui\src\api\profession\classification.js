import request from '@/utils/request'

// 查询专家专业分类列表
export function listClassification(query) {
  return request({
    url: '/profession/classification/list',
    method: 'get',
    params: query
  })
}

// 查询专家专业分类详细
export function getClassification(classificationCode) {
  return request({
    url: '/profession/classification/' + classificationCode,
    method: 'get'
  })
}

// 新增专家专业分类
export function addClassification(data) {
  return request({
    url: '/profession/classification',
    method: 'post',
    data: data
  })
}

// 修改专家专业分类
export function updateClassification(data) {
  return request({
    url: '/profession/classification',
    method: 'put',
    data: data
  })
}

// 删除专家专业分类
export function delClassification(classificationCode) {
  return request({
    url: '/profession/classification/' + classificationCode,
    method: 'delete'
  })
}
