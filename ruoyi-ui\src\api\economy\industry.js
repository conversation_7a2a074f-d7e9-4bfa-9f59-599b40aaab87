import request from '@/utils/request'

// 查询国民经济行业分类列表
export function listIndustry(query) {
  return request({
    url: '/economy/industry/list',
    method: 'get',
    params: query
  })
}

// 查询国民经济行业分类详细
export function getIndustry(industrCode) {
  return request({
    url: '/economy/industry/' + industrCode,
    method: 'get'
  })
}

// 新增国民经济行业分类
export function addIndustry(data) {
  return request({
    url: '/economy/industry',
    method: 'post',
    data: data
  })
}

// 修改国民经济行业分类
export function updateIndustry(data) {
  return request({
    url: '/economy/industry',
    method: 'put',
    data: data
  })
}

// 删除国民经济行业分类
export function delIndustry(industrCode) {
  return request({
    url: '/economy/industry/' + industrCode,
    method: 'delete'
  })
}
