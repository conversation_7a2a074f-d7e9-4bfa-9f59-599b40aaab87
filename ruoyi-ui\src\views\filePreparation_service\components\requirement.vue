<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <el-button
              class="item-button"
              @click="saveUinfo(step)"
            >保存</el-button>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-aside width="200px">
          <div style="min-height: 600px">
            <div
              class="title-button"
              :class="{ btnActive: step == '1' }"
              @click="active('1', 'projectOverview', '项目概述')"
            >
              项目概述
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '2' }"
              @click="active('2', 'standardsAndReq', '服务标准及要求')"
            >
              服务标准及要求
            </div>

          </div>
        </el-aside>
        <el-main>
          <projectOverview
            v-if="step == '1'"
            ref="projectOverview"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('项目概述', 'projectOverview', '1', uInfo)
            "
          ></projectOverview>
          <standardsAndReq
            v-if="step == '2'"
            ref="standardsAndReq"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('服务标准及要求', 'standardsAndReq', '2', uInfo)
            "
          ></standardsAndReq>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import projectOverview from "./requirements/projectOverview.vue";
import standardsAndReq from "./requirements/standardsAndReq.vue";
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
import { listInfo } from "@/api/method/info";
import { listItem } from "@/api/method/item";

export default {
  components: { projectOverview, standardsAndReq },
  data() {
    return {
      uinfo: {},
      projectInfo: {},
      options: [
        {
          value: "选项1",
          label: "选项1",
        },
      ],
      scoringInfoItems: [],
      scoringItemMap: {},
      scoringUinfo: {},
      value: "",
      current: {
        step: "1",
        refName: "projectOverview",
        name: "项目概述",
      },
      step: "1",
      itemInfo: {},
    };
  },
  methods: {
    updateInfoMap(name, refName, stepNumber, scoringUinfo) {
      this.$emit("saveSuccess", scoringUinfo);
    },
    active(stepNumber, refName, name) {
      console.log(
        "socring info active",
        this.projectInfo,
        this.itemInfo.itemContent,
        this.uinfo
      );
      this.step = stepNumber;
      this.current = {
        step: stepNumber,
        refName: refName,
        name: name,
      };
      if (this.itemInfo.itemContent == null) {
        let itemContent = {
          projectOverview: {
            requirement: "",
          },
          standardsAndReq: {
            requirement: "",
          },
        };
        this.itemInfo.itemContent = JSON.stringify(itemContent);
      }
      // 加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(this.projectInfo, this.itemInfo, this.uinfo);
      });
    },
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      console.log("scoringMethod init start", projectInfo, itemInfo, uinfo);
      this.projectInfo = projectInfo;
      this.itemInfo = itemInfo;
      this.uinfo = uinfo;
      //加载子页面
      if (this.step == null) {
        this.$nextTick(() => {
          this.active("1", "projectOverview", "项目概述");
        });
      } else {
        this.$nextTick(() => {
          this.active(
            this.current.step,
            this.current.refName,
            this.current.name
          );
        });
      }

      console.log("scoringMethod init end", this.itemInfo);
    },
    saveUinfo(step) {
      switch (step) {
        case "1":
          this.$refs.projectOverview.saveUinfo();
          break;
        case "2":
          this.$refs.standardsAndReq.saveUinfo();
          break;
      }
      return;
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: rgba(152, 200, 253, 1) !important; /* 激活状态下的字体颜色 */
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;
  .title-button {
    font-size: 16px;
    color: #333;
    margin: 25px 0;
    cursor: pointer;
    s &:hover {
      color: rgba(152, 200, 253, 1);
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  //   padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
</style>
