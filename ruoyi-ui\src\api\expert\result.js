import request from "@/utils/request";

// 查询专家抽取结果列表
export function listResult(query) {
  return request({
    url: "/expert/result/list",
    method: "get",
    params: query,
  });
}

// 查询专家抽取结果详细
export function getResult(resultId) {
  return request({
    url: "/expert/result/" + resultId,
    method: "get",
  });
}

// 新增专家抽取结果
export function addResult(data) {
  return request({
    url: "/expert/result",
    method: "post",
    data: data,
  });
}

// 修改专家抽取结果
export function updateResult(data) {
  return request({
    url: "/expert/result",
    method: "put",
    data: data,
  });
}

// 删除专家抽取结果
export function delResult(resultId) {
  return request({
    url: "/expert/result/" + resultId,
    method: "delete",
  });
}

// 重新抽取专家
export function redrawExpert(data) {
  return request({
    url: "/expert/apply/resetExtract",
    method: "post",
    data: data,
  });
}

// 保存专家选择专家组长记录
export function keepRecord(data) {
  return request({
    url: "/expert/leaderVoteRecord",
    method: "post",
    data: data,
  });
}
// 查询专家组长
export function getExpertLeader(query) {
  return request({
    url: "/expert/leaderVoteRecord/getProjectLeaderVoteRecord",
    method: "get",
    params: query,
  });
}
