<template>
  <div class="discipline">
    <div class="titel">
      项目名称：{{ project.projectName }}
    </div>
    <div style="text-align: center;font-size:20px;color:#176ADB;font-weight: 600;margin-bottom: 20px;">温馨提示：请评审专家确认是否存在需要依法回避的情况</div>
    <div class="tips">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
        :header-cell-style="headStyle"
        :cell-style="cellStyle"
      >
        <el-table-column
          label="序号"
          width="180"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="bidderName"
          label="投标单位"
        >
        </el-table-column>
        <el-table-column
          prop="bidContactPerson"
          label="负责人"
          width="180"
        >
        </el-table-column>
      </el-table>
    </div>
    <div style="text-align:center">
      <el-button
        class="item-button"
        style="background-color:#F5F5F5;color:#176ADB"
        @click="avoid"
      >需要回避</el-button>
      <el-button
        class="item-button"
        @click="nextStep"
      >下一步</el-button>
    </div>
  </div>
</template>

<script>
import {
  editExpertInfo,
  supplierInfo,
  expertInfoById,
} from "@/api/expert/review";
import { updateResult } from "@/api/expert/result";
import { updateNode } from "@/api/evaluation/expertNodeInfo";
export default {
  name: "comfirm",
  props: { project: {} },
  data() {
    return {
      tableData: [
        {
          bidderName: "投标单位1",
          bidContactPerson: "张三",
        },
        {
          bidderName: "投标单位2",
          bidContactPerson: "李四",
        },
        {
          bidderName: "投标单位3",
          bidContactPerson: "王五",
        },
        {
          bidderName: "投标单位4",
          bidContactPerson: "赵六",
        },
      ],
      headStyle: {
        "text-align": "center",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
      },
      cellStyle: {
        "text-align": "center",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      supplierInfo({ projectId: this.$route.query.projectId }).then(
        (response) => {
          if (response.code == 200) {
            this.tableData = response.rows;
          } else {
            this.$message.warning(response.msg);
          }
        }
      );
    },
    nextStep() {
      updateNode({
        evalExpertEvaluationInfoId: localStorage.getItem(
          "evalExpertEvaluationInfoId"
        ),
        evalNode: 4,
      }).then((result) => {
        if (result.code == 200) {
          this.$emit("send", "leader");
        }
      });
    },
    avoid() {
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          updateResult({
            resultId: response.data[0].resultId,
            isAvoid: 1,
          }).then((response) => {
            if (response.code == 200) {
              this.$router.push({
                path: "/expertLogin",
              });
            } else {
              this.$message.warning(response.msg);
            }
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.discipline {
  padding: 20px 30px;
}
.titel {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  padding: 20px 0;
}
.tips {
  margin-left: 50px;
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: left;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
</style>