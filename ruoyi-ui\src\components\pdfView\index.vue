<!-- 文件查看器 -->
<template>
  <div>
    <div id="pdf-container" style="width:100%;overflow: auto;height: 600px;">
      <div style="position:sticky;top:0;z-inedex:99">
        <el-button size="mini" @click="skipPage(curPage - 1)">上一页</el-button>
        <el-button size="mini" @click="skipPage(1)">首页</el-button>
        <el-button size="mini" @click="skipPage(curPage+ 1)">下一页</el-button>
        <el-button size="mini" @click="enlarge(curPage)">放大</el-button>
      </div>
      <template v-if=" filePage.length>0">
        <div v-for="(page, index) in filePage" :key="index">
          <canvas :id="`pdf-${uni_key}-${index}` " style="width: 100%;"></canvas>
        </div>
      </template>
    </div>

    <!-- canvas放大展示 -->
    <div v-show="display_enlarge" class="canvas-enlarge">
      <!-- 关闭按钮 -->
      <div style="position: absolute; top: 0; right: 0;">
        <el-button type="danger" icon="el-icon-close" circle @click="close()">
        </el-button>
      </div>
      <!-- 渲染放大后的画布 -->
      <div id="enlarge" style="width:70%;height:56.25rem;overflow: auto;">
        <template v-if=" filePage.length>0">
          <div v-for="(page, index) in filePage" :key="index">
            <canvas :id="`pdfEnlarge-${uni_key}-${index}` " style="width: 100%;"></canvas>
          </div>
        </template>
      </div>
    </div>
  </div>

</template>

<script>
import pdfjsLib from "pdfjs-dist";
const baseURL = process.env.VUE_APP_BASE_API;
export default {
  props: {
    pdfurl: {
      type: String,
      default: "",
    },
    uni_key: {
      type: String,
      default: "a",
    },
  },
  data() {
    return {
      url: "",
      filePage: [],
      curPage: 1,
      pageHeight: 0,
      enlargeHeight: 0,
      scrollPosition: 0,
      display_enlarge: false,
    };
  },

  computed: {},

  watch: {
    scrollPosition: {
      handler(newVal) {
        if (this.pageHeight > 0) {
          this.curPage = Math.floor(newVal / this.pageHeight) + 1;
        }
      },
      immediate: true,
    },
    pdfurl: {
      handler(newVal) {
        if (newVal != null || newVal != undefined) {
          this.url = newVal;
          if (this.url != "") {
            this.url = baseURL + this.url;
            this.loadPDF();
          }
        }
      },
      immediate: true,
    },
  },

  methods: {
    //   初始化pdf
    async loadPDF() {
      try {
        pdfjsLib.GlobalWorkerOptions.workerSrc = require("pdfjs-dist/build/pdf.worker.min.js");
        const loadingTask = pdfjsLib.getDocument(this.url);

        const pdf = await loadingTask.promise;

        this.filePage = Array.from({ length: pdf.numPages }, (_, i) => ({
          docPage: i,
        }));

        // 渲染每一页
        for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
          const page = await pdf.getPage(pageNum);
          const page_enlarge = await pdf.getPage(pageNum);
          var canvas = null;
          var enlarge = null;

          canvas = document.getElementById(
            `pdf-${this.uni_key}-${pageNum - 1}`
          );
          enlarge = document.getElementById(
            `pdfEnlarge-${this.uni_key}-${pageNum - 1}`
          );
          const context = canvas.getContext("2d");
          const context_enlarge = enlarge.getContext("2d");

          const viewport = page.getViewport({ scale: 1.4 });
          const viewport_enlarge = page_enlarge.getViewport({ scale: 4 });

          canvas.height = viewport.height;
          canvas.width = viewport.width;
          enlarge.height = viewport_enlarge.height;
          enlarge.width = viewport_enlarge.width;
          this.pageHeight = canvas.height / 1.4;
          this.enlargeHeight = enlarge.height / 1.75;

          const renderContext = {
            canvasContext: context,
            viewport: viewport,
          };
          const renderContext_enlarge = {
            canvasContext: context_enlarge,
            viewport: viewport_enlarge,
          };
          page.render(renderContext).promise;
          page_enlarge.render(renderContext_enlarge).promise;

        }
      } catch (error) {
        console.error("Error loading PDF:", error);
      }
    },
    // 跳转页面
    skipPage(num) {
      if (num < 1) {
        num = 1;
      }
      if (num > this.filePage.length) {
        num = this.filePage.length;
      }
      this.curPage = num;
      const canvasId = `pdf-${this.uni_key}-${num - 1}`;
      if (canvasId == null) {
        return;
      }
      const canvasElement = document.getElementById(canvasId);

      if (canvasElement) {
        canvasElement.scrollIntoView({
          behavior: "smooth",
          block: "start",
          inline: "nearest",
        });
      } else {
        console.error(`Canvas element with id ${canvasId} not found.`);
      }
    },
    // 放大
    enlarge(num) {
      this.display_enlarge = true;

      // 隐藏整个页面的滚动条并禁止滚动
      document.body.style.overflow = "hidden";
      document.documentElement.style.overflow = "hidden";

      const canvasId = `pdfEnlarge-${this.uni_key}-${num - 1}`;
      if (canvasId == null) {
        return;
      }
      const canvasElement = document.getElementById(canvasId);

      // 使用requestAnimationFrame来优化滚动
      requestAnimationFrame(() => {
        if (canvasElement) {
          canvasElement.scrollIntoView({
            behavior: "smooth",
            block: "nearest",
            inline: "nearest",
          });
        } else {
          console.error(`Canvas element with id ${canvasId} not found.`);
        }
      });

    },
    // 关闭放大
    close() {
      this.display_enlarge = false;
      document.body.style.overflow = "auto"; // 隐藏滚动条并禁止滚动
      document.documentElement.style.overflow = "auto"; // 隐藏滚动条并禁止滚动
      const enlargeContainer = document.getElementById("enlarge");
      enlargeContainer.scrollTo(0, this.enlargeHeight);
    },

    handleScroll(event) {
      this.scrollPosition = event.target.scrollTop;
    },
  },
  created() { },
  mounted() {
    const container = this.$el.querySelector("#pdf-container");
    container.addEventListener("scroll", this.handleScroll);
  },

  destroyed() {
    const container = this.$el.querySelector("#pdf-container");
    container.removeEventListener("scroll", this.handleScroll);
  },
};
</script>
<style lang="scss" scoped>
.canvas-enlarge {
  z-index: 99;
  background-color: rgba(0, 0, 0, 0.5); /* 透明黑色背景 */
  position: absolute;
  width: 100%;
  height: 100vh;
  top: 0;
  left: 0;

  display: flex;
  justify-content: center;
  align-items: center;
}
</style>