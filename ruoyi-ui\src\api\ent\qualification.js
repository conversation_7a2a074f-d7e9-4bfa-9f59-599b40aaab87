import request from '@/utils/request'

// 查询企业资质列表
export function listQualification(query) {
  return request({
    url: '/ent/qualification/list',
    method: 'get',
    params: query
  })
}

// 查询企业资质详细
export function getQualification(qualificationId) {
  return request({
    url: '/ent/qualification/' + qualificationId,
    method: 'get'
  })
}

// 新增企业资质
export function addQualification(data) {
  return request({
    url: '/ent/qualification',
    method: 'post',
    data: data
  })
}

// 修改企业资质
export function updateQualification(data) {
  return request({
    url: '/ent/qualification',
    method: 'put',
    data: data
  })
}

// 删除企业资质
export function delQualification(qualificationId) {
  return request({
    url: '/ent/qualification/' + qualificationId,
    method: 'delete'
  })
}
