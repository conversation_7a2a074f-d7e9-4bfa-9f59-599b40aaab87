<template>
  <div class="three">
    <div style="position: relative;right: 60px;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0">技术标评审</div>
    <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
      <el-table-column prop="供应商名称" width="180">
      </el-table-column>
      <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.xm" :label="item.xm">
      </el-table-column>
    </el-table>
    <div class="operation" v-if="!finish">
      <el-button class="item-button" @click="completed">节点评审完成</el-button>
    </div>
    <div v-else class="operation">
      <el-button class="item-button" @click="back">返回</el-button>
    </div>
  </div>

</template>

<script>
import { leaderSummaryQuery } from "@/api/expert/review";
import { updateProcess } from "@/api/evaluation/process";
import { reEvaluationTwo } from "@/api/evaluation/expertStatus";

export default {
  props: {
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      votingResults: "",
      headStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
      };
      leaderSummaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          // this.tableData = this.pushAvg(this.tableData);
          this.columns = response.data.tableColumns;
          this.columns.push({"xm":"汇总"});
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    //增加平均值计算
    pushAvg(tableData){
      // 转换数据
      return tableData.map((row) => {
        return row;
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 resultId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.resultId] = column.xm;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        let sum = 0;
        let zjsl = 0;
        for (let key in row) {          
          if(key!='gys'){
            sum += row[key];
            zjsl++;
          }
        } 
        row["avg"] = sum/zjsl;
        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.resultId;
          transformedRow[column.xm] = row[itemId] || "0"; // 默认为 '0' 如果没有找到对应值
        });
        transformedRow["汇总"] = (sum/zjsl).toFixed(2);
        transformedRow["bidderId"] = supplierId;
        return transformedRow;
      });
    },
    reviewed() {
      // TODO  走什么接口
      this.$emit("send", "one");
    },
    // 节点评审完成
    completed() {
      const evaluationProcessId = JSON.parse(
        localStorage.getItem("evalProjectEvaluationProcess")
      );
      console.log("tableData", this.tableData);
      
      var er = [];
      for(let index of this.tableData){
        let i = {};
        i["gys"]=index["供应商名称"];
        i["bidder"]=index["bidderId"];
        i["result"]=index["汇总"];
        er.push(i);
      }
      let ers = JSON.stringify(er);
      const data = {
        evaluationProcessId: evaluationProcessId.evaluationProcessId,
        evaluationResult: ers,
        evaluationState: 2,
        evaluationResultRemark: this.votingResults,
      };
      updateProcess(data).then((response) => {
        if (response.code == 200) {
          this.$router.push({
            path: "/expertInfo",
            query: {
              projectId: this.$route.query.projectId,
              zjhm: this.$route.query.zjhm,
            },
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
    // 重新评审
    reviewed() {
      const query = {
        projectEvaluationId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).projectEvaluationId,
        expertResultId: JSON.parse(localStorage.getItem("evalExpertScoreInfo"))
          .expertResultId,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).scoringMethodItemId,
      };
      reEvaluationTwo(query).then((res) => {
        if (res.code == 200) {
          const evaluationProcessId = JSON.parse(
            localStorage.getItem("evalProjectEvaluationProcess")
          );

          reEvaluate(evaluationProcessId.evaluationProcessId).then(
            (response) => {
              if (response.code == 200) {
                this.$emit("send", "one");
              } else {
                this.$message.warning(resposne.msg);
              }
            }
          );
        }
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 20px 110px;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #fff;
  background-color: #176adb;
  border: 0;
  &:hover {
    color: #fff;
  }
}
.result {
  text-align: left;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>