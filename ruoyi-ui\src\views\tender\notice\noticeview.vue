<template>
  <div class="tender" v-loading="loading">
    <el-tabs v-model="activeName" @tab-click="handleClick">
      <el-tab-pane v-for="(item, index) in noticeList" :key="index" :label="item.changeNum>0?'第'+item.changeNum+'次变更':'采购公告'" :name="item.noticeId+''">
        <changeview :noticeId="item.noticeId" :showNum="showNum" v-if="item.noticeType==2 && activeName==item.noticeId"></changeview>
        <noticeview :noticeId="item.noticeId" :showNum="showNum" v-if="item.noticeType==1 && activeName==item.noticeId"></noticeview>
      </el-tab-pane>
    </el-tabs>
    <div slot="footer" class="option">
      <el-button v-if="showSignUp" type="success" @click="downloadTenderFiles">我要投标</el-button>
      <el-button v-if="showChange" type="success" @click="changeNotice">变更</el-button>
      <el-button @click="cancel">关闭</el-button>
    </div>
  </div>
</template>
<script>
import noticeview from "@/views/tender/notice/view.vue"
import changeview from "@/views/tender/notice/changeview.vue"
import { getNoticeTypes, downloadTenderFile, getTenderNoticeByProject } from "@/api/tender/notice";

export default {
  components: {
    changeview, noticeview
  },
  dicts: [],
  props: [],
  data() {
    return {
      activeName: "1",
      // 遮罩层
      loading: false,
      noticeList: [],
      projectId: '',
      handleType: '',
      noticeInfo: {},
      showNum: false,
      showChange: false,
      showSignUp: false,
    };
  },
  computed: {},
  watch: {},
  created() {
    this.projectId = this.$route.params.projectId;
    this.handleType = this.$route.query.handleType;
    if (this.handleType === 'supplierMain') {
      this.showNum = false;
      this.showChange = false;
      this.showSignUp = true;
    } else if (this.handleType === 'purchaserMain') {
      this.showNum = true;
      this.showChange = false;
      this.showSignUp = false;
    } else if (this.handleType === 'purchaserMenu') {
      this.showNum = true;
      this.showChange = true;
      this.showSignUp = false;
    }
    this.getNoticeTypes();
    this.getNoticeInfo();
  },
  mounted() {
  },
  methods: {
    cancel() {
      this.$tab.closePage();
    },
    handleClick(tab, event) {
      console.log(tab, event);
    },
    // 获取通知公告
    getNoticeTypes() {
      getNoticeTypes(this.projectId).then((result) => {
        if (result.code == 200) {
          this.noticeList = result.data;
          if (this.noticeList.length > 0) {
            this.activeName = this.noticeList[0].noticeId + '';
          }
          console.log(this.noticeList);
        }
      });
    },
    getNoticeInfo() {
      getTenderNoticeByProject(this.projectId).then((result) => {
        if (result.code == 200) {
          this.noticeInfo = result.data;
        }
      });
    },
    downloadTenderFiles() {
      if (new Date(this.noticeInfo.bidOpeningTime) <= new Date()) {
        this.$modal.msgError("已超过采购文件下载截至时间！");
      } else {
        downloadTenderFile(this.projectId).then((result) => {
          const blob = new Blob([result]);
          let downloads = document.createElement("a");
          let href = window.URL.createObjectURL(blob);
          downloads.href = href;
          let noticeVersion = "";
          downloads.download = '采购文件1.' + this.noticeInfo.changeNum + '.zip';
          document.body.appendChild(downloads);
          downloads.click();
          document.body.removeChild(downloads);
          window.URL.revokeObjectURL(href);
        });
      }
    },
    // 变更公告跳转
    changeNotice() {
      // 需要一个标识表明是新增
      this.$router.push({
        path: "/tender/notice/change/" + this.noticeInfo.noticeId+"/1",
      });
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: rgba(185, 248, 191, 1);

  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
