<template>
  <div style="margin-top:20px;">

    <el-form ref="elForm" :model="formData" :rules="rules" >
    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf"><div class="cell " >采购项目</div></td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell" >
                      <el-form-item label-width="0" prop="projectId">
                      <el-select v-model="formData.projectId" placeholder="请选择采购项目" filterable clearable
                          ref="selectProject" :style="{width: '100%'}" @change="changeProject" :disabled="initProject">
                        <el-option v-for="(item, index) in projectIdOptions" :key="index" :label="item.projectName"
                          :value="item.projectId" :disabled="item.disabled"></el-option>
                      </el-select>
                    </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell ">项目编号</div></td>
                  <td colspan="22" class="el-table__cell is-leaf"><div class="cell " >{{formData.projectCode}}</div></td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf" ><div class="cell ">开标时间</div></td>
                  <td colspan="10" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{formData.bidOpeningTime}}</div></td>

                  <td colspan="3" class="el-table__cell is-leaf" ><div class="cell ">实际开标结束时间</div></td>
                  <td colspan="9" class="el-table__cell is-leaf"><div class="cell " >
                  <el-form-item label-width="0" prop="bidOpeningEndTime">
                    <el-date-picker type="datetime" v-model="formData.bidOpeningEndTime" format="yyyy-MM-dd HH:mm:ss"
                      value-format="yyyy-MM-dd HH:mm:ss" :style="{width: '100%'}" placeholder="请选择开标结束时间" clearable>
                    </el-date-picker>
                  </el-form-item>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-form>



    <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>投标人信息</span>
          </div>
          <div >
      <el-table v-loading="loading" :data="tradderList" style="width: 100%;margin: auto;">
        <el-table-column label="投标人"  min-width="6" align="center" prop="bidderName" />
        <el-table-column label="投标报价"  min-width="2" align="center" prop="bidAmount" />
        <el-table-column label="报价单位"  min-width="1" align="center" prop="priceUnitLabel" />
      </el-table>
          </div>
        </el-card>
      </el-col>



      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr v-for="dict in dict.type.bid_opening_attachment" :key="dict.label" >
                  <td colspan="4" class="el-table__cell is-leaf"><div class="cell " >
                    <strong style="color:red;" v-if="dict.raw.isEquals==1">*</strong>{{ dict.label }}
                  </div></td>
                  <td colspan="20" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <template>
                      <FileUpload
                        :value="getImgPath(dict)"
                        @input="handleInput(dict, $event)"
                          :fileType="['pdf', 'doc', 'docx']"
                          :isShowTip="false"
                          :showOnly=false
                    ></FileUpload>
                    </template>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>


      <div slot="footer" class="dialog-footer" style="text-align: center;">
        <el-button type="primary" @click="submitForm">提交</el-button>
        </div>
  </div>
</template>
<script>
import { getOpening, delOpening, addOpening, updateOpening, openingProject,saveWithRecord } from "@/api/bid/opening";
import { getListRecord } from "@/api/bidding/record";
import { getDicts } from "@/api/system/dict/data";
export default {
  components: {},
  props: [],
  dicts: ["bid_opening_attachment"],
  data() {
    return {
      initProject:false,
      // 遮罩层
      loading: false,
      formData: {
        projectId: '',
        bidOpeningEndTime: '',
        attachmentList: [],
      },
      attachmentTypes: [],
      attachmentMap: {},
      attachmentsMap: {},
      rules: {
        projectId: [
          {required: true, message: '请选择采购项目',trigger: 'change'}
        ],
        bidOpeningEndTime: [
          {required: true,message: '请选择开标结束时间',trigger: 'change'}
        ],
      },
      projectIdOptions: [],
      projectMap:{},
      // 投标人表格数据
      tradderList: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bidEvaluationId: null,
        projectId: null,
        evaluationStartTime: null,
        bidEvaluationEndTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
    }
  },
  computed: {},
  watch: {},
  created() {
    var xxx = this.$route.params;
    this.getProjectList();
    // this.getAttachmentTypes();
  },
  mounted() {},
  methods: {
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    submitForm() {
      for(let item of this.dict.type.bid_opening_attachment){
        if(item.raw.isEquals==1 && this.attachmentsMap[item.value]==undefined){
          this.$message.error(item.label+' 文件不能为空')
          return;
        }
      }
      this.$refs["elForm"].validate(valid => {
        // this.formData.attachmentMap = this.attachmentMap;
          this.formData.attachments = [].concat(
            ...Object.values(this.attachmentsMap)
          );
        if (valid) {
          if (this.formData.bidOpeningId != null) {
            updateOpening(this.formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveWithRecord(this.formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.$tab.closePage();
            });
          }
        }
      });
    },
    handleInput(dict, data) {
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
    },
    getProjectList(){
      console.info("getProjectList")
      openingProject().then((result) => {
        if(result.code==200){
          this.projectIdOptions = result.data;
          console.log(this.projectIdOptions )
          for(let project of result.data){
            this.projectMap[project.projectId] = project;
          }

          if (this.$route.query.projectId) {
            this.formData.projectId = parseInt(this.$route.query.projectId);
            this.$refs.selectProject.$emit("change", this.formData.projectId);
            this.initProject = true;
          }
        }
      });
    },
    getAttachmentTypes(){
      getDicts("busi_bid_opening_attachment").then((result) => {
        if(result.code==200){
          this.attachmentTypes = result.data;
        }
      });
    },
    changeProject(r){
      let p = this.projectMap[r];
      console.log(p)
     this.formData.projectCode = p.projectCode;
      this.formData.projectName = p.projectName;
      this.formData.bidOpeningTime = p.bidOpeningTime;
      console.info(this.formData);
      this.loading = true;
      getListRecord({projectId:this.formData.projectId, params:{isScope:1}}).then( (result) => {
        if(result.code==200){
          this.tradderList = result.data;
        }
        this.loading = false;
      });
    }
  }
}

</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}
.cell-right-border {
  border-right:1px solid #dfe6ec
}
 .el-form-item {
    margin-bottom: 0px;
  }
</style>
