import request from '@/utils/request'

// 查询项目评审信息列表
export function listInfo(query) {
  return request({
    url: '/evalExpertEvaluationInfo/list',
    method: 'get',
    params: query
  })
}

// 查询项目评审信息详细
export function getInfo(data) {
  return request({
    url: '/evalExpertEvaluationInfo/getInfo',
    method: 'post',
    data: data
  })
}

// 查询项目评审信息详细
export function getInfo2(data) {
  return request({
    url: '/evalExpertEvaluationInfo/getInfo2',
    method: 'post',
    data: data
  })
}

// 新增项目评审信息
export function addInfo(data) {
  return request({
    url: '/evaluation/info',
    method: 'post',
    data: data
  })
}

// 修改项目评审信息
export function updateInfo(data) {
  return request({
    url: '/evaluation/info',
    method: 'put',
    data: data
  })
}

// 查询项目评审信息列表
export function selectByProject(projectId) {
  return request({
    url: '/evaluation/info/selectByProject?projectId='+projectId,
    method: 'get'
  })
}

// 删除项目评审信息
export function updateNode(data) {
  return request({
    url: '/evalExpertEvaluationInfo/updateNode',
    method: 'post',
    data: data
  })
}
