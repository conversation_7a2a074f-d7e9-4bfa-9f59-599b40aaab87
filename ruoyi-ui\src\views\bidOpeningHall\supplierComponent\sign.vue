<!-- 供应商签到 -->
<template>
  <div class="sign-in">
    <div class="sign-line-one">
      <div class="sign-info">
        <div><span style="font-weight: 500;">公司名称：</span> {{userInfo.nickName}}</div>
        <div><span style="font-weight: 500;">项目名称：</span>{{projectInfo.project.projectName}}</div>
        <div><span style="font-weight: 500;">开标时间：</span>{{ formatBidOpeningTime(projectInfo.bidOpeningTime) }}</div>
      </div>
      <div class="sign-countdown">
        <el-statistic
          format="开标倒计时：HH小时mm分ss秒"
          :value="deadline"
          time-indices
        >
        </el-statistic>
      </div>
    </div>
    <div class="sign-line-two">
      <el-form
        style="width:50%"
        ref="signIn"
        :model="signIn"
        :rules="rules"
        label-width="150px"
        label-position="left"
        :hide-required-asterisk="true"
      >
        <el-form-item
          label="输入开标联系人："
          prop="bidContactPerson"
        >
          <el-input
            class="sign-input"
            v-model="signIn.bidContactPerson"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="输入联系方式："
          prop="bidContactPersonTel"
        >
          <el-input
            class="sign-input"
            v-model="signIn.bidContactPersonTel"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <!-- 如果当前用户在投标人列表中且状态为未签到、时间在deadLine之前，则按钮可点击 -->
    <div class="sign-line-three">
      <el-button
        v-show="show"
        class="sign-button"
        @click="sign_In"
      >签到</el-button>
    </div>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';

import { formatDateOption } from "@/utils/index";
import { signIn, dataList } from "@/api/onlineBidOpening/info";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    projectInfo: {},
    userInfo: "",
  },
  data() {
    //这里存放数据
    return {
      deadline: new Date(this.projectInfo.bidOpeningTime),
      signIn: {
        projectId: "",
        bidContactPerson: "",
        bidContactPersonTel: "",
      },
      rules: {
        bidContactPerson: [
          { required: true, message: "请输入开标联系人", trigger: "blur" },
        ],
        bidContactPersonTel: [
          { required: true, message: "请选择联系方式", trigger: "change" },
          {
            pattern: /^1[3456789]\d{9}$/,
            message: "手机号码格式不正确",
            trigger: "blur",
          },
        ],
      },
      tableData: [],
      show: false,
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 格式化开标时间显示
    formatBidOpeningTime(time) {
      return formatDateOption(time, "date");
    },
    // 签到
    sign_In() {
      // 1.判断内容是否为空
      this.signIn.projectId = this.$route.query.projectId;
      this.$refs.signIn.validate((valid) => {
        if (valid) {
          signIn(this.signIn).then((response) => {
            if (response.code == 200) {
              this.$emit("sendMessage", "signIn");
            } else {
              this.$modal.msgwarning(response.msg);
            }
          });
        }
      });
    },
    initdataList() {
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.tableData = response.data;

          // 如果当前用户在投标人列表中且状态为未签到、时间在deadLine之前，则按钮可点击
          const foundBidder = this.tableData.find(
            (item) => item.bidderName === this.userInfo.ent.entName
          );
          const isSignInStatusZero = !(
            foundBidder && foundBidder.signInStatus === 1
          );
          const currentTime = new Date();
          const deadLineDate = new Date(this.projectInfo.bidOpeningTime);
          const isCurrentTimeBeforeDeadline = currentTime < deadLineDate;

          this.show = isSignInStatusZero && isCurrentTimeBeforeDeadline;
        }
      });
    },
    // 按钮是否显示
    buttonShow() {
      const currentTime = new Date();
      const deadLineDate = new Date(this.projectInfo.bidOpeningTime);
      const isCurrentTimeBeforeDeadline = currentTime < deadLineDate;
      if (this.$store.getters.supplierBidOpenStatus == null) {
        this.show = true;
      }
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.initdataList();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
.sign-in {
  padding: 20px 25px;
  .sign-line-one {
    display: flex;
    .sign-info {
      width: 50%;
      height: 105px;

      font-size: 18px;
      color: #333333;
      letter-spacing: 0;
      div {
        margin-bottom: 10px;
        font-weight: 700;
      }
    }
    .sign-countdown {
      width: 50%;
      height: 105px;

      background: #176adb;
      display: flex;
      align-items: center;
      justify-content: center;
      ::v-deep .number {
        color: #fff;
        font-weight: 700;
      }
    }
  }
  .sign-line-two {
    display: flex;
    justify-content: center;
    align-items: center;

    padding: 77px 0;
    ::v-deep .el-form-item__label {
      font-weight: 700;
      font-size: 17px;
      color: #333333;
    }
    .sign-input {
      border: 1px solid #979797;
      ::v-deep .el-input__inner {
        border-radius: 0;
      }
    }
  }
  .sign-line-three {
    display: flex;
    align-items: center;
    justify-content: center;
    .sign-button {
      width: 164px;
      height: 45px;
      background: #176adb;

      color: #fff;
      font-weight: 700;
    }
  }
}
</style>