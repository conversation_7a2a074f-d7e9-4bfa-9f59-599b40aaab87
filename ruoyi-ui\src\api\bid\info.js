import request from '@/utils/request'

// 查询询标信息列表
export function listInfo(query) {
  return request({
    url: '/bid/info/list',
    method: 'get',
    params: query
  })
}

// 查询询标信息详细
export function getInfo(inquiringBidId) {
  return request({
    url: '/bid/info/' + inquiringBidId,
    method: 'get'
  })
}

// 新增询标信息
export function addInfo(data) {
  return request({
    url: '/bid/info',
    method: 'post',
    data: data
  })
}

// 修改询标信息
export function updateInfo(data) {
  return request({
    url: '/bid/info',
    method: 'put',
    data: data
  })
}

// 删除询标信息
export function delInfo(inquiringBidId) {
  return request({
    url: '/bid/info/' + inquiringBidId,
    method: 'delete'
  })
}
