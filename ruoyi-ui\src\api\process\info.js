import request from '@/utils/request'

// 查询全流程信息归档信息列表
export function listInfo(query) {
  return request({
    url: '/process/info/list',
    method: 'get',
    params: query
  })
}

// 查询全流程信息归档信息详细
export function getInfo(processId) {
  return request({
    url: '/process/info/' + processId,
    method: 'get'
  })
}

// 查询全流程信息归档信息详细
export function getByProject(projectId) {
  return request({
    url: '/process/info/getByProject?projectId=' + projectId,
    method: 'get'
  })
}

// 新增全流程信息归档信息
export function addInfo(data) {
  return request({
    url: '/process/info',
    method: 'post',
    data: data
  })
}

// 修改全流程信息归档信息
export function updateInfo(data) {
  return request({
    url: '/process/info',
    method: 'put',
    data: data
  })
}

// 删除全流程信息归档信息
export function delInfo(processId) {
  return request({
    url: '/process/info/' + processId,
    method: 'delete'
  })
}

// 查询全流程信息归档信息详细
export function selectProcessAttachment(processId) {
  return request({
    url: '/process/info/selectProcessAttachment/' + processId,
    method: 'get'
  })
}

// 查询全流程信息归档信息详细
export function initAttachmentForProcess(query) {
  return request({
    url: '/process/info/initAttachmentForProcess',
    method: 'post',
    params: query
  })
}
getByProject