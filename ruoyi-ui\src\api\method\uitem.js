import request from '@/utils/request'

// 查询用户评分办法因素列表
export function listUitem(query) {
  return request({
    url: '/method/uitem/list',
    method: 'get',
    params: query
  })
}

// 查询用户评分办法因素详细
export function getUitem(entMethodItemId) {
  return request({
    url: '/method/uitem/' + entMethodItemId,
    method: 'get'
  })
}

// 新增用户评分办法因素
export function addUitem(data) {
  return request({
    url: '/method/uitem',
    method: 'post',
    data: data
  })
}

// 修改用户评分办法因素
export function updateUitem(data) {
  return request({
    url: '/method/uitem',
    method: 'put',
    data: data
  })
}

// 删除用户评分办法因素
export function delUitem(entMethodItemId) {
  return request({
    url: '/method/uitem/' + entMethodItemId,
    method: 'delete'
  })
}
