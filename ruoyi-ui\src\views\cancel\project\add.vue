<template>
  <div
    class="tender"
    v-loading="loading"
  >
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-tickets"></i>项目选择</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >采购项目</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <el-select
              v-model="form.projectId"
              placeholder="请选择项目"
              ref="selectProject"
              filterable
              clearable
              :style="{ width: '90%' }"
              :disabled="isFormDisabled"
            @change="change($event)"
            >
              <el-option
                v-for="(item, index) in projectIdOptions"
                :key="index"
                :label="item.projectName"
                :value="item.projectId"
              ></el-option>
            </el-select>
                  </div></td>
                </tr>
                
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >项目编号</div></td>
                  <td colspan="21" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    {{ form.projectCode }}
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
      </el-card>
    </el-col>

<el-col
  :span="24"
  class="card-box"
>
  <el-card>
    <div slot="header">
      <span><i class="el-icon-document"></i>取消原因</span>
    </div>
    <div>
        <editor
          v-model="form.cancelReason"
          :min-height="192"
        />
    </div>
  </el-card>
</el-col>

    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i> 附件</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr
                v-for="dict in dict.type.cancel_project_attachment"
                :key="dict.label"
              >
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <strong style="color:red;" v-if="dict.raw.isEquals==1">*</strong>{{ dict.label }}
                  </div>
                </td>
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    <template>
                      <FileUpload
                        :value="getImgPath(dict)"
                        @input="handleInput(dict, $event)"
                        :fileType="['pdf', 'doc', 'docx']"
                        :isShowTip="false"
                      ></FileUpload>
                    </template>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
      
    
    <el-col :span="24" class="card-box" >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-document"></i>审核流程</span>
        </div>
        <div >
      <el-table v-loading="loading" :data="form.auditProcessList" style="width: 100%;margin: auto;">
        <el-table-column label="操作人员"  min-width="6" align="center" prop="operatorName" />
        <el-table-column label="操作时间"  min-width="2" align="center" prop="auditTime" />
        <el-table-column label="操作"  min-width="1" align="center" prop="auditResultName" />
        <el-table-column label="操作内容"  min-width="1" align="center" prop="auditRemark" />
      </el-table>
        </div>
      </el-card>
    </el-col>
    </el-col>
    <div style="text-align: center">
      <el-button
        type="primary"
        @click="submit()" 
      >提交</el-button>
    </div>
  </div>
</template>
<script>
import { listProject, getProject, delProject, addProject, updateProject,listTenderProject } from "@/api/cancel/project";
import { formatDate } from "@/utils/index";
// import {getRecord} from "@/api/bidding/record";

export default {
  components: {},
  dicts: ["cancel_project_attachment"],
  props: [],
  data() {
    return {
      isFormDisabled:false,
      // 遮罩层
      loading: true,
      datetime:"",
      projectIdOptions: [],
      bidOption: [],
      form: {
        cancelId: null,
        projectId: null,
        cancelReason: null,
        cancelDate: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        auditProcessList:[]
      },
      fileList: "",
      attachmentsMap: {},
      bidderNotice: [],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getInfo()
    this.checkcancelId()
  },
  mounted() {
   // this.getInfo();
  },
  methods: {
    checkcancelId() {
      const cancelId = this.$route.params.cancelId;
      if(cancelId == 0||cancelId === undefined){
        this.$route.meta.title = "新增取消项目"
        //this.isFormDisabled=false;
        // this.getList();

      }else{
        this.$route.meta.title = "修改取消项目"
        this.loadIntentionDetail(cancelId);
        this.isFormDisabled=true;
       /* this.isFormDisabled=this.$route.params.isFormDisabled=="true"?true:false;
        if(this.isFormDisabled){
          this.$route.meta.title = "查看响应文件"
          this.loadIntentionDetail(biddingId);

        }else{
          this.$route.meta.title = "修改响应文件"
          this.loadIntentionDetail(biddingId);

        }*/

      }

    },
    loadIntentionDetail(cancelId) {
     /* listProject({params:{isScope:'1'}}).then((response) => {
        this.projectIdOptions = response.rows;
        console.log(this.projectIdOptions)
      });*/
      // 加载已有意图详情的逻辑
      getProject(cancelId).then(response => {
        this.form = response.data;
        if (typeof this.form.projectId === 'string') {
          this.form.projectId = parseInt(this.form.projectId, 10);
        }
        this.form.projectCode = this.form.project.projectCode;
        this.form.projectName = this.form.project.projectName;
        // 使用reduce方法将attachments数组收集成一个映射
        this.attachmentsMap = response.data.attachments.reduce(
          (accumulator, attachment) => {
            // 如果accumulator中还没有这个fileType的键，则创建一个新数组
            if (!accumulator[attachment.fileType]) {
              accumulator[attachment.fileType] = [];
            }
            // 将当前attachment添加到对应fileType的数组中
            accumulator[attachment.fileType].push(attachment);
            return accumulator;
          },
          {}
        );
      });
    },
    getInfo() {
      const promise1 = listTenderProject({}).then((response) => {
        this.projectIdOptions = response.rows;
      });
     /* // TODO 只查加上当前用户的项目
      const promise2 = listTenderProject({ delFlag: 0}).then((response) => {
        this.projectIdOptions = response.rows;
      });*/

      // 使用 Promise.all 等待两个接口请求都完成
      Promise.all([promise1])
        .then(() => {
          // 两个接口都请求完成后将 this.loading 设置为 false
          this.loading = false;
        })
        .catch((error) => {
          // 处理 Promise.all 中任何一个 Promise 请求失败的情况
          console.error(error);
        });
      this.loading = false;
    },
    change(value) {
      if (value) {
        this.bidderNotice = [];
        console.info("--------------change-----------------")
        console.log(this.projectIdOptions);
        const selectedItem = this.projectIdOptions.find(
          (item) => item.projectId === value
        );
        console.log(selectedItem);
        this.bidderNotice.push(selectedItem.bidderNotice);
        this.form.projectCode = selectedItem.projectCode;
        this.form.projectName = selectedItem.projectName;
      } else {
      }
    },
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    submit() {
      console.log(this.form);
      for(let item of this.dict.type.cancel_project_attachment){
        if(item.raw.isEquals==1 && this.attachmentsMap[item.value]==undefined){
          this.$message.error(item.label+' 文件不能为空')
          return;
        }
      }
      if (this.form.cancelReason.trim() === "") {
        this.$modal.msgError("请填写取消原因");
        return;
      }
      if (this.form.projectId !== "") {
        this.form.cancelDate =  formatDate(new Date());
        this.form.attachments = [].concat(
          ...Object.values(this.attachmentsMap)
        );
        addProject(this.form).then((response) => {
          this.$modal.msgSuccess("提交成功");
          this.$tab.closePage();
        });
      } else {
        this.$modal.msgWarning("请选择项目后保存");
      }
    },
    closeCard() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: #ffffff;
  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
