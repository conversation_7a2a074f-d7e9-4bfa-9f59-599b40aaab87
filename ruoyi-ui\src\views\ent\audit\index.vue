<template>
  <div
    v-loading="loading"
    style="margin-top: 20px;"
  >
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-suitcase"></i>企业信息</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    企业Logo
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="form.entLogo"
                      fit="contain"
                    ></el-image>
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    企业名称
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entName }}
                  </div>
                </td>

              </tr>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    统一社会信用代码
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    {{ form.entCode }}
                  </div>
                </td>

                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    经济性质
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    <dict-tag
                      :options="dict.type.ent_economic_nature"
                      :value="form.entNature"
                    />
                  </div>
                </td>
              </tr>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    企业地址
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entAddress }}
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    重点代理领域
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entKeyAgencyAreas }}
                  </div>
                </td>
              </tr>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    开户行
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entOpeningBank }}
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    开户行户号
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entBankCode }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-suitcase"></i>企业简介</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    单位网址
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entWebsite }}
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    企业类型
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <dict-tag
                      :options="dict.type.base_ent_type"
                      :value="form.entType"
                    />
                  </div>
                </td>
              </tr>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    营业执照
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    <el-image
                      style="width: 100px; height: 100px"
                      :src="form.businessLicense"
                      fit="contain"
                    ></el-image>
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    企业简介
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div
                    class="cell"
                    v-html="form.entIntro"
                  >
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>

    </el-col>
    <el-col
      :span="24"
      class="card-box"
    >
      <el-card>
        <div slot="header">
          <span><i class="el-icon-suitcase"></i>信息联系人</span>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table
            cellspacing="0"
            style="width: 100%;table-layout:fixed;"
          >
            <tbody>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    联系人
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entLinkman }}
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    联系方式
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    {{ form.entContactPhone }}
                  </div>
                </td>
              </tr>
              <tr>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    法人
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    {{ form.entLegalPerson }}
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    法人联系方式
                  </div>
                </td>
                <td
                  colspan="10"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    {{ form.entLegalPersonPhone }}
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <div class="dialog-footer">
      <el-button
        type="primary"
        @click="updateStatus(0)"
      >通 过</el-button>
      <el-button @click="updateStatus(1)">驳 回</el-button>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-input
        v-model="form.auditRemark"
        :placeholder="'请输入'+title"
      ></el-input>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="cancel">取 消</el-button>
        <el-button
          type="primary"
          @click="confirmed"
        >确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
      
<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
  updateAndAudit,
} from "@/api/ent/info";

export default {
  name: "audit",
  dicts: ["ent_economic_nature", "base_ent_status", "base_ent_type"],
  data() {
    return {
      // 遮罩层
      loading: true,
      form: {},
      title: "",
      dialogVisible: false,
    };
  },
  created() {
    this.getInfo();
  },
  methods: {
    getInfo() {
      this.loading = true;
      getInfo(this.$route.params.entId).then((response) => {
        if (response.code == 200) {
          this.form = response.data;
          this.loading = false;
        }
      });
    },
    updateStatus(options) {
      if (options) {
        // 驳回
        this.form.auditResult = false;
        this.title = "驳回原因";
        this.form.auditRemark = "";
        this.dialogVisible = true;
      } else {
        // 同意
        this.form.auditResult = true;
        this.title = "通过原因";
        this.form.auditRemark = "通过";
        this.dialogVisible = true;
      }
    },
    confirmed() {
      if (this.form.auditRemark == "" || this.form.auditRemark == null) {
        this.$message.warning("请输入原因");
      } else {
        updateAndAudit(this.form).then((response) => {
          if (response.code == 200) {
            this.dialogVisible = false;
            const obj = { path: "/ent/info" };
            this.$tab.closeOpenPage(obj);
          }
        });
      }
    },
    cancel() {
      this.form.auditRemark = "";
      this.dialogVisible = false;
    },
  },
};
</script>
      
<style>
.dialog-footer {
  text-align: center;
}
</style>