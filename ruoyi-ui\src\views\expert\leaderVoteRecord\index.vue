<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="投票专家的id" prop="voterExpertId">
        <el-input
          v-model="queryParams.voterExpertId"
          placeholder="请输入投票专家的id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="被投票专家" prop="candidateExpertId">
        <el-input
          v-model="queryParams.candidateExpertId"
          placeholder="请输入被投票专家"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投票时间" prop="voteTime">
        <el-date-picker clearable
          v-model="queryParams.voteTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择投票时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="专家组ID，与专家抽取结果表中的group_id关联" prop="groupId">
        <el-input
          v-model="queryParams.groupId"
          placeholder="请输入专家组ID，与专家抽取结果表中的group_id关联"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['expert:leaderVoteRecord:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['expert:leaderVoteRecord:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['expert:leaderVoteRecord:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['expert:leaderVoteRecord:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="leaderVoteRecordList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="投票记录id" align="center" prop="voteRecordId" />
      <el-table-column label="投票专家的id" align="center" prop="voterExpertId" />
      <el-table-column label="被投票专家" align="center" prop="candidateExpertId" />
      <el-table-column label="投票时间" align="center" prop="voteTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.voteTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="专家组ID，与专家抽取结果表中的group_id关联" align="center" prop="groupId" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['expert:leaderVoteRecord:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['expert:leaderVoteRecord:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专家组长投票记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="投票专家的id" prop="voterExpertId">
          <el-input v-model="form.voterExpertId" placeholder="请输入投票专家的id" />
        </el-form-item>
        <el-form-item label="被投票专家" prop="candidateExpertId">
          <el-input v-model="form.candidateExpertId" placeholder="请输入被投票专家" />
        </el-form-item>
        <el-form-item label="投票时间" prop="voteTime">
          <el-date-picker clearable
            v-model="form.voteTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择投票时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="专家组ID，与专家抽取结果表中的group_id关联" prop="groupId">
          <el-input v-model="form.groupId" placeholder="请输入专家组ID，与专家抽取结果表中的group_id关联" />
        </el-form-item>
        <el-form-item label="删除标记，0正常 1删除" prop="delFlag">
          <el-select v-model="form.delFlag" placeholder="请选择删除标记，0正常 1删除">
            <el-option
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLeaderVoteRecord, getLeaderVoteRecord, delLeaderVoteRecord, addLeaderVoteRecord, updateLeaderVoteRecord } from "@/api/expert/leaderVoteRecord";

export default {
  name: "LeaderVoteRecord",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 专家组长投票记录表格数据
      leaderVoteRecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        voterExpertId: null,
        candidateExpertId: null,
        voteTime: null,
        groupId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        voterExpertId: [
          { required: true, message: "投票专家的id不能为空", trigger: "blur" }
        ],
        candidateExpertId: [
          { required: true, message: "被投票专家不能为空", trigger: "blur" }
        ],
        groupId: [
          { required: true, message: "专家组ID，与专家抽取结果表中的group_id关联不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询专家组长投票记录列表 */
    getList() {
      this.loading = true;
      listLeaderVoteRecord(this.queryParams).then(response => {
        this.leaderVoteRecordList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        voteRecordId: null,
        voterExpertId: null,
        candidateExpertId: null,
        voteTime: null,
        groupId: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.voteRecordId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加专家组长投票记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const voteRecordId = row.voteRecordId || this.ids
      getLeaderVoteRecord(voteRecordId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改专家组长投票记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.voteRecordId != null) {
            updateLeaderVoteRecord(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLeaderVoteRecord(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const voteRecordIds = row.voteRecordId || this.ids;
      this.$modal.confirm('是否确认删除专家组长投票记录编号为"' + voteRecordIds + '"的数据项？').then(function() {
        return delLeaderVoteRecord(voteRecordIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('expert/leaderVoteRecord/export', {
        ...this.queryParams
      }, `leaderVoteRecord_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
