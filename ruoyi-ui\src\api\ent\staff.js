import request from '@/utils/request'

// 查询企业员工信息列表
export function listStaff(query) {
  return request({
    url: '/ent/staff/list',
    method: 'get',
    params: query
  })
}

// 查询企业员工信息详细
export function getStaff(staffId) {
  return request({
    url: '/ent/staff/' + staffId,
    method: 'get'
  })
}

// 新增企业员工信息
export function addStaff(data) {
  return request({
    url: '/ent/staff',
    method: 'post',
    data: data
  })
}

// 修改企业员工信息
export function updateStaff(data) {
  return request({
    url: '/ent/staff',
    method: 'put',
    data: data
  })
}

// 删除企业员工信息
export function delStaff(staffId) {
  return request({
    url: '/ent/staff/' + staffId,
    method: 'delete'
  })
}
