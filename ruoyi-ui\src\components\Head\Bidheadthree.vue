<template>
  <div class="head">
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :lg="24"
      >
        <div class="head-bottom">
          <div class="search">
            <div style="margin-right: 92px; margin-left: 10px">
              <div style="width: 300px; height: 50px;line-height: 50px;text-align: center;font-family: SourceHanSansSC-Heavy;font-weight: 900;font-size: 35px;color: #333333;letter-spacing: 0;">
                专家评审系统
              </div>
            </div>
          </div>
          <el-image
            class="background-img"
            :src="background"
            fit="cover"
          ></el-image>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getProjectStatus } from "@/api/onlineBidOpening/info";
export default {
  name: "Head",
  data() {
    return {
      background: require("@/assets/images/home-background.png"),
      userInfo: {},
    };
  },
  watch: {},
  created() {
    this.userInfo = this.$store.state.user;
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.head {
  height: 150px;
  .head-bottom {
    height: 150px;
    padding: 0 15%;
    position: relative;
    border-bottom: #176adb 1px solid;
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        width: 110px;
        height: 41px;
        line-height: 41px;
        font-family: SourceHanSansSC-Bold;
        font-weight: 700;
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;
        border-radius: 6px;
        text-align: center;

        margin-right: 60px;
        cursor: pointer;
      }
      .active {
        background: #176adb;
        color: #fff;
      }
    }
    .search {
      height: 148px;
      display: flex;
      align-items: center;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      .query {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;

        margin-right: 30px;
        width: 470px;
        height: 46px;
        .input {
          border-radius: 6px 0 0 6px;
          ::v-deep .el-input__inner {
            border: 2px solid #176adb;
          }
        }
        .button {
          background: #176adb;
          width: 46px;
          padding-right: 0;
          padding-left: 0;
          color: #fff;
          border-radius: 0 6px 6px 0;
          border: 1px solid #176adb;
        }
      }
      .publish {
        width: 166px;
        height: 46px;
        background: #176adb;
        border-radius: 6px;
        color: #ffffff;

        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: justify;
        text-align: center;
      }
    }

    .background-img {
      position: absolute;
      bottom: 0;
      right: 15.7%;
      z-index: -1; /* 将图片放置在其他元素下方 */
    }
  }
}
</style>
