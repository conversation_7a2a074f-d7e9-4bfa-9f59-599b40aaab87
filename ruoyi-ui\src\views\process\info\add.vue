<template>
  <div>
    <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="120px">
      <el-form-item label-width="120px" label="采购项目" prop="projectId">
        <el-select v-model="formData.projectId" placeholder="请选择采购项目" filterable clearable
          :style="{width: '80%'}" @change="changeProject">
          <el-option v-for="(item, index) in projectIdOptions" :key="index" :label="item.projectName"
            :value="item.projectId" :disabled="item.disabled"></el-option>
        </el-select>
      </el-form-item>

      <el-table v-loading="loading" :data="attachments" style="width: 90%;margin: auto;" :show-header="false">
        <el-table-column colspan label="模块" width="100" align="center" prop="name"/>
        <el-table-column label="文件类型" align="center" >
            <template slot-scope="scope" >
                <el-table :data="scope.row.fileMap" :show-header="false">
                  <el-table-column width="100"  label="模块" align="center" prop="name" />
                  <el-table-column  label="路径" align="center"  >
                    <template slot-scope="scope" >
                        <FileUpload
                            :value="scope.row.filePath"
                            @input="handleInput(code, $event)"
                            :fileType="['pdf', 'doc', 'docx']"
                            :isShowTip="false"
                            :showOnly=false
                      ></FileUpload>
                      </template>
           </el-table-column>
                </el-table>
            </template>
        </el-table-column>
      </el-table>

      <el-form-item size="large">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button type="primary" @click="initAttachment">初始化文件</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getOpening, delOpening, addOpening, updateOpening, openingProject,saveWithRecord } from "@/api/bid/opening";
import { listInfo, getInfo, delInfo, addInfo, updateInfo,selectProcessAttachment } from "@/api/process/info";
import { getDicts } from "@/api/system/dict/data";
import { listData,listWithTreeData } from "@/api/tree/data";
export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      formData: {
        processId:'',
        projectId: '',
        attachmentMap: {},
      },
      attachments: [
     /*   {
          "code":"01",
          "name":"采购意向采购意向采购意向",
          "fileMap":[
            {"code":"0101","name":"附件1","filePath":"xxx.pdf,yyy.pdf"},
            {"code":"0102","name":"附件2","filePath":"aaa.pdf,bbb.pdf"}
          ]
        }*/
      ],
      rules: {
        projectId: [{
          required: true,
          message: '请选择采购项目',
          trigger: 'change'
        }]
      },
      projectIdOptions: [],
      // 投标人表格数据
      tradderList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    this.getProjectList();
    //this.getInfo();
    // this.attachmentMap = {1:"/xxx.pdf", 2:"xxx.pdf"};
  },
  mounted() {
  },
  methods: {
    submitForm() {
      this.$refs["elForm"].validate(valid => {
        // this.attachmentMap.foreach((value, key) => {
        //   console.info(key+"    "+value);
        // });
        var al = [];
        for(var key in this.attachmentMap){
          if(this.attachmentMap[key] != ''){
          var all = this.attachmentMap[key].split(',');
          all.forEach(element => {
            al.push({"fileType":key, "filePath":element});
          });
        }
        this.formData.attachmentList = al;
        console.info(this.formData);
        }
        if (valid) {
          if (this.formData.bidOpeningId != null) {
            updateOpening(this.formData).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            saveWithRecord(this.formData).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    initAttachment(){
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getInfo(processId){
      selectProcessAttachment(processId).then((result) => {
        console.info(result);
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },
    handleInput(i, j){
        console.info(i+"    "+j);
        this.attachmentTypes[i] = j;
        console.info(this.attachmentTypes);
        this.attachmentMap[i] = j;
    },
    getProjectList(){
      console.info("getProjectList")
      openingProject().then((result) => {
        if(result.code==200){
          console.info(result);
          console.info(result.data);
          this.projectIdOptions = result.data;
        }
      });
    },
    getAttachmentTypes(){
      getDicts("busi_bid_opening_attachment").then((result) => {
        if(result.code==200){
          console.info(result);
          this.attachmentTypes = result.data;
        }
      });
    },
    changeProject(){
      console.info(this.formData.projectId);
      this.loading = true;
      getInfo({projectId:this.formData.projectId}).then(
        (result) => {
        if(result.code==200){
          console.info(result);
          this.tradderList = result.data;
          this.loading = false;
        }
        });
    }
  }
}

</script>
<style>
.el-upload__tip {
  line-height: 1.2;
}

</style>
<style scoped>
/deep/ .el-upload {
 float: right;
}
/deep/ .el-upload-list {
 width: 90%;
}
</style>
