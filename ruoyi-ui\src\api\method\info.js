import request from '@/utils/request'

// 查询评分办法信息列表
export function listInfo(query) {
  return request({
    url: '/method/info/list',
    method: 'get',
    params: query
  })
}

// 查询采购文件编制基础信息列表
export function infoByParams(query) {
  return request({
    url: '/method/info/infoByParams',
    method: 'get',
    params: query
  })
}

// 查询评分办法信息详细
export function getInfo(scoringMethodId) {
  return request({
    url: '/method/info/' + scoringMethodId,
    method: 'get'
  })
}

// 新增评分办法信息
export function addInfo(data) {
  return request({
    url: '/method/info',
    method: 'post',
    data: data
  })
}

// 修改评分办法信息
export function updateInfo(data) {
  return request({
    url: '/method/info',
    method: 'put',
    data: data
  })
}

// 删除评分办法信息
export function delInfo(scoringMethodId) {
  return request({
    url: '/method/info/' + scoringMethodId,
    method: 'delete'
  })
}
