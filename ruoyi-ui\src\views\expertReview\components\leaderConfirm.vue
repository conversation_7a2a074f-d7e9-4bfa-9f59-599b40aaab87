<template>
  <div class="discipline">
    <div class="titel">
      确认专家组长
    </div>
    <div class="tips">
      <div
        class="item"
        style="cursor: pointer;width:300px"
      >
        <el-card
          class="box-card"
          style="width: 100%;"
          shadow="hover"
        >
          <div class="info">
            <div>{{ leader.xm }}</div>
            <div>担任组长次数：{{ leader.expertLeaderCount }}</div>
            <div v-if="leader.zhuanJiaInfoVo">{{ leader.zhuanJiaInfoVo.zc }}</div>
          </div>
        </el-card>
      </div>
    </div>
    <div style="text-align:center">
      <!-- <el-button
        class="item-button"
        style="background-color:#F5F5F5;color:#176ADB"
        @click="downloadTender"
      >下载标书</el-button> -->
      <el-button
        class="item-button"
        @click="startBidEvaluation"
      >开始评标</el-button>
    </div>
  </div>
</template>

<script>
import { expertInfoById } from "@/api/expert/review";
import { updateNode } from "@/api/evaluation/expertNodeInfo";
export default {
  name: "comfirm",
  data() {
    return {
      leader: {},
    };
  },
  methods: {
    init() {
      expertInfoById({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        if (response.code == 200) {
          this.leader = response.data.find((item) => {
            return item.expertLeader == 1;
          });
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    downloadTender() {},
    startBidEvaluation() {
      updateNode({
        evalExpertEvaluationInfoId: localStorage.getItem(
          "evalExpertEvaluationInfoId"
        ),
        evalNode: 6,
      }).then((result) => {
        if (result.code == 200) {
          this.$emit("send", "flow");
        }
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.discipline {
  padding: 20px 30px;
}
.titel {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 22px;
  color: #333333;
  letter-spacing: 0;
  text-align: left;
  padding: 20px 0;
}
.tips {
  display: flex;
  justify-content: center;
  margin-bottom: 20px;

  .box-card {
    .info {
      display: grid;
      justify-items: center;
      align-content: space-between;
      height: 200px;
      div {
        min-height: 30px;
      }
    }
  }
}
::v-deep .box-card {
  background-color: #176adb;
  color: #ffffff;
  border-radius: 4px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.selected {
  border: 2px solid #409eff; /* 你可以根据需要修改颜色 */
  background-color: #f5f7f9; /* 可选的背景颜色 */
}
</style>