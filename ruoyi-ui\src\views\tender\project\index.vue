<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目代码" prop="projectCode">
        <el-input v-model="queryParams.projectCode" placeholder="请输入项目代码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="项目名称" prop="projectName">
        <el-input v-model="queryParams.projectName" placeholder="请输入项目名称" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>

      <el-form-item label="采购方式" prop="tenderMode">
        <el-select v-model="queryParams.tenderMode" placeholder="请选择采购方式" clearable>
          <el-option v-for="dict in dict.type.busi_tender_mode" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="项目类型" prop="projectType">
        <el-select v-model="queryParams.projectType" placeholder="请选择项目类型" clearable>
          <el-option v-for="dict in dict.type.busi_project_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="获取投标人方式" prop="projectType">
        <el-select v-model="queryParams.projectType" placeholder="请选择项目类型" clearable>
          <el-option v-for="dict in dict.type.busi_project_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button v-if="!isAgency" type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd" v-hasPermi="['tender:project:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">

      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete" v-hasPermi="['tender:project:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport" v-hasPermi="['tender:project:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="projectList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />

      <el-table-column label="状态" align="center" prop="busiState">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.busiState==0?'red':'#606266' }">
            {{ scope.row.busiState==0?'待提交':'已提交' }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="项目名称" align="center" prop="projectName" />

      <el-table-column label="采购人联系人" align="center" prop="tendererContactPerson" />
      <el-table-column label="采购人联系方式" align="center" prop="tendererPhone" />
      <el-table-column label="采购方式" align="center" prop="tenderMode">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_tender_mode" :value="scope.row.tenderMode" />
        </template>
      </el-table-column>
      <el-table-column label="项目类型" align="center" prop="projectType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_project_type" :value="scope.row.projectType" />
        </template>
      </el-table-column>
      <el-table-column label="是否为代理机构" align="center" prop="agencyFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_yes_no" :value="scope.row.agencyFlag" />
        </template>
      </el-table-column>
      <el-table-column label="获取投标人方式" align="center" prop="getBidderMode">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.get_bidder_mode" :value="scope.row.getBidderMode" />
        </template>
      </el-table-column>
      <el-table-column label="代理机构名称" align="center" prop="agencyName" />

      <el-table-column label="预算金额" align="center" prop="budgetAmount" />

      <el-table-column label="资金来源" align="center" prop="tenderFundSource">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.tender_project_fundsource" :value="scope.row.tenderFundSource" />
        </template>
      </el-table-column>

      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleView(scope.row)">查看</el-button>
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" v-hasPermi="['tender:project:edit']" v-if="scope.row.busiState==0">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)" v-hasPermi="['tender:project:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改采购项目信息对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目代码" prop="projectCode">
          <el-input v-model="form.projectCode" placeholder="请输入项目代码" />
        </el-form-item>
        <el-form-item label="项目意向id" prop="projectIntentionId">
          <el-input v-model="form.projectIntentionId" placeholder="请输入项目意向id" />
        </el-form-item>
        <el-form-item label="紧急项目" prop="isEmergencyProject">
          <el-select v-model="form.isEmergencyProject" placeholder="请选择紧急项目">
            <el-option v-for="dict in dict.type.base_yes_no" :key="dict.value" :label="dict.label" :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目名称" prop="projectName">
          <el-input v-model="form.projectName" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="项目内容">
          <editor v-model="form.projectContent" :min-height="192" />
        </el-form-item>
        <el-form-item label="投标人资格要求" prop="bidderQualification">
          <el-input v-model="form.bidderQualification" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="项目所属区域" prop="projectArea">
          <el-input v-model="form.projectArea" placeholder="请输入项目所属区域" />
        </el-form-item>
        <el-form-item label="项目所属行业" prop="projectIndustry">
          <el-input v-model="form.projectIndustry" placeholder="请输入项目所属行业" />
        </el-form-item>
        <el-form-item label="采购人id" prop="tendererId">
          <el-input v-model="form.tendererId" placeholder="请输入采购人id" />
        </el-form-item>
        <el-form-item label="采购人代码" prop="tendererCode">
          <el-input v-model="form.tendererCode" placeholder="请输入采购人代码" />
        </el-form-item>
        <el-form-item label="采购人名称" prop="tendererName">
          <el-input v-model="form.tendererName" placeholder="请输入采购人名称" />
        </el-form-item>
        <el-form-item label="采购人联系人" prop="tendererContactPerson">
          <el-input v-model="form.tendererContactPerson" placeholder="请输入采购人联系人" />
        </el-form-item>
        <el-form-item label="采购人联系方式" prop="tendererPhone">
          <el-input v-model="form.tendererPhone" placeholder="请输入采购人联系方式" />
        </el-form-item>
        <el-form-item label="采购方式" prop="tenderMode">
          <el-select v-model="form.tenderMode" placeholder="请选择采购方式">
            <el-option v-for="dict in dict.type.busi_tender_mode" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="项目类型" prop="projectType">
          <el-select v-model="form.projectType" placeholder="请选择项目类型">
            <el-option v-for="dict in dict.type.busi_project_type" :key="dict.value" :label="dict.label" :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否为代理机构" prop="agencyFlag">
          <el-radio-group v-model="form.agencyFlag">
            <el-radio v-for="dict in dict.type.base_yes_no" :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="代理机构id" prop="agencyId">
          <el-input v-model="form.agencyId" placeholder="请输入代理机构id" />
        </el-form-item>
        <el-form-item label="代理机构代码" prop="agencyCode">
          <el-input v-model="form.agencyCode" placeholder="请输入代理机构代码" />
        </el-form-item>
        <el-form-item label="代理机构名称" prop="agencyName">
          <el-input v-model="form.agencyName" placeholder="请输入代理机构名称" />
        </el-form-item>
        <el-form-item label="代理机构联系人" prop="agencyContactPerson">
          <el-input v-model="form.agencyContactPerson" placeholder="请输入代理机构联系人" />
        </el-form-item>
        <el-form-item label="代理机构联系方式" prop="agencyPhone">
          <el-input v-model="form.agencyPhone" placeholder="请输入代理机构联系方式" />
        </el-form-item>
        <el-form-item label="预算金额" prop="budgetAmount">
          <el-input v-model="form.budgetAmount" placeholder="请输入预算金额" />
        </el-form-item>
        <el-form-item label="控制价" prop="controlPrice">
          <el-input v-model="form.controlPrice" placeholder="请输入控制价" />
        </el-form-item>
        <el-form-item label="资金来源" prop="tenderFundSource">
          <el-input v-model="form.tenderFundSource" placeholder="请输入资金来源" />
        </el-form-item>
        <el-form-item label="自筹资金" prop="tenderSelfFund">
          <el-input v-model="form.tenderSelfFund" placeholder="请输入自筹资金" />
        </el-form-item>
        <el-form-item label="财政资金" prop="tenderFinancialFund">
          <el-input v-model="form.tenderFinancialFund" placeholder="请输入财政资金" />
        </el-form-item>
        <el-form-item label="项目工期，单位天" prop="projectDuration">
          <el-input v-model="form.projectDuration" placeholder="请输入项目工期，单位天" />
        </el-form-item>
        <el-form-item label="删除标记" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio v-for="dict in dict.type.base_yes_no" :key="dict.value" :label="parseInt(dict.value)">{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listProject, getProject, delProject, addProject, updateProject } from "@/api/tender/project";
import { getUserProfile } from "@/api/system/user";

export default {
  name: "Project",
  dicts: ['busi_tender_mode', 'busi_project_type','get_bidder_mode', 'base_yes_no', 'tender_project_fundsource'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购项目信息表格数据
      projectList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记时间范围
      daterangeCreateTime: [],
      // 删除标记时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectCode: null,
        projectIntentionId: null,
        isEmergencyProject: null,
        projectName: null,
        projectContent: null,
        bidderQualification: null,
        projectArea: null,
        projectIndustry: null,
        tendererId: null,
        tendererCode: null,
        tendererName: null,
        tendererContactPerson: null,
        tendererPhone: null,
        tenderMode: null,
        projectType: null,
        agencyFlag: null,
        agencyId: null,
        agencyCode: null,
        agencyName: null,
        agencyContactPerson: null,
        agencyPhone: null,
        budgetAmount: null,
        controlPrice: null,
        tenderFundSource: null,
        tenderSelfFund: null,
        tenderFinancialFund: null,
        projectDuration: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        getBidderMode:null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      },
      // 是否是代理机构
      isAgency: true
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购项目信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listProject(this.queryParams).then(response => {
        this.projectList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
      // 获取用户信息
      getUserProfile().then((response) => {
        if (response.data.ent) {
          if (response.data.ent.entType == 2) {
            this.isAgency = true;
          } else {
            this.isAgency = false;
          }
        }
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        projectId: null,
        projectCode: null,
        projectIntentionId: null,
        isEmergencyProject: null,
        projectName: null,
        projectContent: null,
        bidderQualification: null,
        projectArea: null,
        projectIndustry: null,
        tendererId: null,
        tendererCode: null,
        tendererName: null,
        tendererContactPerson: null,
        tendererPhone: null,
        tenderMode: null,
        projectType: null,
        agencyFlag: null,
        agencyId: null,
        agencyCode: null,
        agencyName: null,
        agencyContactPerson: null,
        agencyPhone: null,
        budgetAmount: null,
        controlPrice: null,
        tenderFundSource: null,
        tenderSelfFund: null,
        tenderFinancialFund: null,
        projectDuration: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.projectId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.$router.push('/tender/project/add/0')
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const projectId = row.projectId || this.ids
      getProject(projectId).then(response => {
        // this.form = response.data;
        // this.open = true;
        // this.title = "修改采购项目信息";
        this.$router.push('/tender/project/add/' + projectId)
      });
    },
    /** 查看详情按钮操作 */
    handleView(row) {
      const projectId = row.projectId || this.ids
      this.$router.push('/tender/project/view/' + projectId);
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.projectId != null) {
            updateProject(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addProject(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const projectIds = row.projectId || this.ids;
      this.$modal.confirm('是否确认删除采购项目信息编号为"' + projectIds + '"的数据项？').then(function () {
        return delProject(projectIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('tender/project/export', {
        ...this.queryParams
      }, `project_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
