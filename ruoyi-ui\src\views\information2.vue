<template>
  <div>

    <Head></Head>
    <div class="overAll">
      <div class="app-container home">
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="24"
          >
            <el-card
              shadow="never"
              class="informationPublicity"
            >
              <div
                slot="header"
                class="announcement"
              >
                <div class="info">
                  <div class="clearfix">信息标题</div>
                  <div class="subtitle">
                    <div><span>发布机构:测试代理机构</span></div>
                    <div><span>发布日期:2023-07-19 17:51</span></div>
                    <div><span>访问次数:31432</span></div>
                  </div>
                </div>
              </div> 
              <div
                class="content"
              >  <div class="tender" >
    <el-col :span="24" class="card-box">
    <div v-html="information"></div>
    </el-col>
  </div></div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
    <Foot></Foot>
  </div>
</template>

<script>
import { listIntention } from "@/api/tender/intention";
import {
  portalNoticeList,
  portalNoticeInfo,
  portalNoticeProcess,
} from "@/api/portal/portal";
import { getNoticeTypes } from "@/api/tender/notice";
export default {
  data() {
    return {
      loading:false,
      activeName:'first',
      projectId: "",
      isShowTooltip: false,
      isShowDot: true,
      activeValue: "20",
      information: "123",
      activities: [],
      noticeList:[
        {noticeId:1148677039236101, changeNum:1, noticeName:'第1次变更', noticeType:2},
        {noticeId:1148447265752069, changeNum:0, noticeName:'采购公告', noticeType:1},
      ],
      id: '',
    };
  },
  created() {
    this.projectId = this.$route.query.projectId;
    var dataType = this.$route.query.dataType;
    this.activeValue = dataType;
    this.process();
    if(dataType==20){
      this.getNoticeTypes();
    }
    this.getInfo(dataType, true, 0);
  },
  methods: {
    // 获取通知公告
    process() {
      portalNoticeProcess({ projectId: this.projectId }).then((result) => {
        if (result.code == 200) {
          this.activities = result.data;
          console.log(this.activities);
        }
      });
    },
    // 获取通知公告
    getNoticeTypes() {
      getNoticeTypes(this.projectId).then((result) => {
        if (result.code == 200) {
          this.noticeList = result.data;
          if(this.noticeList.length>0){
            this.activeName = this.noticeList[0].noticeId+'';
          }
          console.log(this.noticeList);
        }
      });
    },
    getInfo(dataType, done, delFlag) {
      if (done) {
        this.activeValue = dataType;
        portalNoticeInfo({
          id: this.id,
          projectId: this.projectId,
          dataType: dataType,
          delFlag: delFlag
        }).then((result) => {
          if (result.code == 200) {
            this.information = result.data;
          }
        });
      }
    },
    getTenderIntentionData() {
      this.bodyCenterTitle = "采购意向";
      //获取采购意向
      listIntention({
        pageNum: 1,
        pageSize: 4,
        params: {
          returnEntName: true,
          orderByAsc: "intention_end_time",
          geIntentionEndTime: this.parseTime(
            new Date(),
            "{y}-{m}-{d} {h}:{i}:{s}"
          ),
        },
      }).then((result) => {
        console.log("listIntention", result);
        this.haveSignedUp = result.rows.map((item) => {
          return {
            title: item.intentionName,
            price: item.budgetAmount,
            serveType: "服务工程",
            releaseTime: item.intentionStartTime,
            purchasingUnit: item.tendererName,
            deadline5: new Date(`${item.intentionEndTime}T23:59:59`),
          };
        });
      });
    },
    changeNoticeVersion(tab, event) {
      console.log("----------------------changeNoticeVersion");
        console.log(tab);
        console.log(event);
        this.id = tab.name;
        this.getInfo(this.activeValue, true, tab.index==0?0:1);
      }
  },
};
</script>

<style scoped lang="scss">
.overAll {
  position: relative;

  background-color: #f5f5f5;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
}
.home {
  background-color: #f5f5f5;
  width: 70%;
  .el-row {
    margin-bottom: 20px;
  }
  .timelineProcessBox {
    .timeline {
      display: flex;
      width: 95%;
      margin: 40px auto 70px auto;
      justify-content: center;
      .lineitem {
        position: relative;
        transform: translateX(50%);
        width: 25%;
        .flag {
          position: absolute;
          top: -21px;
          right: 30px;
        }
      }
    }
  }
  .informationPublicity {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
    }
    .announcement {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: wrap;
      flex-direction: row;
      .info {
        width: 100%;
        .subtitle {
          display: flex;
          justify-content: center;
          align-items: center;
          flex-wrap: nowrap;
          flex-direction: row;

          font-family: SourceHanSansSC-Bold;
          font-weight: 400;
          font-size: 14px;
          color: #999999;
          letter-spacing: 0;
          text-align: center;

          div:not(:last-child) {
            margin-right: 20px;
          }
        }
      }
    }
    .content {
      min-height: 450px;
      padding: 10px 20px 20px;
    }
  }
  .clearfix {
    font-family: SourceHanSansSC-Bold;
    font-weight: 400;
    font-size: 35px;
    color: #666666;
    letter-spacing: 0;
    text-align: center;

    margin-bottom: 10px;
  }
  .more {
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #176adb;
    letter-spacing: 0;
    float: right;
  }
  .tr_title{
    padding-top:25px;
    // border-top: 1px #dfdede solid;
  }
}
</style>
<style scoped lang="scss">
 .el-table td.el-table__cell{
  border-bottom:0px;
  padding:4px 0;
  font-size:15px;
}
// ::v-deep .el-card__header {
//   border-bottom: none;
// }
::v-deep .el-table .even-row {
  background: #eff6ff;
}

::v-deep .el-table .odd-row {
  background: #ffffff;
}
::v-deep .el-timeline-item__node--normal {
  left: -15px;
  top: -8px;
  width: 30px;
  height: 30px;
}

::v-deep .el-timeline-item__tail {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}
::v-deep .el-timeline-item__wrapper {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}
::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}
.active {
  ::v-deep .el-timeline-item__node {
    background-color: #176adb;
  }
  ::v-deep .el-timeline-item__tail {
    border-color: #176adb;
  }
}
.inactive {
  ::v-deep .el-timeline-item__tail {
    border-color: #176adb;
  }
}
.active + li {
  ::v-deep .el-timeline-item__node {
    // background-color: #000;
  }
}
</style>
