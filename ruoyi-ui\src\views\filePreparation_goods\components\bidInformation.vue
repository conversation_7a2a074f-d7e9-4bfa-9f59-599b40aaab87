<template>
  <div class="container">
    <div class="transfer">
      <div style="text-align: right; height: 50px;">
        <!--        <el-button
          class="item-button"
          style="background-color: #fff; height: 40px; color: #333"
          @click="open()"
          >新增</el-button
        >-->
      </div>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column label="序号" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="openBid" label="开标项" width="180">
        </el-table-column>
        <el-table-column prop="defaultValue" label="默认项"> </el-table-column>
        <el-table-column prop="remark" label="备注"> </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button v-if="scope.row.isFixed" @click="handleUpdateClick(scope.row)" type="text" size="small">编辑</el-button>
            <!-- <el-button v-else @click="handleClick(scope.row)" type="text" size="small">删除</el-button> -->
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog title="修改开标项" :visible.sync="dialogVisible" width="30%">
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="开标项" prop="openBid">
          <el-input v-model="form.openBid"></el-input>
        </el-form-item>
        <el-form-item label="默认值" prop="defaultValue">
          <el-input v-model="form.defaultValue"></el-input>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleConfirm()">确 定</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      isSave: true,
      itemInfo: {},
      originTableData: [
        {
          isFixed: false,
          code: "projectName",
          openBid: "项目名称",
          defaultValue: "",
          remark: "",
        },
        {
          isFixed: false,
          code: "bidder",
          openBid: "响应人名称",
          defaultValue: "",
          remark: "",
        },
        {
          isFixed: false,
          code: "bidPrice",
          openBid: "投标报价（元）",
          defaultValue: "",
          remark: "只能输入金额数字，不要带单位",
        },
        {
          isFixed: true,
          code: "qualityDemand",
          openBid: "质量要求",
          defaultValue: "合格",
          remark: "",
        },
        {
          isFixed: false,
          code: "overTimeLimit",
          openBid: "供货期",
          defaultValue: "",
          remark: "默认单位为日历日。只能输入整数，不要带单位",
        },
        {
          isFixed: true,
          code: "warrantyPeriod",
          openBid: "质保期",
          defaultValue: "",
          remark: "默认单位为年。只能输入整数，不要带单位",
        },
        {
          isFixed: true,
          code: "bidValidityPeriod",
          openBid: "投标有效期",
          defaultValue: "90",
          remark: "默认单位为日历日。只能输入整数，不要带单位",
        },
      ],
      tableData: [],
      projectInfo: {},
      dialogVisible: false,
      form: {
        code: "",
        isFixed: false,
        openBid: "",
        defaultValue: "",
        remark: "",
      },
      rules: {
        openBid: [{ required: true, message: "请输入开标项", trigger: "blur" }],
        defaultValue: [
          { required: true, message: "请输入默认项", trigger: "blur" },
        ],
      },
    };
  },
  methods: {
    handleUpdateClick(row) {
      this.form.code = row.code;
      this.form.openBid = row.openBid;
      this.form.defaultValue = row.defaultValue;
      this.form.isFixed = row.isFixed;
      this.form.remark = row.remark;
      this.isSave = false;
      this.dialogVisible = true;
    },
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.tableData = JSON.parse(itemInfo.itemContent);
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
      if (!this.tableData || this.tableData.length == 0) {
        this.tableData = this.originTableData;
        console.log("tableData",this.tableData)
        this.saveUinfo();
      }
    },
    //  删除
    handleClick(row) {
      this.tableData.splice(this.tableData.indexOf(row), 1);
      this.saveUinfo();
    },
    // 新建开标项
    open() {
      this.form.openBid = "";
      this.form.defaultValue = "";
      this.dialogVisible = true;
      this.form.isFixed = false;
      this.form.remark = "";
      this.isSave = true;
    },
    // 确认
    handleConfirm() {
      //前端增加，改为后端接口增加时可删除
      let a = {
        code: this.form.code,
        isFixed: this.form.isFixed,
        openBid: this.form.openBid,
        defaultValue: this.form.defaultValue,
        remark: this.form.remark,
      };
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            if (this.isSave) {
              this.tableData.push(a);
            } else {
              this.updateTableData(a);
            }
            this.saveUinfo();
          } else {
            return false;
          }
        });
      }
    },
    updateTableData(a) {
      this.tableData = this.tableData.map((element) => {
        if (a.openBid == element.openBid) {
          element = a;
        }
        return element;
      });
    },
    saveUinfo() {
          this.tableData[0].defaultValue = this.projectInfo.projectName;
          this.tableData[4].defaultValue = this.projectInfo.projectDuration;
          this.tableData[2].defaultValue = this.projectInfo.budgetAmount;
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: JSON.stringify(this.tableData),
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-icon {
  width: 20px;
  height: 20px;
  color: #333;
}
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}
.transfer {
  width: 90%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
