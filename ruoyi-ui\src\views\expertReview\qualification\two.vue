<template>
  <div class="three">
    <div style="position: absolute;left: 25px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;">资格性评审</div>
    <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
      <el-table-column prop="供应商名称" width="180">
      </el-table-column>
      <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.itemName" :label="item.itemName">
        <template slot-scope="scope">
          <i style="color:#176ADB;font-size:20px" :class="getIconClass(scope.row[item.itemName])"></i>

        </template>
      </el-table-column>
    </el-table>

    <div class="result">
      <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin-bottom:20px">评审结果：</div>
      <div style="display: flex;margin-left:30px">
        <div style="margin-right:30px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;" v-for="(item,index) in result" :key="index">
          {{ item.gys }}：
          <span v-if="item.result" style="color:green">
            通过
          </span>
          <span v-else style="color:red">
            不通过
          </span>
        </div>
      </div>
      <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 18px;color: #333333;letter-spacing: 0;margin:20px 0">表决意见：</div>
      <div style="display: flex;margin-left:30px">
        <el-input class="text" type="textarea" :rows="4" placeholder="请输入表决意见" v-model="evalContent">
        </el-input>
      </div>
    </div>
    <div v-if="!finish" class="operation">
      <el-button class="item-button" @click="modify">修改</el-button>
      <el-button class="item-button" style="background-color: #176adb;color: #f5f5f5;" @click="submit">确认提交</el-button>
    </div>
    <div v-else class="operation">
      <el-button class="item-button" @click="back">返回</el-button>
    </div>
  </div>

</template>

<script>
import { summaryQuery } from "@/api/expert/review";
import { editEvalExpertScoreInfo } from "@/api/evaluation/expertStatus";
export default {
  props: {
    isLeader: {
      type: Boolean,
      default: false,
    },
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      result: {},
      evalContent: "",
      headStyle: {
        "text-align": "left",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
        resultId: expertInfo.resultId,
      };
      summaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          this.columns = response.data.tableColumns;
          this.result = this.generateResultTable(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.result = this.result.filter(item => item.isAbandonedBid == 0)
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    modify() {
      const status = {
        evalExpertScoreInfoId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).evalExpertScoreInfoId,
        evalContent: this.evalContent,
        evalState: 0,
      };
      editEvalExpertScoreInfo(status).then((res) => {
        if (res.code == 200) {
            this.$emit("send", "one");
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    submit() {
      const status = {
        evalExpertScoreInfoId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).evalExpertScoreInfoId,
        evalContent: this.evalContent,
        evalState: 2,
      };
      editEvalExpertScoreInfo(status).then((res) => {
        if (res.code == 200) {
          this.$message.success("提交成功");
          if (this.isLeader) {
            this.$emit("send", "three");
          } else {
            this.$router.push({
              path: "/expertInfo",
              query: {
                projectId: this.$route.query.projectId,
                zjhm: this.$route.query.zjhm,
              },
            });
          }
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 entMethodItemId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.entMethodItemId] = column.itemName;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.entMethodItemId;
          transformedRow[column.itemName] = row[itemId] || "0"; // 默认为 '0' 如果没有找到对应值
        });

        return transformedRow;
      });
    },
    // 组装评审结果
    generateResultTable(tableColumns, busiBidderInfos, tableData) {
      const entMethodItemIds = tableColumns.map((item) => {
        return item.entMethodItemId;
      });
      // Create a map from bidderId to bidderName
      const bidderMap = new Map(
        busiBidderInfos.map((bidder) => [bidder.bidderId, {
          bidderName: bidder.bidderName,
          isAbandonedBid: bidder.isAbandonedBid || 0 // 如果 isAbandonedBid 不存在，则默认为 0
        }])
      );

      // Generate the result table
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderMap.get(supplierId);
        var result = true;
        const temp = entMethodItemIds.every((key) => {
          return row[key] == "1";
        });

        if (!temp) {
          result = false;
        }
        return {
          gys: bidderName,
          isAbandonedBid: isAbandonedBid,
          result: result,
        };
      });
    },
    getIconClass(value) {
      return value == "1" ? "el-icon-check" : "el-icon-circle-close";
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.three {
  padding: 60px 170px;
  position: relative;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #176adb;
  background-color: #f5f5f5;
  border: 0;
  font-weight: 700;
  &:hover {
    color: #176adb;
  }
}
.result {
  text-align: left;
  margin: 20px 0;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>
