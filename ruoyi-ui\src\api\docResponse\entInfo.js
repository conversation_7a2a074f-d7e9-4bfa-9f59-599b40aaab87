import request from '@/utils/request'


// 查询采购公告信息
export function getProcurementInfo(infoId) {
  return request({
    url: '/docResponseEnt/getProcurementInfo?infoId='+infoId,
    method: 'get'
  })
}

// 查询采购文件下载记录列表
export function entList(query) {
  return request({
    url: '/docResponseEnt/entList',
    method: 'get',
    params: query
  })
}
// 查询采购文件下载记录列表
export function entInfo(projectId) {
  return request({
    url: '/docResponseEnt/entInfo?projectId='+projectId,
    method: 'get'
  })
}

// 查询采购文件下载记录列表
export function getById(infoId) {
  return request({
    url: '/docResponseEnt/getById?infoId='+infoId,
    method: 'get'
  })
}

// 查询采购文件下载记录列表
export function saveInfo(data) {
  return request({
    url: '/docResponseEnt/saveInfo',
    method: 'post',
    params: data
  })
}

// 查询采购文件下载记录列表
export function saveDetail(data) {
  return request({
    url: '/docResponseEnt/saveDetail',
    method: 'post',
    data: data
  })
}

// 查询采购文件下载记录列表
export function delDetail(detailId) {
  return request({
    url: '/docResponseEnt/delDetail?detailId='+detailId,
    method: 'get'
  })
}

// 删除中小企业声明函货物内容
export function delZxqysmhDetail(detailId, detailQyId) {
  return request({
    url: '/docResponseEnt/delZxqysmhDetail?detailId='+detailId+"&detailQyId="+detailQyId,
    method: 'get'
  })
}

// 创建响应文件
export function createDocResponse(data) {
  return request({
    url: '/docResponseEnt/createDocResponse',
    method: 'post',
    data: data
  })
}

export function systemVerification(data) {
  return request({
    url: '/docResponseEnt/systemVerification',
    method: 'post',
    data: data
  })
}

// 创建货物类响应文件
export function createGoodsDocResponse(data) {
  return request({
    url: '/docResponseEnt/createDocResponse',
    method: 'post',
    data: data
  })
}

// 创建服务类响应文件
export function createServiceDocResponse(data) {
  return request({
    url: '/docResponseEnt/createDocResponse',
    method: 'post',
    data: data
  })
}

// 创建询价货物类响应文件
export function createInquiryGoodsDocResponse(data) {
  return request({
    url: '/docResponseEnt/createDocResponse',
    method: 'post',
    data: data
  })
}

// 查询采购文件下载记录列表
export function createSecurityDocResponse(infoId) {
  return request({
    url: '/docResponseEnt/createSecurityDocResponse?infoId='+infoId,
    method: 'get'
  })
}

// 查询采购文件下载记录列表
export function getPesponseDocPageByProject(projectId) {
  return request({
    url: '/docResponseEnt/getPesponseDocPageByProject?projectId='+projectId,
    method: 'get'
  })
}

// 响应文件评审因素校验
export function resDocReviewFactorsDecision(data) {
  return request({
    url: '/docResponseEnt/resDocReviewFactorsDecision',
    method: 'post',
    data: data
  })
}