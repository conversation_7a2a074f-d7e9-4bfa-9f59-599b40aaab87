<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="再次报价id" prop="againQuoteId">
        <el-input
          v-model="queryParams.againQuoteId"
          placeholder="请输入再次报价id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目评审信息id" prop="projectEvaluationId">
        <el-input
          v-model="queryParams.projectEvaluationId"
          placeholder="请输入项目评审信息id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="企业id" prop="entId">
        <el-input
          v-model="queryParams.entId"
          placeholder="请输入企业id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报价次数" prop="quoteNumber">
        <el-input
          v-model="queryParams.quoteNumber"
          placeholder="请输入报价次数"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="报价金额" prop="quoteAmount">
        <el-input
          v-model="queryParams.quoteAmount"
          placeholder="请输入报价金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除标记，0正常 1删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记，0正常 1删除" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['again:quote:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['again:quote:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['again:quote:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['again:quote:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="quoteList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="再次报价id" align="center" prop="againQuoteId" />
      <el-table-column label="项目评审信息id" align="center" prop="projectEvaluationId" />
      <el-table-column label="企业id" align="center" prop="entId" />
      <el-table-column label="报价次数" align="center" prop="quoteNumber" />
      <el-table-column label="报价金额" align="center" prop="quoteAmount" />
      <el-table-column label="报价文件" align="center" prop="quoteFile" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['again:quote:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['again:quote:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改二次报价对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="再次报价id" prop="againQuoteId">
          <el-input v-model="form.againQuoteId" placeholder="请输入再次报价id" />
        </el-form-item>
        <el-form-item label="项目评审信息id" prop="projectEvaluationId">
          <el-input v-model="form.projectEvaluationId" placeholder="请输入项目评审信息id" />
        </el-form-item>
        <el-form-item label="企业id" prop="entId">
          <el-input v-model="form.entId" placeholder="请输入企业id" />
        </el-form-item>
        <el-form-item label="报价次数" prop="quoteNumber">
          <el-input v-model="form.quoteNumber" placeholder="请输入报价次数" />
        </el-form-item>
        <el-form-item label="报价金额" prop="quoteAmount">
          <el-input v-model="form.quoteAmount" placeholder="请输入报价金额" />
        </el-form-item>
        <el-form-item label="报价文件" prop="quoteFile">
          <file-upload v-model="form.quoteFile"/>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
        <el-form-item label="删除标记，0正常 1删除" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuote, getQuote, delQuote, addQuote, updateQuote } from "@/api/again/quote";

export default {
  name: "Quote",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 二次报价表格数据
      quoteList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记，0正常 1删除时间范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        againQuoteId: null,
        projectEvaluationId: null,
        entId: null,
        quoteNumber: null,
        quoteAmount: null,
        quoteFile: null,
        createTime: null,
        createBy: null,
        remark: null,
        delFlag: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询二次报价列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listQuote(this.queryParams).then(response => {
        this.quoteList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        againQuoteId: null,
        projectEvaluationId: null,
        entId: null,
        quoteNumber: null,
        quoteAmount: null,
        quoteFile: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        remark: null,
        delFlag: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.againQuoteId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加二次报价";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const againQuoteId = row.againQuoteId || this.ids
      getQuote(againQuoteId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改二次报价";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.againQuoteId != null) {
            updateQuote(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQuote(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const againQuoteIds = row.againQuoteId || this.ids;
      this.$modal.confirm('是否确认删除二次报价编号为"' + againQuoteIds + '"的数据项？').then(function() {
        return delQuote(againQuoteIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('again/quote/export', {
        ...this.queryParams
      }, `quote_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
