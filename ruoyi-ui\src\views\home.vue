<template>
  <div>

    <Head></Head>
    <div class="overAll">
      <div style="height: 470px; width: 100%">
        <div class="login">
          <div class="login-top">
            <div class="avatar">
              <el-image
                style="width: 106px; height: 106px"
                :src="avatar"
                fit="fill"
              ></el-image>
            </div>
            <div
              class="operation"
              v-if="userInfo.token"
            >
              <div>
                <el-button
                  class="button"
                  style="background: #176adb; color: #fff; margin-bottom: 15px"
                  @click="goIndex()"
                >后台管理</el-button>
              </div>
            </div>
            <div
              class="operation"
              v-else
            >
              <div>
                <el-button
                  class="button"
                  style="background: #176adb; color: #fff; margin-bottom: 15px"
                  @click="goLoginIn()"
                >登录</el-button>
              </div>
              <div>
                <el-button
                  class="button"
                  @click="goSignIn()"
                >注册</el-button>
              </div>
            </div>
            <div class="slogan">
              <el-image
                style="width: 100%; height: 100%"
                :src="slogan"
                fit="fill"
              ></el-image>
            </div>
          </div>
        </div>
        <el-carousel
          :interval="5000"
          arrow="always"
          height="470px"
        >
          <el-carousel-item
            v-for="(item, index) in urls"
            :key="index"
          >
            <el-image
              :src="item"
              style="width: 100%; height: 100%"
            ></el-image>
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="app-container home">
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="24"
          >
            <el-card
              shadow="never"
              class="noticeAnnouncement"
            >
              <div class="announcement">
                <div class="info">
                  <div class="title"><span>通知公告：</span></div>
                  <div class="type">
                    <div class="label">平台通知</div>
                    <div class="content">{{ noticeAnnouncement.name }}</div>
                  </div>
                </div>

                <el-button
                  type="text"
                  class="more"
                  style="color: #666666"
                  @click="gotoList('notice')"
                >更多></el-button>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="24"
          >
            <el-card
              shadow="never"
              class="haveSignedUp"
            >
              <div class="date">
                <div
                  v-for="item in haveSignedUp"
                  class="main"
                  :key="item.intentionId"
                  @click="viewContent(item)"
                >
                  <div class="content">
                    <div class="title">{{ item.title }}</div>
                    <div class="price">
                      <div class="one">¥{{ item.price }}</div>
                      <el-tooltip
                        :content="item.serveType"
                        :disabled="isShowTooltip"
                        effect="dark"
                        placement="top-end"
                      >
                        <div
                          class="two"
                          @mouseover="onMouseOver(item.serveType)"
                          style="overflow: hidden;  text-overflow: ellipsis; white-space: nowrap;"
                        >
                          <span ref="str">{{ `${item.serveType}`}}</span>
                        </div>
                      </el-tooltip>
                    </div>
                    <div class="info">
                      <div style="padding-bottom: 7px">
                        <span>发布时间 </span>
                        <span style="color: #666666">{{ item.releaseTime }}
                        </span>
                      </div>
                      <div>
                        <span>采购单位 </span>
                        <span style="color: #666666">{{
                          item.purchasingUnit
                        }}</span>
                      </div>
                    </div>
                  </div>
                  <div class="time">
                    <el-row>
                      <el-col
                        :sm="24"
                        :lg="6"
                      >
                        <div class="label">{{ item.status }}</div>
                      </el-col>
                      <el-col
                        :sm="24"
                        :lg="18"
                      >
                        <div class="openBid">
                          开标时间
                          <div class="word">{{ item.year }}</div>
                          年
                          <div class="word">{{ item.month }}</div>
                          月
                          <div class="word">{{ item.day }}</div>
                          日
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="24"
          >
            <el-card
              shadow="never"
              class="informationPublicity"
            >
              <div
                slot="header"
                class="announcement"
              >
                <div class="info">
                  <div class="clearfix">采购信息</div>
                  <div class="type">
                    <div
                      class="select"
                      :class="{ active: activeName === '采购公告' }"
                      @click="toggleActive('20')"
                    >
                      采购公告
                    </div>
                    <!-- <div
                      class="select"
                      :class="{ active: activeName === '变更公告' }"
                      @click="toggleActive('21')"
                    >
                      变更公告
                    </div> -->
                    <div
                      class="select"
                      :class="{ active: activeName === '结果公告' }"
                      @click="toggleActive('60')"
                    >
                      结果公告
                    </div>
                    <div
                      class="select"
                      :class="{ active: activeName === '取消公告' }"
                      @click="toggleActive('-2')"
                    >
                      取消公告
                    </div>
                  </div>
                </div>

                <el-button
                  type="text"
                  class="more"
                  @click="gotoList('publicity')"
                >更多></el-button>
              </div>
              <el-table
                :data="informationPublicity"
                style="width: 100%"
                :row-class-name="tableRowClassName"
                :cell-style="{ 'text-align': 'center' }"
                :header-cell-style="{
                  background: '#176ADB',
                  color: '#FFFFFF',
                  'text-align': 'center',
                }"
              >
                <el-table-column label="公告名称">
                  <template slot-scope="scope">
                    <div class="name">
                      <router-link
                        :to="todoPage(scope.row)"
                        class="link-type row-name"
                      >
                        <span>{{ scope.row.name }}</span>
                      </router-link>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="tendererName"
                  label="采购单位"
                >
                </el-table-column>
                <el-table-column
                  prop="tenderModeName"
                  label="采购方式"
                >
                </el-table-column>
                <el-table-column
                  prop="releaseTime"
                  label="发布时间"
                  width="150"
                >
                  <template slot-scope="scope">
                    <div class="name">
                      <span>{{ parseTime(scope.row.releaseTime, "{y}-{m}-{d}") }}</span>
                    </div>
                  </template>
                </el-table-column>
              </el-table>

            </el-card>
          </el-col>
        </el-row>
        <el-row
          :gutter="20"
          class="el-row"
        >
          <el-col
            :sm="24"
            :lg="12"
          >
            <el-card
              shadow="never"
              class="todo-list"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>政策法规</span>
                <el-button
                  type="text"
                  class="more"
                  @click="gotoList('policies')"
                >更多></el-button>
              </div>
              <el-table
                :show-header="false"
                :data="policies"
                style="width: 100%"
              >
                <el-table-column
                  prop="name"
                  label="名字"
                >
                  <template slot-scope="scope">
                    <div class="name">
                      <div class="label">政策</div>
                      <router-link
                        :to="todoPage(scope.row, '298')"
                        class="link-type"
                      >
                        <span>{{ scope.row.name }}</span>
                      </router-link>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="date"
                  label="日期"
                  width="100"
                >
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
          <el-col
            :sm="24"
            :lg="12"
          >
            <el-card
              shadow="never"
              class="todo-list"
            >
              <div
                slot="header"
                class="clearfix"
              >
                <span>新闻通知</span>
                <el-button
                  type="text"
                  class="more"
                  @click="gotoList('news')"
                >更多></el-button>
              </div>
              <el-table
                :show-header="false"
                :data="journalism"
                style="width: 100%"
              >
                <el-table-column
                  prop="name"
                  label="名字"
                >
                  <template slot-scope="scope">
                    <div class="name">
                      <div class="label">新闻</div>
                      <router-link
                        :to="todoPage(scope.row, '299')"
                        class="link-type"
                      >
                        <span>{{ scope.row.name }}</span>
                      </router-link>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="date"
                  label="日期"
                  width="100"
                >
                </el-table-column>
              </el-table>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
    <Foot></Foot>
  </div>
</template>

<script>
import {
  portalNoticeList,
  notOpeninglist,
  portalNoticeInfo,
  portalNoticeProcess,
} from "@/api/portal/portal";
import { listIntention } from "@/api/tender/intention";
import { listProject } from "@/api/tender/project";
import { listNotice } from "@/api/tender/notice";
import { formatDateOption } from "@/utils/index";
export default {
  data() {
    return {
      isShowTooltip: true,
      active: "20",
      activeMap: {
        20: "采购公告",
        21: "变更公告",
        60: "结果公告",
        "-2": "取消公告",
      },
      loading: true,
      noticeAnnouncement: {
        name: "关于XXXXXXXXXXXXXXXX通知",
      },
      haveSignedUp: [],
      policies: [
        {
          id: 111,
          name: "政策法规AA",
          date: "2024-06-17",
          type: "1",
        },
      ],
      journalism: [
        {
          id: 111,
          name: "新闻通知A",
          date: "2024-06-17",
          type: "2",
        },
      ],

      activeName: "采购",
      informationPublicity: [],
      slogan: require("@/assets/images/slogan.png"),
      avatar:
        "https://cube.elemecdn.com/3/7c/********************************.png",
      urls: [require("@/assets/images/banner.png")],
      userInfo: {},
      isLoading: false,
    };
  },
  watch: {},
  created() {
    this.toggleActive("20");
    this.notOpeninglist();
    this.userInfo = this.$store.state.user;
  },
  mounted() {},
  methods: {
    handleMore(path) {
      this.$router.push(path);
    },
    todoPage(row, act) {
      // console.info("--------------todoPage")
      // console.info(act!=undefined?this.active:act)
      if (act != undefined && (act === "298" || act === "299")) {
        return {
          path: "/information2",
          query: { projectId: row.id, dataType: act },
        };
      } else {
        return {
          path: "/information",
          query: {
            projectId: row.projectId,
            dataType: act != undefined ? act : this.active,
          },
        };
      }
    },

    notOpeninglist() {
      //获取采购意向
      notOpeninglist({
        pageNum: 1,
        pageSize: 4,
        params: {
          toOpening: true,
          orderByAsc: "bid_opening_time",
        },
      }).then((result) => {
        this.haveSignedUp = result.rows.map((item) => {
          return {
            projectId: item.id,
            title: item.name,
            price: item.budgetAmount,
            serveType: item.projectIndustry,
            releaseTime: formatDateOption(item.releaseTime, "date"),
            purchasingUnit: item.tendererName,
            status: "未开标",
            year: new Date(`${item.bidOpeningTime}`).getFullYear(),
            month: new Date(`${item.bidOpeningTime}`).getMonth() + 1,
            day: new Date(`${item.bidOpeningTime}`).getDate(),
          };
        });
      });
    },

    onMouseOver(str) {
      // 内容超出，显示文字提示内容
      this.$nextTick(() => {
        const tag = this.$refs.str;
        const parentWidth = tag[0].parentNode.offsetWidth; // 获取元素父级可视宽度
        const contentWidth = tag[0].offsetWidth; // 获取元素可视宽度
        this.isShowTooltip = contentWidth <= parentWidth;
      });
    },
    viewContent(item) {
      this.$router.push(this.todoPage(item));
    },

    tableRowClassName({ row, rowIndex }) {
      if (rowIndex % 2 === 0) {
        return "even-row";
      } else {
        return "odd-row";
      }
    },
    toggleActive(type) {
      this.isLoading = true;
      this.active = type;
      this.activeName = this.activeMap[type];
      portalNoticeList({
        pageNum: 1,
        pageSize: 10,
        dataType: type,
      }).then((result) => {
        if (result.code == 200) {
          this.informationPublicity = result.rows;
        }
        this.isLoading = false;
      });
    },
    // 跳转到后台管理页面
    goIndex() {
      this.$router.push({ path: "/" });
    },
    // 跳转到登录页
    goLoginIn() {
      window.location.href = process.env.VUE_APP_ZCXH_URL;

      // this.$router.push("/login");
    },
    // 跳转到注册页
    goSignIn() {
      window.location.href = process.env.VUE_APP_ZCXH_URL;

      // this.$router.push("/register");
    },
    // 跳转到列表页
    gotoList(value) {
      this.$router.push({
        path: "/list",
        query: {
          result: value,
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.overAll {
  position: relative;

  background-color: #f5f5f5;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-content: flex-start;
  align-items: flex-start;
  .login {
    position: absolute;
    z-index: 5;
    float: right;
    width: 300px;
    height: 470px;
    background: #ffffff;

    padding: 41px 33px 42px 33px;
    right: 16%;
    .login-top {
      width: 100%;
      .avatar {
        display: flex;
        justify-content: center;
        padding-bottom: 40px;
      }
      .operation {
        padding-bottom: 52px;
        .button {
          width: 100%;
          height: 43px;
          border: 1px solid #176adb;
        }
      }
    }

    .slogan {
      padding: 0 15px;
    }
  }
}
.home {
  background-color: #f5f5f5;
  width: 70%;
  .el-row {
    margin-bottom: 20px;
  }
  .noticeAnnouncement {
    .announcement {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      .info {
        display: flex;
        .title {
          width: 85px;
          font-family: SourceHanSansSC-Regular;
          font-weight: 400;
          font-size: 17px;
          color: #333333;
          letter-spacing: 0;
          white-space: nowrap;
          margin: 0;
        }
        .type {
          display: flex;
          flex-direction: row;
          flex-wrap: nowrap;
          justify-content: flex-start;
          align-items: center;
          .label {
            width: 77px;
            height: 25px;
            border: 1px solid #176adb;
            border-radius: 5px;

            text-align: center;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 13px;
            color: #176adb;
            letter-spacing: 0;
            line-height: 25px;
          }
          .content {
            margin-left: 15px;
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 17px;
            color: #666666;
            letter-spacing: 0;
          }
        }
      }
    }
  }
  .todo-list {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
      height: 256px;
      overflow-y: auto;
    }

    ::v-deep .el-table .cell {
      .name {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;
        align-items: center;
        .label {
          min-width: 45px;
          height: 23px;
          background: #4694ff;
          border-radius: 3px;
          color: #ffffff;
          margin-right: 15px;
          text-align: center;
        }
        .row-name {
          width: 300px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2; /* 控制显示的行数 */
          overflow: hidden;
        }
      }
    }
  }
  .haveSignedUp {
    .date {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      align-content: center;
      justify-content: flex-start;
      gap: 1vw;
      width: 100%;
      align-items: center;
      .main {
        flex: none; /* 均匀分配剩余空间 */
        min-width: 0; /* 允许内容溢出以适应空间分配 */
        box-sizing: border-box; /* 确保边框和填充不会增加元素宽度 */
        width: 24%;
        height: 239px;
        background: #ffffff;
        border: 1px solid #176adb;
        position: relative;
        cursor: pointer; /* 小手指针 */
        transition: box-shadow 0.3s ease; /* 阴影变化的过渡效果 */
        .content {
          padding: 20px 22px 9px 22px;
          .title {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 2; /* 显示的行数 */
            overflow: hidden;
            text-overflow: ellipsis;

            font-family: SourceHanSansSC-Heavy;
            font-weight: 900;
            font-size: 17px;
            color: #333333;
            letter-spacing: 0.7px;
            margin-bottom: 16px;
          }
          .price {
            display: flex;
            padding: 16px 0 24px 0;
            align-items: center;
            justify-content: space-between;
            align-content: center;
            flex-wrap: nowrap;
            flex-direction: row;
            .one {
              font-family: SourceHanSansSC-Heavy;
              font-weight: 900;
              font-size: 17px;
              color: #d91d18;
              letter-spacing: 1.6px;
            }
            .two {
              max-width: 100px;
              min-width: 86px;
              height: 20px;
              background: #176adb;
              border-radius: 3px;
              padding: 0 5px;

              font-family: SourceHanSansSC-Regular;
              font-weight: 400;
              font-size: 13px;
              color: #ffffff;
              letter-spacing: 0;
              text-align: center;
            }
          }
          .info {
            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #9a9a9a;
            letter-spacing: 0;
          }
        }
        .time {
          display: flex;
          //   height: 40px;
          justify-content: center;
          align-items: center;
          flex-wrap: nowrap;
          flex-direction: row;
          padding: 0 18px;
          .label {
            height: 30px;
            background: #176adb;
            padding: 0 10px;

            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 14px;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            white-space: nowrap;

            line-height: 30px;
          }
          .openBid {
            display: flex;
            align-items: center;
            flex-direction: row;
            flex-wrap: nowrap;
            justify-content: flex-start;

            height: 30px;
            background: #fafafa;
            padding: 5px;

            font-family: SourceHanSansSC-Regular;
            font-weight: 400;
            font-size: 12px;
            color: #0a0808;
            letter-spacing: 0;
            text-align: center;
            line-height: 30px;
            white-space: nowrap;
            .word {
              height: 27px;
              line-height: 21px;
              background: #176adb;
              padding: 3px;
              margin: 0 5px;

              font-family: SourceHanSansSC-Heavy;
              font-weight: 900;
              font-size: 12px;
              color: #ffffff;
              letter-spacing: 0;
            }
          }
        }
      }
      .main:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3); /* 鼠标悬浮时的阴影效果 */
      }
    }
  }
  .informationPublicity {
    ::v-deep .el-card__body {
      padding: 15px 0 20px 0;
    }
    ::v-deep .el-table .cell {
      .name {
        display: flex;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        .label {
          width: 45px;
          height: 23px;
          background: #4694ff;
          border-radius: 3px;
          color: #ffffff;
          margin-right: 15px;
        }
      }
    }
    .announcement {
      display: flex;
      justify-content: space-between;
      align-content: center;
      align-items: center;
      flex-wrap: nowrap;
      flex-direction: row;
      .info {
        display: flex;
        .type {
          display: flex;
          flex-wrap: wrap;
          align-content: center;
          justify-content: flex-start;
          align-items: center;
          margin-left: 31px;
          .select {
            width: 60px;
            height: 22px;
            font-family: SourceHanSansSC-Medium;
            font-weight: 500;
            font-size: 15px;
            color: #666666;
            letter-spacing: 0;

            margin-right: 26px;
            cursor: pointer;
          }
          .active {
            color: #f98319;
          }
        }
      }
    }
  }
  .clearfix {
    font-family: SourceHanSansSC-Bold;
    font-weight: 700;
    font-size: 22px;
    color: #333333;
    letter-spacing: 0;
  }
  .businessScope {
    text-align: justify;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 5; /* 显示的行数 */
    -webkit-box-orient: vertical;
  }
  .more {
    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #176adb;
    letter-spacing: 0;
    float: right;
  }
}
</style>
<style scoped lang="scss">
::v-deep .el-card__header {
  border-bottom: none;
}
::v-deep .el-table .even-row {
  background: #eff6ff;
}

::v-deep .el-table .odd-row {
  background: #ffffff;
}

::v-deep .el-timeline-item__tail {
  border-left: none;
  border-top: 2px solid #e4e7ed;
  width: 100%;
  position: absolute;
  top: 6px;
}
::v-deep .el-timeline-item__wrapper {
  padding-left: 0;
  position: absolute;
  top: 20px;
  transform: translateX(-50%);
  text-align: center;
}
::v-deep .el-timeline-item__timestamp {
  font-size: 14px;
}
.active {
  ::v-deep .el-timeline-item__node {
    background-color: #176adb;
  }
  ::v-deep .el-timeline-item__tail {
    border-color: #176adb;
  }
}
// 有active样式的下一个li
// .active + li {
//   ::v-deep .el-timeline-item__node {
//     background-color: #e4e7ed;
//   }
// }
</style>
