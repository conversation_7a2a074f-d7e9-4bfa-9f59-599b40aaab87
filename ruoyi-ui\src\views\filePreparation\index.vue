<template>
  <div>
    <!-- <el-header height="100px">采购文件制作</el-header> -->
    <el-main>
      <div class="item">
        <div class="item-title">项目信息:</div>
        <div class="item-content">{{ projectInfo.projectName }}</div>
      </div>
      <div class="item">
        <div class="item-title">项目编号:</div>
        <div class="item-content">{{ projectInfo.projectCode }}</div>
      </div>
      <div class="item">
        <div class="item-title">响应截止时间:</div>
        <div class="item-content">{{ projectInfo.deadLine }}</div>
      </div>
      <div class="item">
        <div class="item-title">采购方式:</div>
        <dict-tag
          :options="dict.type.busi_tender_mode"
          :value="projectInfo.procurementWey"
        />
      </div>
      <div class="item">
        <div class="item-title">采购人:</div>
        <div class="item-content">{{ projectInfo.purchasName }}</div>
      </div>
      <div class="item">
        <div><el-button
            @click="confirm"
            class="item-button"
            style="background-color:rgba(150, 255, 193, 1)"
          >确认</el-button></div>
        <div><el-button
            @click="cancle"
            class="item-button"
            style="background-color:rgba(236, 232, 197, 1)"
          >取消</el-button></div>
      </div>
    </el-main>
  </div>

</template>

<script>
import { getProject, initNewProjectUitem } from "@/api/tender/project";
import { saveInfo } from "@/api/documents/uinfo";

export default {
  name: "Page401",
  dicts: ["busi_tender_mode"],
  data() {
    return {
      projectInfo: {
        projectName: "",
        projectCode: "",
        deadLine: "",
        procurementWey: "",
        purchasName: "",
        projectType: "",
      },
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectByProjectId();
    },
    getProjectByProjectId() {
      // 获取项目信息
      getProject(this.$route.query.projectId).then((response) => {
        this.projectInfo = {
          projectName: response.data.projectName,
          projectCode: response.data.projectCode,
          deadLine: response.data.notice.noticeEndTime,
          procurementWey: response.data.tenderMode,
          purchasName: response.data.tendererName,
          projectType: response.data.projectType,
        };
      });
    },
    confirm() {
      // TODO 解决projectId关联问题后，打开initNewProjectUitem
      // initNewProjectUitem(this.$route.query.projectId).then((result)=>{
      //   if (result.code === 200) {
      //     this.$router.push({path: '/filePreparationInfo', query: { projectId: this.$route.query.projectId,noticeId:this.$route.query.noticeId }});
      //   }
      // })
      var info = { projectId: this.$route.query.projectId };
      console.info("info", info);
      saveInfo(info).then((result) => {
        if (result.code == 200) {
          this.$tab.closeOpenPage();
          if (
            this.projectInfo.projectType == "0" &&
            this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationInfo/index",
              query: {
                projectId: this.$route.query.projectId,
                noticeId: this.$route.query.noticeId,
              },
            });
          } else if (
            this.projectInfo.projectType == "1" &&
            this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationServiceInfo/index",
              query: {
                projectId: this.$route.query.projectId,
                noticeId: this.$route.query.noticeId,
              },
            });
          } else if (
            this.projectInfo.projectType == "2" &&
            this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationGoodsInfo/index",
              query: {
                projectId: this.$route.query.projectId,
                noticeId: this.$route.query.noticeId,
              },
            });
          }
        }
      });
    },
    cancle() {
      const obj = { path: `/tender/notice/add/${this.$route.query.noticeId}` };
      this.$tab.closeOpenPage(obj);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
  .item-content {
    min-width: 100px;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #333;
  }
}
</style>
