<template>
  <div>
    <!-- <el-header height="100px">采购文件制作</el-header> -->
    <el-container>
      <el-aside width="200px">
        <div style="min-height: 740px">
          <div class="title-button" :class="{ btnActive: step == '1' }" @click="active('1', 'basicInfo', '项目基本信息')" :autofocus="true" v-if="showAndHide('项目基本信息')">
            项目基本信息
          </div>
          <div class="title-button" :class="{ btnActive: step == '2' }" @click="active('2', 'bidInformation', '开标一览表')" v-if="showAndHide('开标一览表')">
            开标一览表
          </div>
          <div class="title-button" :class="{ btnActive: step == '3' }" @click="active('3', 'scoringMethod', '评分办法')" v-if="showAndHide('评分办法')">
            评分办法
          </div>
          <div class="title-button" :class="{ btnActive: step == '4' }" @click="active('4', 'requirement', '采购需求')">
            <!-- v-if="showAndHide('采购需求')" -->
            采购需求
          </div>
          <div class="title-button" :class="{ btnActive: step == '5' }" @click="active('5', 'biddingDocuments', '生成采购文件')">
            生成采购文件
          </div>
        </div>
      </el-aside>
      <el-main>
        <div v-if="step == '1'">
          <basicInfo ref="basicInfo" @saveSuccess="
              (uInfo) => updateInfoMap('项目基本信息', 'basicInfo', '1', uInfo)
            "></basicInfo>
        </div>
        <div v-if="step == '2'">
          <bidInformation ref="bidInformation" @saveSuccess="
              (uInfo) =>
                updateInfoMap('开标一览表', 'bidInformation', '2', uInfo)
            "></bidInformation>
        </div>
        <div v-if="step == '3'">
          <scoringMethod ref="scoringMethod" @saveSuccess="
              (uInfo,defName) => updateInfoMap('评分办法', 'scoringMethod', '3', uInfo,defName)
            "></scoringMethod>
        </div>
        <div v-if="step == '4'">
          <requirement ref="requirement" @saveSuccess="
              (uInfo) => updateInfoMap('采购需求', 'requirement', '4', uInfo)
            "></requirement>
        </div>
        <div v-if="step == '5'">
          <biddingDocuments ref="biddingDocuments" @saveSuccess="
              (uInfo) =>
                updateInfoMap('生成采购文件', 'biddingDocuments', '5', uInfo)
            "></biddingDocuments>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import basicInfo from "./components/basicInfo";
import bidInformation from "./components/bidInformation";
import biddingDocuments from "./components/biddingDocuments";
import scoringMethod from "./components/scoringMethod";
import requirement from "./components/requirement";
import { getProject } from "@/api/tender/project";
import { infoByParams } from "@/api/documents/info";
export default {
  components: {
    basicInfo,
    bidInformation,
    biddingDocuments,
    scoringMethod,
    requirement,
  },
  name: "Page401",
  data() {
    return {
      info: {},
      uinfo: {},
      itemMap: {},
      infoItems: [],
      projectInfo: {
        projectName: "",
        projectCode: "",
        deadLine: "",
        tenderMode: "",
        tendererName: "",
      },
      step: "1",
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    updateInfoMap(name, refName, stepNumber, uinfo , defName) {
      this.init(stepNumber, refName, name , defName);
    },
    showAndHide(val) {
      return this.infoItems.includes(val);
    },
    init(stepNumber='1', refName='basicInfo', name='项目基本信息',defName) {
     
      this.getProjectByProjectId()
        .then(() => {
          //渲染页面
          this.getPageStructure()
            .then(() => {
              //加载子页面
              this.$nextTick(() => {
                this.active(stepNumber, refName, name , defName);
              });
            })
            .catch((err) => { });
        })
        .catch((err) => { });
    },
    active(stepNumber, refName, name , defName) {
      this.step = stepNumber;
      //加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(
          this.projectInfo,
          this.itemMap[name],
          this.uinfo , 
          defName
        );
      });
    },
    getPageStructure() {
      let query = {
        tenderMode: this.projectInfo.tenderMode,
        projectType: this.projectInfo.projectType,
        projectId: this.$route.query.projectId,
        params: {
          projectId: this.$route.query.projectId,
          orderByDesc: "project_file_id",
          itemOrderByAsc: "project_file_item_id",
          returnUinfo: "true",
        },
      };
      return infoByParams(query).then((response) => {
        this.info = response.data;
        this.infoItems = response.data.items.map((item) => {
          this.itemMap[item.itemName] = item;
          return item.itemName;
        });
        this.uinfo = response.data.uinfo;
        if (this.uinfo && this.uinfo.uItems && this.uinfo.uItems.length > 0) {
          this.mergeUitemMap();
        }
      });
    },
    mergeUitemMap() {
      for (const key in this.itemMap) {
        let foundItem = this.uinfo.uItems.find(
          (item) =>
            item.projectFileItemId == this.itemMap[key].projectFileItemId
        );
        if (foundItem) {
          foundItem.itemName = key;
          this.itemMap[key] = foundItem;
        }
      }
    },
    getProjectByProjectId() {
      // 获取项目信息
      return getProject(this.$route.query.projectId).then((response) => {
        this.projectInfo = response.data;
        this.projectInfo.deadLine = response.data.notice.noticeEndTime;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: #409eff !important; /* 激活状态下的字体颜色 */
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;

  .title-button {
    font-size: 20px;
    color: #333;
    margin: 40px 0;
    cursor: pointer;
    &:hover {
      color: #409eff;
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.head {
  display: flex;
  justify-content: space-between;
}
</style>
