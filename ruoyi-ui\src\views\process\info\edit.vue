<template>
  <div style="margin-top:20px;">
      <el-col :span="24" class="card-box">
        <el-card v-if="!showOnly">
        <el-button type="primary" @click="submitForm">提交</el-button>
        <el-button type="primary" @click="initAttachment">初始化文件</el-button>
        <el-button @click="resetForm">重置</el-button>
      </el-card>
      </el-col>
      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>项目信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
                <tr>
                  <td colspan="4" class="el-table__cell is-leaf"><div class="cell cell-right-border" >采购项目</div></td>
                  <td colspan="20" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{formData.projectName}}</div></td>
                </tr>
                <tr>
                  <td colspan="4" class="el-table__cell is-leaf" ><div class="cell cell-right-border">项目编号</div></td>
                  <td colspan="20" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{formData.projectCode}}</div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>归档文件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody v-for="(item, index) in attachments" :key="index">
                <tr v-for="(file, fileIndex) in item.childrens" :key="fileIndex">
                  <td v-if="fileIndex==0" colspan="3" :rowspan="item.childrens.length" class="el-table__cell is-leaf cell-right-border"><div class="cell " >{{item.name}}</div></td>
                  <td colspan="3" class="el-table__cell is-leaf"><div class="cell cell-right-border" >{{file.name}}</div></td>
                  <td colspan="18" class="el-table__cell is-leaf"><div class="cell cell-right-border" >
                    <template>
                    <FileUpload
                            :value="file.strField1"
                            @input="handleInput(file.code, $event)"
                            @upload="fileUpload(file.code, $event)"
                            @remove="fileRemove(file.code, $event)"
                            :fileType="['pdf', 'doc', 'docx']"
                            :isShowTip="false"
                            :showOnly=showOnly
                      ></FileUpload>
                    </template>
                  </div></td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>


  </div>
</template>
<script>
import { listProject } from "@/api/tender/project";
import { addInfo as addAttachment,  delInfo as delAttachment} from "@/api/attachment/info";
import { listInfo, getInfo, delInfo, addInfo, updateInfo,selectProcessAttachment,getByProject } from "@/api/process/info";
import { getDicts } from "@/api/system/dict/data";
import { listData,listWithTreeData } from "@/api/tree/data";
export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      showOnly: false,
      projectId: '',
      formData: {
        projectId: '',
        attachmentMap: {},
      },
      attachments: [
        /*{
          "code":"01",
          "name":"采购意向",
          "childrens":[
            {"code":"0101","name":"附件11","strField1":"111.pdf,112.pdf"},
            {"code":"0102","name":"附件12","strField1":"121.pdf,122.pdf"}
          ]
        },
        {
          "code":"02",
          "name":"采购意向2",
          "childrens":[
            {"code":"0101","name":"附件21","strField1":"211.pdf,212.pdf"},
            {"code":"0102","name":"附件22","strField1":"221.pdf,222.pdf"}
          ]
        }*/
      ],
      rules: {
        projectId: [{
          required: true,
          message: '请选择采购项目',
          trigger: 'change'
        }]
      },
      projectIdOptionss: [],
      // 投标人表格数据
      tradderList: [],
    }
  },
  computed: {},
  watch: {},
  created() {
    if (this.$route.query.projectId) {
        this.projectId = parseInt(this.$route.query.projectId);
        this.getByProject();
      }else{
        var processId = this.$route.params.processId;
        this.getInfos(processId);
        this.selectAttachment(processId);
      }
    // this.getProjectList();
    // this.attachmentMap = {1:"/xxx.pdf", 2:"xxx.pdf"};
  },
  mounted() {},
  methods: {
    submitForm() {
      this.formData.busiState = 1;
      updateInfo(this.formData).then(result => {
        if(result.code==200){
          this.$modal.msgSuccess("提交成功");
          this.showOnly = true;
        }
      })
    },
    initAttachment(){
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    getInfos(processId){
      getInfo(processId).then((result) => {
        console.info(result);
        this.formData=result.data;
        if(this.formData.busiState==1){
          this.showOnly = true;
        }
      })
    },
    getByProject(){
      getByProject(this.projectId).then((result) => {
        console.info(result);
        this.formData=result.data;
        if(this.formData.busiState==1){
          this.showOnly = true;
        }
        this.selectAttachment(this.formData.processId);
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },
    handleInput(i, j){
      console.info("handleInput");
        console.info(i);
        console.info(j);
        // this.attachmentTypes[i] = j;
        // console.info(this.attachmentTypes);
        // this.attachmentMap[i] = j;
    },
    fileUpload(i, j){
      console.info("fileUpload");
        console.info(i);
        console.info(j);
        let lastIndex = j.name.lastIndexOf('.');
        let fileExtension = j.name.substring(lastIndex+1);
        var body = {busiId:this.formData.processId, fileName:j.name, fileType:i, fileSuffix: fileExtension,filePath:j.url}
        console.info(body)
        addAttachment(body).then(res => {
          if(res.code===200){
            this.$modal.msgSuccess("添加成功");
          }else{
            this.$modal.msgError("添加失败");
          }
        });
    },
    fileRemove(i, j){
      console.info("fileRemove");
        console.info(i);
        console.info(j);
    },
    getProjectList(){
      var this_ = this;
      console.info("getProjectList")
      listProject().then((result) => {
        if(result.code==200){
          this_.projectIdOptionss = result.rows;
          console.info(this_.projectIdOptionss);
        }
      });
    },
    getAttachmentTypes(){
      getDicts("busi_bid_opening_attachment").then((result) => {
        if(result.code==200){
          console.info(result);
          this.attachmentTypes = result.data;
        }
      });
    },
    changeProject(){
      console.info(this.formData.projectId);
      this.loading = true;
      getInfo({projectId:this.formData.projectId}).then(
        (result) => {
        if(result.code==200){
          console.info(result);
          this.tradderList = result.data;
          this.loading = false;
        }
        });
    },
    selectAttachment(processId){
      selectProcessAttachment(processId).then((result) =>{
        console.info(result)
        if(result.code==200){
          this.attachments = result.data;
        }
      })
    }
  }
}

</script>
<style>
/* .el-upload__tip {
  line-height: 1.2;
} */
.cell-right-border {
  border-right:1px solid #dfe6ec
}
</style>
<style scoped>
/deep/ .el-upload {
 float: right;
}
/deep/ .el-upload-list {
 width: 90%;
}
</style>
