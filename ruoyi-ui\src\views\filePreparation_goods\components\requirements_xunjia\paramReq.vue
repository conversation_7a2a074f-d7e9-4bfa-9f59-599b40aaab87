<template>
  <div>
    <el-container>
      <el-container>
        <el-main>
<!--
          <p class="text" style="text-indent:0">1、质保期：{{warrantyPeriod}} 年</p>
          <p class="text" style="text-indent:0">2、付款方式：{{projectInfo.notice.paymentMethod}}</p>
          <p class="text" style="text-indent:0">3、售后服务：</p>
          <p>
            <el-input type="textarea" :rows="20" placeholder="4.1质保期：免费质保3年（包含维修、保养及设备配套零配件在内的全部配件），供应商可以提出优于采购文件的质保年限。质保期外，根据采购人要求，供应商应当无条件进行调试维修，需更换配件的，按照市场优惠价格双方协商解决。
4.2服务内容承诺：终身免费提供设备维护保养、软件升级更新。
4.3服务体系：质保期内因设备本身缺陷造成各种故障应由卖方免费技术服务和维修，并在响应文件中说明在质保期内提供的服务计划；供应商应当免费提供设备的安装、调试及使用人员培训服务等。
4.4响应方式：供应商应当具有电话维修指导、远程线上维修指导及现场维修等。
4.5响应时间：设备正常使用期内如机器出现故障，在接到通知后15分钟内立即做出技术指导性响应，2小时内派工程技术人员到达现场维修（质保期满后也按此执行）。
4.6服务质量：供应商应当按照采购文件要求的内容及响应文件中承诺的内容提供相应的服务，并确保服务质量。
4.7质量保证体系：供应商所提供的设备应当为全新合格设备，在设备的正常使用周期内，供应商应当保证提供原厂的配品配件，以确保设备的正常使用。" v-model="requirement">
            </el-input>
          </p>
          <p class="text" style="text-indent:0"> 4、包装和运输 </p>
          <p class="text">商品包装及快递包装（如需要）同时应当符合《商品包装政府采购需求标准（试行）》及《快递包装政府采购需求标准（试行）》，必要时可要求中标供应商在履约验收环节出具检测报告。</p>
          <p class="text" style="text-indent:0">5、保险</p>

          <p class="text">采购标的的运输险、意外险等费用，标的供应方应当按照《中华人民共和国保险法》等相关法律法规的要求缴纳支付，保险费用应当计入标的物的成本。</p>
-->
          响应文件偏离表：<FileUpload v-model="requirement"  :is-show-tip="false"  :limit="1" :fileType="['xlsx']"></FileUpload>
          <div style="font-size:12px;color: #606266;"> <span style="color:red">请上传供投标人填写的响应文件偏离表模板。支持xlsx。</span> 
            <a href="/prod-api/profile/templates/tender_template/偏离表.xlsx" target="_blank" >响应文件偏离表模板</a> </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {
        notice: ""
      },
      itemInfo: {},
      uinfo: {},
      itemContent: {
        paramReq:{
          requirement:""
        },
        quantityParamReq:{
          requirement:""
        },
        substantiveReq:{
          requirement:""
        }
      },
      requirement: "",
      warrantyPeriod:"",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.itemContent = JSON.parse(itemInfo.itemContent);
        this.requirement = this.itemContent.paramReq.requirement;
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
      if(uinfo && uinfo.uItems){
        for(let uItem of uinfo.uItems){
          if(uItem.itemName==='开标一览表' && uItem.itemContent){
            let kbylb = JSON.parse(uItem.itemContent);
            console.log("kbylb", kbylb);

            for(let i of kbylb){
              console.log("i.code", i.code);
              if(i.code==='warrantyPeriod'){
                this.warrantyPeriod = i.defaultValue;
              }
            }
          }
        }
      }
    },
    saveUinfo() {
      this.itemContent.paramReq.requirement = this.requirement;
      let newContent = JSON.stringify(this.itemContent);
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newContent,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    //删除
    delete_btn() {
      this.requirement = "";
      this.saveUinfo();
    },
    handleInput(value) {
      this.requirement = value.url;
      if (this.requirement) {
        this.saveUinfo();
      }
    },
  },
  mounted() { },
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
.text {
  font-size: 16px;
  line-height: 100%;
  text-align: left;
  text-indent: 2em;
}
</style>
