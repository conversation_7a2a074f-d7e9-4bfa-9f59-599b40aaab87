<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="6" :xs="24">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>个人信息</span>
          </div>
          <div>
            <div class="text-center">
              <userAvatar />
            </div>
            <ul class="list-group list-group-striped">
              <li class="list-group-item">
                <svg-icon icon-class="user" />用户名称
                <div class="pull-right">{{ user.userName }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="phone" />手机号码
                <div class="pull-right">{{ user.phonenumber }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="email" />用户邮箱
                <div class="pull-right">{{ user.email }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="tree" />所属部门
                <div class="pull-right" v-if="user.dept">
                  {{ user.dept.deptName }} / {{ postGroup }}
                </div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="peoples" />所属角色
                <div class="pull-right">{{ roleGroup }}</div>
              </li>
              <li class="list-group-item">
                <svg-icon icon-class="date" />创建日期
                <div class="pull-right">{{ user.createTime }}</div>
              </li>
            </ul>
          </div>
        </el-card>
        <!-- <el-card
          class="box-card"
          v-if="show"
        >
          <div
            slot="header"
            class="clearfix"
          >
            <span>账号状态</span>
          </div>
          <div class="block">
            <el-timeline>
              <el-timeline-item
                v-for="(item,index) in operation"
                :key="index"
                :timestamp="item.timestamp"
                placement="top"
              >
                <el-card>
                  <p>{{ item.content }}</p>
                </el-card>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card> -->
        <el-card class="box-card" v-if="show">
          <div
            slot="header"
            class="clearfix"
            style="display: flex; justify-content: space-between"
          >
            <div><span>签章</span></div>
            <div>
              <el-button
                type="primary"
                size="mini"
                @click="reloadPage(ControlType.Seal)"
                >重置企业印章</el-button
              >
              <el-button
                type="primary"
                size="mini"
                @click="reloadPage(ControlType.legalPerson)"
                >重置法人印章</el-button
              >
            </div>
          </div>
          <div class="block">
            <div
              class="signer-list"
              style="display: flex; justify-content: center"
            >
              <ul>
                <div v-for="(element, index) in thisControlList" :key="index">
                  <li
                    class="unmover li-style"
                    style="text-align: center"
                    v-if="element.type == ControlType.Seal"
                  >
                    企业印章
                  </li>
                  <li
                    class="unmover li-style"
                    style="text-align: center"
                    v-if="element.type == ControlType.legalPerson"
                  >
                    法人印章
                  </li>
                  <!-- <li
                        class="unmover li-style"
                        v-if="element.type== ControlType.Signature"
                      >手写签名-{{element.user.userName?element.user.userName:'未手写签名'}}</li> -->
                  <li :class="['li-entp-seal']">
                    <div
                      class="entp-seal item"
                      v-if="element.type == ControlType.Seal"
                      @click="openModal(element)"
                    >
                      <img
                        :src="'data:image/png;base64,' + element.value"
                        v-if="element.value"
                      />
                      <span v-else>请先制作印章</span>
                    </div>
                    <div
                      class="entp-seal item"
                      v-if="element.type == ControlType.legalPerson"
                      @click="openModal(element)"
                    >
                      <img
                        :src="'data:image/png;base64,' + element.value"
                        v-if="element.value"
                      />
                      <span v-else>请先制作印章</span>
                    </div>
                    <!-- <div
                          class="person-seal item"
                          v-if="element.type == ControlType.Signature"
                          @click="openModal(element)"
                        >
                          <img
                            :src="'data:image/png;base64,'+element.value"
                            v-if="element.value"
                          />
                          <span v-else>请先设置手写签名</span>
                        </div> -->
                  </li>
                </div>
              </ul>
            </div>
            <Seal
              :seal-modal-show.sync="sealModalShow"
              @success="sealModalSubmit"
              :entId="form.entId"
              :type="type"
            ></Seal>
            <Signature
              :signature-modal-show.sync="signatureModalShow"
              @success="signatureModalSubmit"
            ></Signature>
          </div>
        </el-card>
      </el-col>
      <el-col :span="18" :xs="24">
        <el-card>
          <div slot="header" class="clearfix">
            <span>基本资料</span>
          </div>
          <el-tabs v-model="activeTab">
            <el-tab-pane label="企业信息" name="entInfo" v-if="show">
              <entInfo ref="entInfo" :user="form" />
            </el-tab-pane>
            <!-- <el-tab-pane
              label="基本资料"
              name="userinfo"
            >
              <userInfo :user="user" />
            </el-tab-pane> -->
            <el-tab-pane label="修改密码" name="resetPwd">
              <resetPwd />
            </el-tab-pane>
            <el-tab-pane label="签章信息" name="signAndPwd">
              <signAndPwd />
            </el-tab-pane>
          </el-tabs>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import entInfo from "./entInfo";
import signAndPwd from "./signAndPwd";
import { getUserProfile } from "@/api/system/user";
import { getInfo, updateInfo, updateAndAudit } from "@/api/ent/info";
import { listByBusi } from "@/api/audit/info";
import Seal from "@/components/Seal";
import Signature from "@/components/Signature";
import request from "@/utils/request";
import {
  CanvasZoom,
  controlList,
  ControlType,
} from "@/components/control/data/ControlData";

import "@/assets/styles/main.scss";
export default {
  name: "Profile",
  components: { userAvatar, userInfo, entInfo, resetPwd, signAndPwd, Seal, Signature },
  dicts: ["ent_economic_nature", "base_ent_status", "base_ent_type"],
  data() {
    return {
      user: {},
      roleGroup: {},
      postGroup: {},
      activeTab: "entInfo",
      show: false,
      operation: [
        {
          timestamp: "2018/4/1",
          content:
            "新注册账号，请完善信息后保存，等待管理员审核通过后可正常使用账号。",
        },
        {
          timestamp: "2018/4/3",
          content: "提交信息，待审核",
        },
        {
          timestamp: "2018/4/3",
          content: "管理员审核通过",
        },
      ],
      // 表单参数
      form: {},
      sealModalShow: false,
      signatureModalShow: false,
      thisControlList: JSON.parse(JSON.stringify(controlList)),
      //签署相关的属性
      signData: {
        signType: 1,
        entKeyword: "",
        personalKeyword: "",
        entName: "",
        legalName: "",
        personalName: "",
        entSeal: "",
        legalSeal: "",
        personalSeal: "",
        entPositionList: [],
        personalPositionList: [],
      },
      //文档展示的数据  包含控件列表
      documentPDF: {
        images: [
          {
            docPage: 0,
            image: "",
          },
          {
            docPage: 1,
            image: "",
          },
        ],
        control: [],
      },
      ControlType: ControlType,
      type: "",
    };
  },
  created() {
    this.getUser();
  },
  methods: {
    getAuditProcess() {
      listByBusi(this.form.entId).then((result) => {
        console.info("-----------getAuditProcess--------------");
        console.info(result);
        result.data.forEach((item, index) => {
          console.info(item);
          console.info(index);
          this.operation.push({
            timestamp: item.auditTime,
            content: item.auditResultName + "：" + item.auditRemark,
          });
        });
      });
    },
    getUser() {
      getUserProfile().then((response) => {
        this.user = response.data;
        this.roleGroup = response.roleGroup;
        this.postGroup = response.postGroup;
        if (!response.data.admin) {
          getInfo(response.data.entId).then((response) => {
            this.thisControlList.map((element) => {
              if (element.type == ControlType.Seal) {
                element.value = response.data.enterpriseSignature;
              } else if (element.type == ControlType.legalPerson) {
                element.value = response.data.legalRepreSignature;
              }
            });
            this.form = response.data;
            this.operation = [
              {
                timestamp: this.form.createTime,
                content: "账号注册成功，请完善信息并提交审核。",
              },
            ];
            this.$nextTick(() => {
              window.scrollTo(0, 0); // 确保页面滚动到顶部
            });
            this.getAuditProcess();
          });
          this.show = true;
        }
      });
    },

    reloadPage(type) {
      this.signData = {
        signType: 1,
        entKeyword: "",
        personalKeyword: "",
        entName: "",
        personalName: "",
        entSeal: "",
        personalSeal: "",
        entPositionList: [],
        personalPositionList: [],
      };
      if (type == ControlType.Seal) {
        this.thisControlList[0].value = "";
        this.thisControlList[0].user.userName = "";
        // this.$refs.entInfo.resetForm('enterpriseSignature','');
      this.resetSeal('enterpriseSignature');
      } else if (type == ControlType.legalPerson) {
        this.thisControlList[1].value = "";
        this.thisControlList[1].user.userName = "";
        // this.$refs.entInfo.resetForm('legalRepreSignature','');
        this.resetSeal('legalRepreSignature');
      }
      this.documentPDF.control = [];
    },
    resetSeal(type) {
      const response = request({
        url: "/kaifangqian/clip/resetSeal",
        method: "post",
        data: {
          entId: this.form.entId,
          type: type,
          opUser: this.form.entName
        },
      });
      console.log(response);
    },
    openModal(element) {
      if (element.type == this.ControlType.Seal && !element.value) {
        //signData.value.entPositionList.push(temItem)
        this.type = this.ControlType.Seal;
        this.sealModalShow = true;
      } else if (
        element.type == this.ControlType.legalPerson &&
        !element.value
      ) {
        this.type = this.ControlType.legalPerson;
        this.sealModalShow = true;
      } else if (element.type == this.ControlType.Signature && !element.value) {
        //signData.value.personalPositionList.push(temItem)
        this.signatureModalShow = true;
      }
    },
    /**
     * 接受签章图片
     */
    sealModalSubmit(data) {
      var temControlList = JSON.parse(JSON.stringify(this.thisControlList));
      if (data.type == ControlType.Seal) {
        temControlList[0].value = data.sealImage;
        temControlList[0].user.userName = data.entpName;
        this.signData.entName = data.entpName;
        this.signData.entSeal = data.sealImage;
      } else if (data.type == ControlType.legalPerson) {
        temControlList[1].value = data.sealImage;
        temControlList[1].user.userName = data.entpName;
        this.signData.legalName = data.entpName;
        this.signData.legalSeal = data.sealImage;
      }

      this.signData.entName = data.entpName;
      this.signData.entSeal = data.sealImage;
      this.thisControlList = temControlList;

      //替换控件中已经使用的签章
      this.documentPDF.control.forEach((item) => {
        if (item.type == this.ControlType.Seal) {
          item.value = data.sealImage;
        } else if (item.type == this.ControlType.legalPerson) {
          item.value = data.sealImage;
        }
      });
    },
    /**
     * 接收手写签名图片
     */
    signatureModalSubmit(data) {
      var temControlList = JSON.parse(JSON.stringify(this.thisControlList));
      temControlList[1].value = data.image;
      temControlList[1].user.userName = data.userName;
      this.signData.personalName = data.userName;
      this.signData.personalSeal = data.image;
      this.thisControlList = temControlList;
      //替换控件中已经使用的手写签名
      this.documentPDF.control.forEach((item) => {
        if (item.type == this.ControlType.Signature) {
          item.value = data.image;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.clearfix:after {
  content: none; /* 这将移除伪元素 */
}
</style>
