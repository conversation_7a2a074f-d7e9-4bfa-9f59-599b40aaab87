<!-- 聊天室 -->
<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="info">
      <div class="content">
        <div class="title">
          <div class="title-container">
            <div class="back-button" @click="goBack">
              <i class="el-icon-arrow-left"></i>
              <span>返回</span>
            </div>
            <div class="title-text">询标</div>
            <div class="placeholder"></div>
          </div>
        </div>
        <!--对话总体-->
        <div class="mess">

          <!--用户列表-->
          <div class="mess_user_list">
            <!--其他用户或群-->
            <div class="user_list">
              <div
                v-for="(item, index) in userList"
                :key="index"
                @click="showmessdlog(item,index)"
                class="user_list_item"
                :class="isActive == index ? 'user_list_item_active': ''"
              >
                <div>
                  <span>{{item.bidderName}}</span>
                </div>
              </div>
            </div>
          </div>
          <!--有对话时，对话框-->
          <div
            v-if="acceptUser !== ''"
            class="mess_dialog"
          >
            <!--对话框内容-->
            <div class="dlog_content">
              <div
                v-for="(item, index) in messnowList"
                :key="index"
                class="dlog_content_item"
                style="margin-left: 5px;"
              >
                <div style="margin: 5px 0;background-color:#f5f5f5">
                  <div class="content_other">
                    <div class="question">问题：</div>
                    <p style="font-family: SourceHanSansSC-Medium;font-weight: 500;font-size: 14px;color: #333333;letter-spacing: 0;margin: 0px;margin-top: 4px;">{{item.inquiringContent}}</p>
                  </div>
                  <div
                    class="content_me"
                    v-if="item.replyContent"
                  >
                    <div
                      class="question"
                      style="display:flex;justify-content: flex-end;align-items: center"
                    >回答：
                      <div class="file">
                        <el-link
                          :href="item.replyFile"
                          :underline="false"
                          target="_blank"
                        >查看文件</el-link>
                      </div>
                    </div>
                    <p style="font-family: SourceHanSansSC-Medium;font-weight: 500;font-size: 14px;color: #333333;letter-spacing: 0;margin: 0px;margin-top: 4px;">{{item.replyContent}}</p>
                  </div>
                </div>
              </div>
            </div>
            <!--对话框底部-->
            <div class="dlog_footer">
              <el-input
                style="margin-right:10px"
                v-model="mess"
              ></el-input>
              <el-button
                type="primary"
                @click="Wssendmess"
                style="float: right;margin-top: 5px;"
              >发送</el-button>
            </div>
          </div>
          <!--无对话时，对话框-->
          <div
            v-else
            class="mess_dialog_false"
          >
            <span>暂无消息，请选择用户对象</span>
          </div>
        </div>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
// const ws = new WebSocket("ws://localhost:8082?id=" + id);
import avatar from "@/assets/logo/avatar.png";
import { dataList } from "@/api/onlineBidOpening/info";
import { getHistoryMessage } from "@/api/evaluation/info";

export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  data() {
    //这里存放数据
    return {
      expertInfo: {},
      userAvatar: avatar,
      userList: [],
      isActive: -1,
      acceptUser: "",
      messnowList: [],
      mess: "",
      ws: null,
      baseUrl: process.env.VUE_APP_BASE_API,
      text_content: "",
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {
    messnowList: {
      handler() {
        this.$nextTick(() => {
          const dlogContent = this.$el.querySelector(".dlog_content");
          if (dlogContent) {
            dlogContent.scrollTop = dlogContent.scrollHeight;
          }
        });
      },
      deep: true,
    },
  },
  //方法集合

  methods: {
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    init() {
      // 获取专家信息
      this.expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      // 获取投标人信息
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.userList = response.data;
        }
      });
      this.join();
    },
    // 初始化聊天记录
    showmessdlog(item, index) {
      this.isActive = index;
      this.acceptUser = item;
      this.getMessage();
    },
    // 连接websocket
    join() {
      let socketUrl = this.baseUrl.replace("http", "ws");
      this.url = `${process.env.VUE_APP_WEBSOCKET_API}/websocket/message/${this.expertInfo.resultId}/${this.$route.query.projectId}/1`;
      const wsurl = this.url;
      this.ws = new WebSocket(wsurl);
      const self = this;
      // 心跳检测函数
      const ws_heartCheck = {
        timeout: 5000, // 5秒
        timeoutObj: null,
        serverTimeoutObj: null,
        start: function () {
          this.timeoutObj = setTimeout(() => {
            // 这里发送一个心跳包
            self.ws.send("ping");
            this.serverTimeoutObj = setTimeout(() => {
              self.ws.close(); // 如果超过一定时间还没重置，视为断开连接
            }, this.timeout);
          }, this.timeout);
        },
        reset: function () {
          clearTimeout(this.timeoutObj); // 在连接打开时启动心跳检测
          clearTimeout(this.serverTimeoutObj);
          this.start();
        },
      };
      this.ws.onopen = function (event) {
        ws_heartCheck.start();
        self.text_content = self.text_content + "已经打开连接!" + "\n";
        console.log(self.text_content);
      };
      this.ws.onmessage = function (event) {
        if (event.data != "ping" && event.data != "连接成功") {
          self.getMessage();
        }
        ws_heartCheck.reset(); // 收到消息后重置心跳检测
      };
      this.ws.onclose = function (event) {
        self.text_content = self.text_content + "已经关闭连接!" + "\n";
        console.log(self.text_content);
        clearTimeout(ws_heartCheck.timeoutObj);
        clearTimeout(ws_heartCheck.serverTimeoutObj);
      };
    },
    // 断开websocket连接
    exit() {
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
    },
    // 发送消息
    Wssendmess() {
      if (this.ws) {
        const evalProjectEvaluationProcess = JSON.parse(
          localStorage.getItem("evalProjectEvaluationProcess")
        );
        const message = {
          projectId: this.$route.query.projectId,
          projectEvaluationId: evalProjectEvaluationProcess.projectEvaluationId,
          entId: this.acceptUser.bidderId,
          expertResultId: this.expertInfo.resultId,
          message: this.mess,
          evalInquiringBidInfo: {
            projectEvaluationId:
              evalProjectEvaluationProcess.projectEvaluationId,
            entId: this.acceptUser.bidderId,
            expertResultId: this.expertInfo.resultId,
            inquiringContent: this.mess,
          },
        };

        this.ws.send(JSON.stringify(message));
        this.mess = "";
      } else {
        alert("未连接到服务器");
      }
    },
    // 获取历史消息
    getMessage() {
      const data = {
        projectId: this.$route.query.projectId,
        entId: this.acceptUser.bidderId,
        expertResultId: this.expertInfo.resultId,
      };
      getHistoryMessage(data).then((response) => {
        this.messnowList = response.data;
        console.log('getHistoryMessage',this.messnowList)
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() {},
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.init();
  },
  beforeCreate() {}, //生命周期 - 创建之前
  beforeMount() {}, //生命周期 - 挂载之前
  beforeUpdate() {}, //生命周期 - 更新之前
  updated() {}, //生命周期 - 更新之后
  beforeDestroy() {}, //生命周期 - 销毁之前
  destroyed() {}, //生命周期 - 销毁完成
  activated() {}, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 64vh;
  margin: 20px 0;
  padding: 0;
}
.title {
  border-radius: 5px 5px 0 0;
  background-clip: padding-box;
  margin: 0 auto;
  width: 100%;
  height: 60px;
  /* box-shadow: 0 0 10px #9b9393; */
  background-color: #176adb;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 20px;
}

.back-button {
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #ffffff;
  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 14px;
  transition: opacity 0.3s;
}

.back-button:hover {
  opacity: 0.8;
}

.back-button i {
  margin-right: 5px;
  font-size: 16px;
}

.title-text {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 18px;
  color: #ffffff;
  letter-spacing: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.placeholder {
  width: 80px; /* 与返回按钮宽度相同，保持平衡 */
}
.mess {
  border-radius: 5px;
  background-clip: padding-box;
  margin: 20px auto;
  margin-top: 0;
  width: 100%;
  height: 600px;
  /* box-shadow: 0 0 10px #9b9393; */
  background-color: white;
  display: flex;
}
.mess_user_list {
  width: 30%;
  height: 100%;
  background-color: #9f9c9c;
}
.mess_dialog {
  width: 70%;
  height: 600px;
  background-color: #fff;
  padding: 10px;
  padding-left: 0;
}
.mess_dialog_false {
  width: 70%;
  height: 600px;
  text-align: center;
  line-height: 600px;
  background-color: #fff;
  padding: 10px;
}
.dlog_content {
  width: 100%;
  height: 550px;
  padding: 30px;
  overflow-y: scroll;
  background-color: #f5f5f5;
}
.dlog_footer {
  width: 100%;
  height: 50px;
  display: flex;
  align-items: center;
}
.user {
  height: 60px;
  width: 270px;
  /*垂直居中*/
  display: flex;
  align-items: center;
  border-bottom: 1px solid #0a0a0a;
  background-color: #fff;
}
.user_list {
  height: 600px;
  /* overflow-y: scroll; */
  background-color: #fff;
  padding: 10px;
}
.user_list_item {
  height: 60px;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;

  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 17px;
  color: #333333;
  letter-spacing: 0;
}
.user_list_item_active {
  background-color: #176adb;
  color: #fff;
}
.content_other {
  width: 100%;
  margin-bottom: 20px;
}
.content_me {
  width: 100%;
  text-align: right;
  margin-bottom: 20px;
}
.question {
  font-family: SourceHanSansSC-Bold;
  font-weight: 700;
  font-size: 16px;
  color: #176adb;
  letter-spacing: 0;
}
.file {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;

  width: 68px;
  height: 30px;
  background: #176adb;
  border-radius: 2px;
}
::v-deep .el-link {
  font-family: SourceHanSansSC-Medium;
  font-weight: 500;
  font-size: 12px;
  color: #f5f5f5;
  letter-spacing: 0;
  text-align: center;
}
::v-deep .el-link:hover {
  color: #f5f5f5;
}
</style>
