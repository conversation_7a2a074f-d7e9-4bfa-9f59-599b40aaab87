<template>
  <el-form ref="entForm" :model="form" :rules="rules" label-width="140px">
    <el-row :gutter="20">
      <!-- <el-col
        :span="24"
        :xs="24"
      >
        <el-form-item
          label="企业LOGO"
          prop="entLogo"
        >
          <image-upload v-model="form.entLogo" />
        </el-form-item>
      </el-col> -->
      <el-col :span="24" :xs="24">
        <el-form-item label="营业执照照片" prop="businessLicense">
          <image-upload v-model="form.businessLicense" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="旧二级密码" prop="oldSecretKey">
          <el-input
            type="password"
            v-model="form.oldSecretKey"
            placeholder="二级密码用于标书加解密、签章等操作，新注册用户可不填"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="新二级密码" prop="newSecretKey">
          <el-input
            type="password"
            v-model="form.newSecretKey"
            placeholder="新二级密码，如不修改可不填"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="确认新二级密码" prop="newSecretKey2">
          <el-input
            type="password"
            v-model="form.newSecretKey2"
            placeholder="确认新二级密码"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="企业名称" prop="entName">
          <el-input v-model="form.entName" placeholder="请输入企业名称" />
        </el-form-item>
      </el-col>
      <el-col :span="24" :xs="24">
        <el-form-item label="统一社会信用代码" prop="entCode">
          <el-input
            v-model="form.entCode"
            placeholder="请输入统一社会信用代码"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="经济性质" prop="entNature">
          <el-select v-model="form.entNature" placeholder="请选择经济性质">
            <el-option
              v-for="dict in dict.type.ent_economic_nature"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="企业地址" prop="entAddress">
          <el-input v-model="form.entAddress" placeholder="请输入企业地址" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item
          label="重点代理领域"
          prop="entKeyAgencyAreas"
          v-if="form.entType === 2"
        >
          <el-input
            v-model="form.entKeyAgencyAreas"
            placeholder="请输入重点代理领域"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="开户行" prop="entOpeningBank">
          <el-input v-model="form.entOpeningBank" placeholder="请输入开户行" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="开户行户号" prop="entBankCode">
          <el-input v-model="form.entBankCode" placeholder="请输入开户行户号" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="联系人" prop="entLinkman">
          <el-input v-model="form.entLinkman" placeholder="请输入联系人" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="联系方式" prop="entContactPhone">
          <el-input
            v-model="form.entContactPhone"
            placeholder="请输入联系方式"
          />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="法定代表人" prop="entLegalPerson">
          <el-input v-model="form.entLegalPerson" placeholder="法定代表人" />
        </el-form-item>
      </el-col>
      <el-col :span="12" :xs="12">
        <el-form-item label="法定代表人联系方式" prop="entLegalPersonPhone">
          <el-input
            v-model="form.entLegalPersonPhone"
            placeholder="请输入法定代表人联系方式"
          />
        </el-form-item>
      </el-col>
      <el-col :span="24" :xs="24">
        <el-form-item label="法人身份证件" prop="entLegalPersonCardFile">
          <el-upload
            :file-list="fileList"
            class="upload-demo"
            :action="uploadUrl"
            :headers="headers"
            accept=".pdf"
            :on-preview="handlePreview"
            :on-success="
              (res, file) => handleSuccess(res, file, 'entLegalPersonCardFile')
            "
            :on-remove="
              (file, fileList) =>
                handleRemove(file, fileList, 'entLegalPersonCardFile')
            "
            :limit="1"
            :on-exceed="handleExceed"
          >
            <el-button type="primary">点击上传</el-button>
            <template #tip>
              <div class="el-upload__tip">
                请上传企业法人身份证件，文件格式为.pdf格式，大小不超过5M
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-col>
      <el-col :span="24" :xs="24">
        <el-form-item label="企业简介" prop="entIntro">
          <editor v-model="form.entIntro" :min-height="192" />
        </el-form-item>
      </el-col>
      <!-- <el-form-item
                label="企业状态"
                prop="entStatus"
              >
                <el-select
                  v-model="form.entStatus"
                  placeholder="请选择企业状态"
                >
                  <el-option
                    v-for="dict in dict.type.base_ent_status"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  ></el-option>
                </el-select>
              </el-form-item> -->
      <!-- <el-form-item
                label="企业类型"
                prop="entType"
              >
                <el-select
                  v-model="form.entType"
                  placeholder="请选择企业类型"
                >
                  <el-option
                    v-for="dict in dict.type.base_ent_type"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  ></el-option>
                </el-select>
              </el-form-item> -->
    </el-row>

    <el-form-item>
      <el-button type="primary" size="mini" @click="submitForm">保存</el-button>
      <el-button type="danger" size="mini" @click="close">关闭</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import { updateAndAudit } from "@/api/ent/info";
import { getToken } from "@/utils/auth";

export default {
  dicts: ["ent_economic_nature", "base_ent_status", "base_ent_type"],
  props: {
    user: {
      type: Object,
    },
  },
  data() {
    return {
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      uploadUrl: process.env.VUE_APP_BASE_API + "/common/upload", // 上传的图片服务器地址
      fileList: [],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entName: [
          { required: true, message: "企业名称不能为空", trigger: "blur" },
        ],
        entCode: [
          {
            required: true,
            message: "统一社会信用代码不能为空",
            trigger: "blur",
          },
        ],
        entAddress: [
          { required: true, message: "企业地址不能为空", trigger: "blur" },
        ],
        entKeyAgencyAreas: [
          { required: false, message: "重点代理领域不能为空", trigger: "blur" },
        ],
        entLinkman: [
          { required: true, message: "联系人不能为空", trigger: "blur" },
        ],
        entContactPhone: [
          { required: true, message: "联系方式不能为空", trigger: "blur" },
        ],
        entStatus: [
          { required: true, message: "企业状态不能为空", trigger: "change" },
        ],
        entType: [
          { required: true, message: "企业类型不能为空", trigger: "change" },
        ],
        entLegalPerson: [
          { required: true, message: "法定代表人不能为空", trigger: "blur" },
        ],
        entOpeningBank: [
          { required: true, message: "开户行不能为空", trigger: "blur" },
        ],
        entBankCode: [
          { required: true, message: "开户行户号不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    user: {
      handler(user) {
        console.log(123 ,user);
        if (user) {
          this.form = user;
          this.fileList = [];
          if(this.form.entLegalPersonCardFile){
            this.fileList = [{
              name: "法人身份证件",
              url: this.form.entLegalPersonCardFile,
            }];
            console.log(this.fileList);
          }
        }
      },
      immediate: true,
    },
  },
  methods: {
    // resetForm(key,value){
    //   this.form[key] = value;
    // },
    handleSuccess(res, file, key) {
      this.form[key] = res.url;
    },
    handlePreview(file) {
      console.log(file);
      if (file.url) {
        window.open(file.url);
      } else {
        window.open(file.response.url);
      }
    },
    handleRemove(file, fileList, key) {
      console.log(file, fileList);
      this.form[key] = "";
    },
    handleExceed(files, fileList) {
      this.$message.warning(`当前限制选择 1 个文件`);
    },
    submitForm() {
      console.info("------------------newSecretKey");
      let newSecretKey = this.form.newSecretKey;
      let newSecretKey2 = this.form.newSecretKey2;
      console.info(newSecretKey);
      if (
        newSecretKey != "" &&
        newSecretKey != undefined &&
        newSecretKey != newSecretKey2
      ) {
        this.$modal.msgError(`新二级密码与确认二级密码不一致!`);
        return;
      }
      this.form.secretKey = this.form.newSecretKey;
      this.$refs["entForm"].validate((valid) => {
        console.info(valid);
        if (!valid) return;
        delete this.form["enterpriseSignature"];
        delete this.form["legalRepreSignature"];
        updateAndAudit(this.form).then((response) => {
          if (this.form.busiState == 10) {
            this.$modal.msgSuccess("修改成功");
          } else {
            this.$modal.msgSuccess("提交成功，请等待管理员审核");
            form.busiState == 1;
          }
        });
      });
    },
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
