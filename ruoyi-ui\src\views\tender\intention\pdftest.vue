<template>
  <div style="height:700px;width:1000px;">
    <iframe ref="pdfIframe" :src="pdfUrl" width="100%" height="100%" frameborder="0" @load="onPdfLoad" @onclick="ttt"></iframe>
    <button @click="getPageNumbers">获取页码</button>
  </div>
</template>

<script>

export default {
  name: "add",
  props: {
    // 定义props
  },
  data() {
    return {
      pdfUrl:"/pdfjs/web/viewer.html?file=http://localhost/123.pdf",
      pdfDocument: null,
      pdfIframe:null,
    };
  },
  mounted() {
    window.addEventListener('message', this.handleMessage);
  },
  beforeDestroy() {
    window.removeEventListener('message', this.handleMessage);
  },
  methods: {
    onPdfLoad(event) {
      const iframeWindow = event.target.contentWindow;
      this.pdfIframe = iframeWindow;
      console.info("iframeWindow",iframeWindow)
      console.info("pdfjsLib",iframeWindow.pdfjsLib)

        iframeWindow.pdfjsLib.getDocument('http://localhost/123.pdf').promise.then((pdf) => {
          console.info("pdf",pdf)
          this.pdfDocument = pdf;
        });
    },
    getPageNumbers() {
      if (this.pdfDocument) {
        console.log('pdfIframe', this.pdfIframe);
        console.log('PDFViewerApplication', this.pdfIframe.PDFViewerApplication);
        console.log('pdfViewer', this.pdfIframe.PDFViewerApplication.pdfViewer);
        const pdfViewer = this.pdfIframe.PDFViewerApplication.pdfViewer;
        const numPages = pdfViewer.currentPageNumber;
        console.log('PDF页码：', numPages);
      } else {
        console.log('PDF文档尚未加载完成。');
      }
    },
    ttt(){
      alert(1)
    },
    handleMessage(event) {
      // 确保事件来自我们期望的源
      if (event.data.type === 'PDF_CLICKED') {
        console.log(`Page: ${event.data.page}, X:${event.data.x}, clientX: ${event.data.clientX}, left: ${event.data.left}, 
        Y: ${event.data.y}, clientY: ${event.data.clientY}, top: ${event.data.top}`);
        console.log(`e: ${event}`);
        // 在这里处理点击坐标
      }
    }
  },
};
</script>

<style scoped>

.el-form-item{
  margin-bottom: 0px;
}
/* 添加样式 */
</style>
