import request from "@/utils/request";

// 专家登录
export function expertLogin(data) {
  return request({
    url: "/expertLogin",
    method: "post",
    data: data,
  });
}
export function expertResultLogin(data) {
  return request({
    url: "/expert/result/login",
    method: "post",
    data: data,
  });
}


// 修改专家信息
export function editExpertInfo(data) {
  return request({
    url: "/LoginRequest/editZhuanJiaInfo",
    method: "post",
    data: data,
  });
}
// 获取登陆专家的所有项目
export function allProject(data) {
  return request({
    url: "/expert/result/getLoginUserProject",
    method: "post",
    data: data,
  });
}
// 查询供应商信息
export function supplierInfo(query) {
  return request({
    url: "/bidding/info/list",
    method: "get",
    params: query,
  });
}
// 获取专家开户行信息
export function expertAccout(Id) {
  return request({
    url: "/LoginRequest/zhuanJiaInfo/" + Id,
    method: "get",
  });
}
// 通过项目id找专家信息
export function expertInfoById(data) {
  return request({
    url: "/expert/result/getZhuanJiaByProjectId",
    method: "post",
    data: data,
  });
}
// 通过项目id查找采购文件信息
export function filesById(projectId) {
  return request({
    url: "/bidding/record/getProjectFileById/" + projectId,
    method: "get",
  });
}
// 审批流程展示
export function approvalProcess(projectId, resultId) {
  return request({
    url: "/method/uinfo/getInfoByProjectId/" + projectId + "/" + resultId,
    method: "get",
  });
}
// 供应商请求询价消息记录
export function inquiringBidList(data) {
  return request({
    url: "/bid/info/getHistoryMessage",
    method: "post",
    data: data,
  });
}


// 选择评审后插入评审项目状态
export function reviewStatus(data) {
  return request({
    url: "/evaluation/process",
    method: "post",
    data: data,
  });
}
// 汇总查询
export function summaryQuery(query) {
  return request({
    url: "/evaluation/detail/getEalExpertEvaluationDetail",
    method: "post",
    params: query,
  });
}
// 专家组长汇总查询
export function leaderSummaryQuery(query) {
  return request({
    url: "/evaluation/detail/getEalExpertEvaluationDetailToGroupLeader",
    method: "post",
    params: query,
  });
}
// 查询投标报价得分
export function quotationScore(data) {
  return request({
    url: "/again/quote/getEvalAgainQuote",
    method: "post",
    data: data,
  });
}
// 插入评分因素分值
export function scoringFactors(data) {
  return request({
    url: "/evaluation/detail",
    method: "post",
    data: data,
  });
}
// 评审汇总
export function reviewSummary(query) {
  return request({
    url: "/evaluation/detail/getReviewSummary",
    method: "post",
    params: query,
  });
}
// 获取专家当前item供应商是否全部提交
export function checkReviewSummary(data) {
  return request({
    url: "/evaluation/detail/getCheckExpertEvaluationDetail",
    method: "post",
    data: data,
  });
}
// 重新评审
export function reEvaluate(evaluationProcessId) {
  return request({
    url: "/evaluation/process/reEvaluate/" + evaluationProcessId,
    method: "get",
  });
}
