import { listWithTreeData,listData } from "@/api/tree/data";

/**
 * @param {Object} queryParam
 * @returns {Map}
 */
export async function getdataTreeOptionsMap(queryParam) {
  // 等待异步请求完成
  try {
    // 等待异步请求完成
    const response = await listWithTreeData(queryParam);
    
    // 使用reduce方法将数组转换为Map，以对象的type属性作为键
    const dataTreeOptionsMap = response.data.reduce((accumulator, current) => {
      accumulator.set(current.type, current);
      return accumulator;
    }, new Map());

    // 返回包含数据的Map对象
    return dataTreeOptionsMap;
  } catch (error) {
    // 如果发生错误，捕获并返回
    console.error('Error fetching tree data:', error);
    throw error;
  }
}

/**
 * @param {Object} queryParam
 * @returns {Map}
 */
export async function getdataTreeListMap(queryParam) {

  // 等待异步请求完成
  try {
    // 等待异步请求完成
    const response = await listData(queryParam);
    // 使用reduce方法将数组转换为Map，以对象的type属性作为键
    const dataTreeOptionsMap = response.rows.reduce((accumulator, current) => {
      accumulator.set(current.id, current.name);
      return accumulator;
    }, new Map());

    // 返回包含数据的Map对象
    return dataTreeOptionsMap;
  } catch (error) {
    // 如果发生错误，捕获并返回
    console.error('Error fetching tree data:', error);
    throw error;
  }
}