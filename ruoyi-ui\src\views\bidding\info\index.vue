<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="项目名称" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人名称" prop="bidderName">
        <el-input
          v-model="queryParams.bidderName"
          placeholder="请输入投标人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人代码" prop="bidderCode">
        <el-input
          v-model="queryParams.bidderCode"
          placeholder="请输入投标人代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="解密标记 0未解密 1解密成功 -1解密失败" prop="decodeFlag">
        <el-select v-model="queryParams.decodeFlag" placeholder="请选择解密标记 0未解密 1解密成功 -1解密失败" clearable>
          <el-option
            v-for="dict in dict.type.busi_bidding_decode_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="解密时间">
        <el-date-picker
          v-model="daterangeDecodeTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="投标金额" prop="bidderAmount">
        <el-input
          v-model="queryParams.bidderAmount"
          placeholder="请输入投标金额"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="排名" prop="ranking">
        <el-input
          v-model="queryParams.ranking"
          placeholder="请输入排名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="得分" prop="score">
        <el-input
          v-model="queryParams.score"
          placeholder="请输入得分"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否中标 0未中标 1中标" prop="isWin">
        <el-select v-model="queryParams.isWin" placeholder="请选择是否中标 0未中标 1中标" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="删除标记 0正常 1删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记 0正常 1删除" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间">
        <el-date-picker
          v-model="daterangeUpdateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['bidding:info:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['bidding:info:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['bidding:info:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['bidding:info:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="infoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="投标人id" align="center" prop="bidderId" />
      <el-table-column label="项目名称" align="center" prop="projectId" />
      <el-table-column label="投标人名称" align="center" prop="bidderName" />
      <el-table-column label="投标人代码" align="center" prop="bidderCode" />
      <el-table-column label="解密标记 0未解密 1解密成功 -1解密失败" align="center" prop="decodeFlag">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_bidding_decode_flag" :value="scope.row.decodeFlag"/>
        </template>
      </el-table-column>
      <el-table-column label="解密时间" align="center" prop="decodeTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.decodeTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="投标金额" align="center" prop="bidderAmount" />
      <el-table-column label="排名" align="center" prop="ranking" />
      <el-table-column label="得分" align="center" prop="score" />
      <el-table-column label="是否中标 0未中标 1中标" align="center" prop="isWin">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_yes_no" :value="scope.row.isWin"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['bidding:info:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['bidding:info:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改参与投标人信息对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目名称" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目名称" />
        </el-form-item>
        <el-form-item label="投标人名称" prop="bidderName">
          <el-input v-model="form.bidderName" placeholder="请输入投标人名称" />
        </el-form-item>
        <el-form-item label="投标人代码" prop="bidderCode">
          <el-input v-model="form.bidderCode" placeholder="请输入投标人代码" />
        </el-form-item>
        <el-form-item label="解密标记 0未解密 1解密成功 -1解密失败" prop="decodeFlag">
          <el-select v-model="form.decodeFlag" placeholder="请选择解密标记 0未解密 1解密成功 -1解密失败">
            <el-option
              v-for="dict in dict.type.busi_bidding_decode_flag"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="解密时间" prop="decodeTime">
          <el-date-picker clearable
            v-model="form.decodeTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择解密时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="投标金额" prop="bidderAmount">
          <el-input v-model="form.bidderAmount" placeholder="请输入投标金额" />
        </el-form-item>
        <el-form-item label="排名" prop="ranking">
          <el-input v-model="form.ranking" placeholder="请输入排名" />
        </el-form-item>
        <el-form-item label="得分" prop="score">
          <el-input v-model="form.score" placeholder="请输入得分" />
        </el-form-item>
        <el-form-item label="是否中标 0未中标 1中标" prop="isWin">
          <el-select v-model="form.isWin" placeholder="请选择是否中标 0未中标 1中标">
            <el-option
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="删除标记 0正常 1删除" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInfo, getInfo, delInfo, addInfo, updateInfo } from "@/api/bidding/info";

export default {
  name: "Info",
  dicts: ['busi_bidding_decode_flag', 'base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 参与投标人信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 0正常 1删除时间范围
      daterangeDecodeTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCreateTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        bidderName: null,
        bidderCode: null,
        decodeFlag: null,
        decodeTime: null,
        bidderAmount: null,
        ranking: null,
        score: null,
        isWin: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目名称不能为空", trigger: "blur" }
        ],
        bidderName: [
          { required: true, message: "投标人名称不能为空", trigger: "blur" }
        ],
        bidderCode: [
          { required: true, message: "投标人代码不能为空", trigger: "blur" }
        ],
        decodeFlag: [
          { required: true, message: "解密标记 0未解密 1解密成功 -1解密失败不能为空", trigger: "change" }
        ],
        bidderAmount: [
          { required: true, message: "投标金额不能为空", trigger: "blur" }
        ],
        ranking: [
          { required: true, message: "排名不能为空", trigger: "blur" }
        ],
        score: [
          { required: true, message: "得分不能为空", trigger: "blur" }
        ],
        isWin: [
          { required: true, message: "是否中标 0未中标 1中标不能为空", trigger: "change" }
        ],
        delFlag: [
          { required: true, message: "删除标记 0正常 1删除不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询参与投标人信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeDecodeTime && '' != this.daterangeDecodeTime) {
        this.queryParams.params["beginDecodeTime"] = this.daterangeDecodeTime[0];
        this.queryParams.params["endDecodeTime"] = this.daterangeDecodeTime[1];
      }
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listInfo(this.queryParams).then(response => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        bidderId: null,
        projectId: null,
        bidderName: null,
        bidderCode: null,
        decodeFlag: null,
        decodeTime: null,
        bidderAmount: null,
        ranking: null,
        score: null,
        isWin: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeDecodeTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bidderId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加参与投标人信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const bidderId = row.bidderId || this.ids
      getInfo(bidderId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改参与投标人信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.bidderId != null) {
            updateInfo(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bidderIds = row.bidderId || this.ids;
      this.$modal.confirm('是否确认删除参与投标人信息编号为"' + bidderIds + '"的数据项？').then(function() {
        return delInfo(bidderIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('bidding/info/export', {
        ...this.queryParams
      }, `info_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
