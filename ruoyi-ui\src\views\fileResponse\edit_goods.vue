<template>
  <div style="margin-top:20px;" v-loading="loading">
    <el-button style="float: right; margin-right: 30px;" type="primary" @click="createSecurityDocResponse">3.生成加密文件</el-button>
    <el-button style="float: right; margin-right: 30px;" type="primary" @click="fileSign">2.响应文件签章</el-button>
    <el-button style="float: right; margin-right: 30px;" type="primary" @click="createGoodsDocResponse">1.生成响应文件</el-button>
    <el-col :span="24" class="card-box">

      <div style="margin-top:10px;font-size:20px;font-weight:bold">项目基本信息</div><el-divider></el-divider>
      <div class="el-table el-table--enable-row-hover el-table--medium">
        <table cellspacing="0" style="width: 100%;table-layout:fixed;">
          <tbody>
          <tr>
            <td colspan="4" class="el-table__cell is-leaf">
              <div class="cell cell-right-border">采购项目</div>
            </td>
            <td colspan="20" class="el-table__cell is-leaf">
              <div class="cell cell-right-border">{{ projectInfo.projectName }}</div>
            </td>
          </tr>
          <tr>
            <td colspan="4" class="el-table__cell is-leaf">
              <div class="cell cell-right-border">项目编号</div>
            </td>
            <td colspan="20" class="el-table__cell is-leaf">
              <div class="cell cell-right-border">{{ projectInfo.projectCode }}</div>
            </td>
          </tr>
          </tbody>
        </table>
      </div>
    </el-col>

    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">响应函及开标一览表</div><el-divider></el-divider>
      <el-card class="box-card">
        <div class="text">
          <span style="color:red">*</span>
          开标一览表
          <el-button style="float: right; padding: 3px 0" type="text" @click="kbylbDialog">在线编辑</el-button>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium" v-if="kbylbData.id">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  投标报价（大写）
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.tbbjdx }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  投标报价（小写）
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.bidPrice }} 元
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  质量要求
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.qualityDemand }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  供货期
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.overTimeLimit }} 日历日
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  质保期
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.warrantyPeriod }} 年
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  投标有效期
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ kbylbData.tbyxq }} 日历日
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <el-divider></el-divider>
        <div class="text">
          <span style="color:red">*</span>
          明细报价表
          <el-button style="float: right; padding: 3px 0" type="text" @click="fileDialogOpen('mxbjb','明细报价表',docResponseData.mxbjb[0],true)">
            {{docResponseData.mxbjb.length <= 0?'上传':'修改'}}</el-button>
        </div>
        <!-- <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
              <tr v-for="(detail, index) in docResponseData.mxbjb" :key="index">
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    {{ detail.detailName }}
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="fileDialogOpen('mxbjb','明细报价表',detail)">修改</el-button>
                    <el-button type="text" @click="removeFileDetail(detail, index, '明细报价表')">删除</el-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div> -->
      </el-card>
    </el-col>

    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">法定代表人身份证明或法定代表人授权书</div><el-divider></el-divider>
      <el-card class="box-card">
        <div>
          <el-radio v-model="frorsqs" label="fr">法定代表人身份证明</el-radio>
          <el-radio v-model="frorsqs" label="sqs">法定代表人授权书</el-radio>
        </div>
        <el-divider></el-divider>
        <div v-if="frorsqs == 'fr'">
          <div class="text">
            <span style="color:red">*</span>
            法定代表人身份证明
            <el-button style="float: right; padding: 3px 0" type="text" @click="fddbrsfzmDialog">在线编辑</el-button>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium" v-if="fddbrsfzmData.id">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    法定代表人姓名
                  </div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    {{ fddbrsfzmData.fddbrxm }}
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>

        <div v-if="frorsqs == 'sqs'">
          <div class="text">
            <span style="color:red">*</span>
            法定代表人授权书
            <el-button style="float: right; padding: 3px 0" type="text" @click="fddbrsqsDialog">在线编辑</el-button>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium" v-if="fddbrsqsData.id">
            <table cellspacing="0" style="width: 100%;table-layout:fixed;">
              <tbody>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    法定代表人姓名
                  </div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    {{ fddbrsqsData.fddbrxm }}
                  </div>
                </td>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    委托人姓名
                  </div>
                </td>
                <td colspan="9" class="el-table__cell is-leaf">
                  <div class="cell">
                    {{ fddbrsqsData.wtrxm }}
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    委托期限
                  </div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell">
                    {{ fddbrsqsData.wtqx }}
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>
        </div>
      </el-card>
    </el-col>

    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">资格审查及评审资料</div><el-divider></el-divider>
      <el-card class="box-card" style="margin-top:20px;">
        <div style="display:flex;align-items: center;justify-content: flex-start;">
          <div class="text" style="margin-right:10px">
            落实政府采购政策需满足的资格条件
          </div>
          <el-link type="primary" href="https://baosong.miit.gov.cn/ScaleTest" target="_blank">(中小企业自测)</el-link>
        </div>

        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
              <tr>
              <!-- <td
                colspan="4"
                class="el-table__cell is-leaf"
              >
                <div class="cell cell-right-border">
                  特殊企业类型
                </div>
              </td> -->
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-radio v-model="tsqylx" label="0">无</el-radio>
                    <el-radio v-model="tsqylx" label="zxqy">中小企业</el-radio>
                    <el-radio v-model="tsqylx" label="cjrflxdw">残疾人福利性单位</el-radio>
                    <el-radio v-model="tsqylx" label="jyqy">监狱企业</el-radio>
                  </div>
                </td>
              </tr>
              <tr v-if="tsqylx == 'zxqy'">
                <td colspan="5" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    附件一：中小企业声明函（货物）
                  </div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="text">
                    <el-button style="float: right; padding: 3px 0" type="text" @click="zxqysmhDialogAdd()">{{'添加'}}</el-button>
                  </div>
                </td>
              </tr>
              <tr v-if="tsqylx == 'zxqy'" v-for="(zxqysmhData,index) in zxqysmhDataArr" :key="zxqysmhData.qyId">
                <td colspan="4" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    条目 {{ index+1 }}
                  </div>
                </td>
                <td colspan="20" class="el-table__cell is-leaf">
                  <div class="cell">
                    <div class="text">
                      <el-button style="float: right; padding: 3px 10px" type="text" @click="zxqysmhDetailDel(zxqysmhData)">删除</el-button>
                      <el-button style="float: right; padding: 3px 0" type="text" @click="zxqysmhDialog(zxqysmhData)">{{'在线编辑'}}</el-button>
                    </div>
                    <div class="el-table el-table--enable-row-hover el-table--medium">
                      <table cellspacing="0" style="width: 100%;table-layout:fixed;">
                        <tbody>
                        <tr>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              制造商名称
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.zzsmc }}
                            </div>
                          </td>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              所属行业
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.sshy }}
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              从业人员人数
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.cyryrs }}
                            </div>
                          </td>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              营业收入
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.yysr }}
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              资产总额
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.zcze }}
                            </div>
                          </td>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              企业类型
                            </div>
                          </td>
                          <td colspan="9" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.qylx }}
                            </div>
                          </td>
                        </tr>
                        <tr>
                          <td colspan="3" class="el-table__cell is-leaf">
                            <div class="cell cell-right-border">
                              货物名称
                            </div>
                          </td>
                          <td colspan="21" class="el-table__cell is-leaf">
                            <div class="cell">
                              {{ zxqysmhData.hwmc }}
                            </div>
                          </td>
                        </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </td>
              </tr>
              <tr v-if="tsqylx === 'cjrflxdw'">
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell">
                    <span style="color: red;">注：选择后系统将自动生成。</span>
                  </div>
                </td>
              </tr>
              <tr v-if="tsqylx == 'jyqy'">
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="text">
                    监狱企业（注：需提供省级以上监狱管理局、戒毒管理局（含新疆生产建设兵团）出具的属于监狱企业的证明文件）
                    <el-button style="float: right; padding: 3px 0" type="text" @click="singleFileDialogOpen('jyqy','监狱企业', docResponseData.jyqy)">上传</el-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <el-divider></el-divider>
        <div class="text">
          特定资格要求
<!--          <el-button style="float: right; padding: 3px 0" type="text" @click="fileDialogOpen('tdzgyq','特定资格要求')">上传</el-button>-->
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr>
              <td colspan="22" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <span style="color:red">*</span>
                  营业执照
                </div>
              </td>
              <td colspan="2" class="el-table__cell is-leaf">
                <div class="cell">
                  <el-button type="text" @click="textFileDialogOpen('yyzz', '营业执照', docResponseData.yyzz)">
                    {{ docResponseData.yyzz.id == null || docResponseData.yyzz.id == '' ? '上传' :
                    '修改' }}</el-button>
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="24" class="el-table__cell is-leaf">
                <div class="cell">
                  <div class="cell cell-right-border">
                    资质证明
                    <el-button style="float: right; padding: 3px 0" type="text" @click="fileDialogOpen('zzzm', '资质证明', docResponseData.zzzm)">上传</el-button>
                  </div>
                  <table cellspacing="0" style="width: 100%;table-layout:fixed;">
                    <tbody>
                    <tr v-for="(detail, index) in docResponseData.zzzm" :key="index">
                      <td colspan="22" class="el-table__cell is-leaf">
                        <div class="cell cell-right-border">
                          {{ detail.detailName }}
                        </div>
                      </td>
                      <td colspan="2" class="el-table__cell is-leaf">
                        <div class="cell">
                          <el-button type="text" @click="fileDialogOpen('zzzm', '资质证明', detail)">修改</el-button>
                          <el-button type="text" @click="removeFileDetail(detail, index, '资质证明')">删除</el-button>
                        </div>
                      </td>
                    </tr>
                    </tbody>
                  </table>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
        <el-divider></el-divider>
        <div class="text">
          信用查询
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
              <tr>
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-radio v-model="xylx" label="cnh">承诺函</el-radio>
                    <!-- <el-radio v-model="xylx" label="jt">截图</el-radio> -->
                  </div>
                </td>
              </tr>
              <tr v-if="xylx === 'cnh'">
                <td colspan="24" class="el-table__cell is-leaf">
                  <div class="cell">
                    <span style="color: red;">注：选择后系统将自动生成。</span>
                  </div>
                </td>
              </tr>
              <tr v-if="xylx == 'jt'">
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    中国政府采购网
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="fileDialogOpen('zgzfcgw','中国政府采购网',docResponseData.zgzfcgw, true)">
                      {{docResponseData.zgzfcgw.id==null||docResponseData.zgzfcgw.id==''?'上传':'修改'}}</el-button>
                  </div>
                </td>
              </tr>
              <tr v-if="xylx == 'jt'">
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    中国执行信息公开网
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="fileDialogOpen('zgzxxxgkw','中国执行信息公开网',docResponseData.zgzxxxgkw, true)">
                      {{docResponseData.zgzxxxgkw.filePath==null||docResponseData.zgzxxxgkw.filePath==''?'上传':'修改'}}</el-button>
                  </div>
                </td>
              </tr>
              <tr v-if="xylx == 'jt'">
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    信用中国
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="fileDialogOpen('xyzg','信用中国',docResponseData.xyzg, true)">
                      {{docResponseData.xyzg.filePath==null||docResponseData.xyzg.filePath==''?'上传':'修改'}}</el-button>
                  </div>
                </td>
              </tr>
              <!--              <tr v-if="xylx == 'jt'">
                <td
                  colspan="22"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell cell-right-border">
                    全国建筑市场监管公共服务平台
                  </div>
                </td>
                <td
                  colspan="2"
                  class="el-table__cell is-leaf"
                >
                  <div class="cell">
                    <el-button
                      type="text"
                      @click="fileDialogOpen('qgjzscjgggfwpt','全国建筑市场监管公共服务平台',docResponseData.qgjzscjgggfwpt, true)"
                    >
                      {{docResponseData.qgjzscjgggfwpt.filePath==null||docResponseData.qgjzscjgggfwpt.filePath==''?'上传':'修改'}}</el-button>
                  </div>
                </td>
              </tr>-->
            </tbody>
          </table>
        </div>
        <el-divider></el-divider>
        <div class="text">
          其他资格证明文件
          <el-button style="float: right; padding: 3px 0" type="text" @click="fileDialogOpen('qtzgzmwj','其他资格证明文件')">上传</el-button>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr v-for="(detail, index) in docResponseData.qtzgzmwj" :key="index">
              <td colspan="22" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ detail.detailName }}
                </div>
              </td>
              <td colspan="2" class="el-table__cell is-leaf">
                <div class="cell">
                  <el-button type="text" @click="fileDialogOpen('qtzgzmwj', '其他资格证明文件', detail)">修改</el-button>
                  <el-button type="text" @click="removeFileDetail(detail, index, '其他资格证明文件')">删除</el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
<!--        <div class="el-table el-table&#45;&#45;enable-row-hover el-table&#45;&#45;medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr v-for="(detail, index) in docResponseData.qtzgzmwj" :key="index">
              <td colspan="22" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ detail.detailName }}
                </div>
              </td>
              <td colspan="2" class="el-table__cell is-leaf">
                <div class="cell">
                  <el-button type="text" @click="fileDialogOpen('qtzgzmwj','其他资格证明文件',detail)">修改</el-button>
                  <el-button type="text" @click="removeFileDetail(detail, index, '其他资格证明文件')">删除</el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>-->

      </el-card>
    </el-col>

    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">技术部分</div><el-divider></el-divider>
      <el-card class="box-card">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
              <tr>
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    技术方案
                    <P style="color:red">注：响应人依“采购需求”技术要求，如实提供服务或技术方案，格式自拟。</P>
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="textFileDialogOpen('jsfa', '技术方案', docResponseData.jsfa)">
                      {{ docResponseData.jsfa.id == null || docResponseData.jsfa.id == '' ? '添加' :
                      '修改' }}</el-button>
                  </div>
                </td>
              </tr>
              <tr>
                <td colspan="22" class="el-table__cell is-leaf">
                  <div class="cell cell-right-border">
                    <span style="color:red">*</span>
                    技术偏离表
                  </div>
                </td>
                <td colspan="2" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-button type="text" @click="textFileDialogOpen('jsplb', '技术偏离表', docResponseData.jsplb)">
                      {{ docResponseData.jsplb.id == null || docResponseData.jsplb.id == '' ? '添加' :
                      '修改' }}</el-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

      </el-card>
    </el-col>

    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">商务部分</div><el-divider></el-divider>
      <el-card class="box-card" style="margin-top:20px;">
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr v-for="item in this.docResponseInfo.businessPart">
              <td colspan="22" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <span style="color:red">*</span>
                  {{ item.detailName }}
                </div>
              </td>
              <td colspan="2" class="el-table__cell is-leaf">
                <div class="cell">
                  <el-button type="text" @click="textFileDialogOpen_swbf(item.detailCode,item.detailName,docResponseData[item.detailCode])">
                    {{isBusinessPartItemCode(docResponseData[item.detailCode])?'修改':'上传'}}</el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>
      </el-card>
    </el-col>
    <el-col :span="24" class="card-box">
      <div style="margin-top:50px;font-size:20px;font-weight:bold">其他资料</div><el-divider></el-divider>
      <el-card class="box-card" style="margin-top:20px;">
        <div class="text">
          响应人认为需要提供的其他资料
          <el-button style="float: right; padding: 3px 0" type="text" @click="fileDialogOpen('tbrrwxytgdqtzl', '投标人认为需要提供的其他资料')">上传</el-button>
        </div>
        <div class="el-table el-table--enable-row-hover el-table--medium">
          <table cellspacing="0" style="width: 100%;table-layout:fixed;">
            <tbody>
            <tr v-for="(detail, index) in docResponseData.tbrrwxytgdqtzl" :key="index">
              <td colspan="22" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ detail.detailName }}
                </div>
              </td>
              <td colspan="2" class="el-table__cell is-leaf">
                <div class="cell">
                  <el-button type="text" @click="fileDialogOpen('tbrrwxytgdqtzl', '投标人认为需要提供的其他资料', detail)">修改</el-button>
                  <el-button type="text" @click="removeFileDetail(detail, index, '投标人认为需要提供的其他资料')">删除</el-button>
                </div>
              </td>
            </tr>
            </tbody>
          </table>
        </div>

      </el-card>
    </el-col>

    <el-button style="float: right; margin-right: 30px;" type="primary" @click="createSecurityDocResponse">3.生成加密文件</el-button>
    <el-button style="float: right; margin-right: 30px;" type="primary" @click="fileSign">2.响应文件签章</el-button>
    <el-button style="float: right; margin-right: 30px;" type="primary" @click="createGoodsDocResponse">1.生成响应文件</el-button>

    <!-- 添加或修改开标一览表对话框 -->
    <el-dialog :close-on-click-modal="false" title="开标一览表" :visible.sync="kbylbOpen" width="60%" append-to-body>
      <el-form ref="kbylbForm" :model="kbylbData" :rules="kbylbRules" label-width="150px">

        <el-form-item label="投标报价（大写）" prop="tbbjdx">
          <el-input :disabled="true" v-model="tbbjdx" placeholder="请输入投标报价（大写）"></el-input>
        </el-form-item>
        <el-form-item label="投标报价（小写）" prop="bidPrice">
          <el-input type="number" v-model="kbylbData.bidPrice" placeholder="请输入投标报价（小写）" maxlength="10" :min='0'><template slot="append">元</template></el-input>
        </el-form-item>
        <el-form-item label="质量要求" prop="qualityDemand">
          <el-input v-model="kbylbData.qualityDemand" placeholder="请输入质量要求" />
        </el-form-item>
        <el-form-item label="供货期" prop="overTimeLimit">
          <el-input type="number" v-model="kbylbData.overTimeLimit" placeholder="供货期" maxlength="5"><template slot="append">日历日</template></el-input>
        </el-form-item>
        <el-form-item label="质保期" prop="warrantyPeriod">
          <el-input type="number" v-model="kbylbData.warrantyPeriod" placeholder="请输入质保期" maxlength="3"><template slot="append">年</template></el-input>
        </el-form-item>
        <el-form-item label="投标有效期" prop="tbyxq">
          <el-input v-model="kbylbData.tbyxq" disabled maxlength="3" placeholder="请输入投标有效期" type="number">
            <template slot="append">日历日</template>
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="kbylbSave">保存</el-button>
        <el-button @click="kbylbCancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改法定代表人身份证明对话框 -->
    <el-dialog :close-on-click-modal="false" title="法定代表人身份证明" :visible.sync="fddbrsfzmOpen" width="60%" append-to-body>
      <el-form ref="fddbrsfzmData" :model="fddbrsfzmData" :rules="fddbrsfzmRules" label-width="150px">
        <el-form-item label="法定代表人姓名" prop="fddbrxm">
          <el-input v-model="fddbrsfzmData.fddbrxm" placeholder="请输入法定代表人姓名"  style="display:none;"/>
          {{fddbrsfzmData.fddbrxm}}
        </el-form-item>
        <el-form-item label="文件" prop="filePath">
            <FileUpload :value="fddbrsfzmData.filePath" v-model="fddbrsfzmData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fddbrsfzmSave">保存</el-button>
        <el-button @click="fddbrsfzmDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改法定代表人授权书对话框 -->
    <el-dialog :close-on-click-modal="false" title="法定代表人授权书" :visible.sync="fddbrsqsOpen" width="60%" append-to-body>
      <el-form ref="fddbrsqsData" :model="fddbrsqsData" :rules="fddbrsqsRules" label-width="150px">
        <el-form-item label="法定代表人姓名" prop="fddbrxm">
          <el-input v-model="fddbrsqsData.fddbrxm" placeholder="请输入法定代表人姓名"  style="display:none;"/>
          {{fddbrsfzmData.fddbrxm}}
        </el-form-item>
        <el-form-item label="委托人姓名" prop="wtrxm">
          <el-input v-model="fddbrsqsData.wtrxm" placeholder="请输入委托人姓名" />
        </el-form-item>
        <el-form-item label="委托期限" prop="wtqx">
          <el-input v-model="fddbrsqsData.wtqx" placeholder="请输入委托期限"></el-input>
        </el-form-item>
        <el-form-item label="文件" prop="filePath">
          <template>
            <FileUpload :value="fddbrsqsData.filePath" v-model="fddbrsqsData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fddbrsqsSave">保 存</el-button>
        <el-button @click="fddbrsqsDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改中小企业声明函对话框 -->
    <el-dialog :close-on-click-modal="false" title="中小企业声明函" :visible.sync="zxqysmhOpen" width="60%" append-to-body>
      <el-form ref="zxqysmhData" :model="zxqysmhData" :rules="zxqysmhRules" label-width="150px">
        <el-form-item label="制造商名称" prop="zzsmc">
          <el-input v-model="zxqysmhData.zzsmc" placeholder="请输入制造商名称" />
        </el-form-item>
        <el-form-item label="所属行业" prop="sshy">
          <el-input v-model="zxqysmhData.sshy" placeholder="请输入所属行业" />
        </el-form-item>
        <el-form-item label="货物名称" prop="hwmc">
          <el-input v-model="zxqysmhData.hwmc" placeholder="请输入货物名称" />
        </el-form-item>
        <el-form-item label="从业人员人数" prop="cyryrs">
          <el-input type="number" :min="0" v-model="zxqysmhData.cyryrs" placeholder="请输入从业人员人数" />
        </el-form-item>
        <el-form-item label="营业收入" prop="yysr">
          <el-input type="number" v-model="zxqysmhData.yysr" placeholder="请输入营业收入"><template slot="append">万元</template></el-input>
        </el-form-item>
        <el-form-item label="资产总额" prop="zcze">
          <el-input type="number" v-model="zxqysmhData.zcze" placeholder="请输入资产总额"><template slot="append">万元</template></el-input>
        </el-form-item>
        <el-form-item label="企业类型" prop="qylx">
          <el-select style="width:50%" v-model="zxqysmhData.qylx" placeholder="请选择企业类型（中型、小型、微型）">
            <el-option label="中型企业" :value="'中型企业'">
            </el-option>
            <el-option label="小型企业" :value="'小型企业'">
            </el-option>
            <el-option label="微型" :value="'微型'">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="zxqysmhSave">保存</el-button>
        <el-button @click="zxqysmhDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改上传附件对话框 -->
    <el-dialog :close-on-click-modal="false" :title="fielTitle" :visible.sync="fileOpen" width="60%" append-to-body>
      <el-form :rules="fileDetailRules" label-width="150px">
        <el-form-item label="附件名称" prop="detailName">
          <el-input v-model="fileDetailData.detailName" placeholder="请输入附件名称" :disabled="detailNameDisabled" />
        </el-form-item>
        <el-form-item label="文件" prop="filePath">
          <template>
            <FileUpload :value="fileDetailData.filePath" v-model="fileDetailData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
          </template>
          <div  v-if = "fielTitle.includes('明细报价表')" class="image__preview">
          </div>
        </el-form-item>
        <div v-if="!fielTitle.includes('明细报价表')" style = "color: red; margin-left: 150px;">注:只能上传pdf文件</div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fileDetailSave">保 存</el-button>
        <el-button @click="fileDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改确认对话框 -->
    <el-dialog :close-on-click-modal="false" :title="fielTitle" :visible.sync="confirmOpen" width="60%" append-to-body>
      确认添加 {{ fileDetailData.detailName }} 项
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fileDetailSave">确 认</el-button>
        <el-button @click="confirmDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改上传附件对话框 -->
    <el-dialog :close-on-click-modal="false" :title="fielTitle" :visible.sync="singleFileOpen" width="60%" append-to-body>
      <el-form :rules="kbylbRules" label-width="150px">
        <el-form-item label="文件名称" prop="detailName">
          {{ fileDetailData.detailName }}
        </el-form-item>
        <el-form-item label="文件" prop="filePath">
          <template>
            <FileUpload :value="fileDetailData.filePath" v-model="fileDetailData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fileDetailSave">保存</el-button>
        <el-button @click="singleFileDialogClose">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改富文本框上传附件对话框 -->
    <el-dialog :close-on-click-modal="false" :title="fielTitle" :visible.sync="textFileOpen" width="60%" append-to-body>
      <el-form :rules="fileDetailRules" label-width="150px">
        <el-form-item label="附件名称" prop="detailName">
          {{ fileDetailData.detailName }}
        </el-form-item>
        <!-- <el-form-item prop="detaiContent">
          <editor
            v-model="fileDetailData.detailContent"
            :min-height="192"
          />
        </el-form-item> -->
        <el-form-item label="文件" prop="filePath">
          <template>
            <FileUpload :value="fileDetailData.filePath" v-model="fileDetailData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="fileDetailSave">保存</el-button>
        <el-button @click="textFileDialogClose">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 添加或修改拟派项目管理机构成员对话框 -->
    <el-dialog :close-on-click-modal="false" :title="fielTitle" :visible.sync="memberFileOpen" width="60%" append-to-body>
      <el-form :rules="npxmgljgRules" label-width="150px" :model="npxmgljgData">
        <el-form-item label="项目组人员姓名" prop="memberName">
          <el-input v-model="npxmgljgData.memberName" placeholder="请输入成员姓名" />
        </el-form-item>
        <el-form-item label="项目组人员职务" prop="memberDuty">
          <el-select v-model="npxmgljgData.memberDuty" placeholder="请选择成员职务">
            <el-option label="施工员" :value="'施工员'">
            </el-option>
            <el-option label="安全员" :value="'安全员'">
            </el-option>
            <el-option label="安全员" :value="'安全员'">
            </el-option>
            <el-option label="质量员或质检员" :value="'质量员或质检员'">
            </el-option>
            <el-option label="材料员" :value="'材料员'">
            </el-option>
            <el-option label="资料员" :value="'资料员'">
            </el-option>
            <el-option label="预算员" :value="'预算员'">
            </el-option>
            <el-option label="项目技术负责人" :value="'项目技术负责人'">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="职称证书扫描件" prop="filePath">
          <template>
            <FileUpload :value="npxmgljgData.filePath" v-model="npxmgljgData.filePath" :fileType="['pdf']" :isShowTip="false" :showOnly=false></FileUpload>
          </template>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="memberFileDetailSave">保存</el-button>
        <el-button @click="memberFileDialogClose">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  entList,
  saveInfo,
  saveDetail,
  getById,
  delDetail,
  systemVerification,
  createGoodsDocResponse,
  createSecurityDocResponse,
  getProcurementInfo,
  delZxqysmhDetail, createInquiryGoodsDocResponse, createDocResponse
} from "@/api/docResponse/entInfo";
import { getItemMap } from "@/api/docResponse/info";
import { convertToChineseCurrency } from "@/utils/amount";

import { getUserProfile } from "@/api/system/user";
import log from "@/views/monitor/job/log";
export default {
  components: {},
  props: [],
  data() {
    return {
      // 遮罩层
      loading: false,
      showOnly: false,
      projectId: "",
      srcList: [
        "/fileResponse/goods/pianli.png",
        "/fileResponse/goods/mingxibaojia.png",
        "/fileResponse/goods/shenfenzheng.png"
      ],
      projectInfo: {
        projectCode: "CS20241010001",
        projectName: "20241010测试项目01",
      },
      bidder: {
        entName: "供应商3号",
        entCode: "123456789123456789",
      },
      kbylbOpen: false,
      fddbrsfzmOpen: false,
      fddbrsqsOpen: false,
      zxqysmhOpen: false,
      confirmOpen: false,
      docResponseInfo: {},
      docResponseData: {
        kbylb: {},
        mxbjb: [],
        fddbrsfzm: {},
        fddbrsqs: {},
        lszfcgzcxmzdzgtj: [],
        tdzgyq: [],
        xycx: [],
        zgzfcgw: {},
        zgzxxxgkw: {},
        xyzg: {},
        qgjzscjgggfwpt: {},
        qtzgzmwj: [],
        ybjgclqd: [],
        qtfhxys: [],
        plb: [],
        fwfahjsfa: {},
        npxmfwtd: {},
        jsplb: {},
        swplb: {},
        jsbf: [],
        swbf: [],
        qtbf: [],
        xytgdqtzl: [],
        zxqysmh: {},
        cjrflxdwsmh: {},
        jyqy: {},
        ywyj: {},
        txrz: {},
        npxmglry: {},
        xmjsfzr: {},
        jsfa: {},
        jnjp: {},
        xgy: {},
        fxglcs: {},
        yyzz: {},
        zzzm: {}
      },
      fddbrsfzmData: {
        fddbrxm: "",
        fddbrxb: "",
        filePath:"",
        fddbrnl: "",
        fddbrzw: "",
      },
      zxqysmhDataArr:[],
      zxqysmhDetailId: "",
      zxqysmhData: {
        qyId: "",
        zzsmc: "",
        hwmc: "",
        sshy: "",
        cyryrs: "",
        yysr: "",
        zcze: "",
        qylx: "",
      },
      fddbrsqsData: {
        fddbrxm: "",
        wtrxm: "",
        wtqx: "自本授权委托书签署之日起至“投标有效期”结束为止。",
      },
      kbylbData: {
        tbbjdx: "",
        bidPrice: "",
        overTimeLimit: "",
        warrantyPeriod: "",
        qualityDemand: "合格",
        tbyxq: "90",
      },
      npxmgljgData: {
        memberName: "",
        memberDuty: "",
        filePath: "",
      },
      docResponseItemMap: {
        kbylb: 12,
        tdzgyq: 313,
        xycx: 314,
      },
      kbylbRules: {
        bjnr: [
          {
            required: true,
            message: "请填写报价内容，没有填无",
            trigger: "blur",
          },
        ],
        tbbjdx: [
          { required: true, message: "请填写大写投标报价", trigger: "blur" },
        ],
        bidPrice: [
          { required: true, message: "请填写小写投标报价", trigger: "blur" },
          { min: 0, max: 9, message: '长度在 0 到 9 个字符', trigger: 'blur' },
          { pattern: /^(?:[1-9]\d*(?:\.\d+)?|0(?:\.\d+))$/, message: "请输入具有实际意义的数字", trigger: "blur"},
        ],
        overTimeLimit: [
          { required: true, message: "请填写供货期", trigger: "blur" },
          { min: 0, max: 5, message: '长度在 0 到 5 个字符', trigger: 'blur' },
          { pattern: /^\d+$/, message: "请输入具有实际意义的数字", trigger: "blur"},
        ],
        warrantyPeriod: [
          { required: true, message: "请填写质保期", trigger: "blur" },
          { min: 0, max: 3, message: '长度在 0 到 3 个字符', trigger: 'blur' },
          { pattern: /^\d+$/, message: "请输入具有实际意义的数字", trigger: "blur"},
        ],
        qualityDemand: [
          { required: true, message: "请填写质量要求", trigger: "blur" },
          { min: 0, max: 200, message: '长度在 0 到 200 个字符', trigger: 'blur' },
        ],
        tbyxq: [
          { required: true, message: "请填写投标有效期", trigger: "blur" },
        ],
        detailName: [
          { required: true, message: "请填写明细名称", trigger: "blur" },
        ],
      },
      fddbrsfzmRules: {
        fddbrxm: [
          { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
          { min: 0, max: 20, message: '长度在 0 到 20 个字符', trigger: 'blur' },
        ],
        filePath: [{ required: true, message: "请上传", trigger: "blur" }],
      },
      fddbrsqsRules: {
        fddbrxm: [
          { required: true, message: "请输入法定代表人姓名", trigger: "blur" },
          { min: 0, max: 20, message: '长度在 0 到 20 个字符', trigger: 'blur' },
        ],
        wtrxm: [
          { required: true, message: "请输入委托人姓名", trigger: "blur" },
          { min: 0, max: 20, message: '长度在 0 到 20 个字符', trigger: 'blur' },
        ],
        wtqx: [
          { required: true, message: "请输入委托期限", trigger: "blur" },
          { min: 0, max: 100, message: '长度在 0 到 100 个字符', trigger: 'blur' },
        ],
        filePath: [{ required: true, message: "请上传", trigger: "blur" }],
      },
      zxqysmhRules: {
        zzsmc: [
          { required: true, message: "请输入制作商名称", trigger: "blur" },
          { min: 0, max: 100, message: '长度在 0 到 100 个字符', trigger: 'blur' },
        ],
        hwmc: [
          { required: true, message: "请输入货物名称", trigger: "blur" },
          { min: 0, max: 100, message: '长度在 0 到 300 个字符', trigger: 'blur' },
        ],
        sshy: [
          { required: true, message: "请输入所属行业", trigger: "blur" },
          { min: 0, max: 100, message: '长度在 0 到 100 个字符', trigger: 'blur' },
        ],
        cyryrs: [
          { required: true, message: "请输入从业人员人数", trigger: "blur" },
          { min: 0, max: 10, message: '长度在 0 到 10 个字符', trigger: 'blur' },
        ],
        yysr: [
          { required: true, message: "请输入营业收入", trigger: "blur" },
          { min: 0, max: 11, message: '长度在 0 到 11 个字符', trigger: 'blur' },
        ],
        zcze: [
          { required: true, message: "请输入资产总额", trigger: "blur" },
          { min: 0, max: 11, message: '长度在 0 到 11 个字符', trigger: 'blur' },
        ],
        qylx: [
          {
            required: true,
            message: "请输入企业类型（中型、小型、微型",
            trigger: "blur",
          },
        ],
      },
      fileDetailRules: {
        detailName: [
          { required: true, message: "请输入附件名称", trigger: "blur" },
          { min: 0, max: 20, message: '长度在 0 到 20 个字符', trigger: 'blur' },
        ],
        filePath: [{ required: true, message: "请上传", trigger: "blur" }],
      },
      npxmgljgRules: {
        memberName: [
          { required: true, message: "请输入成员名称", trigger: "blur" },
          { min: 0, max: 50, message: '长度在 0 到 50 个字符', trigger: 'blur' },
        ],
        memberDuty: [
          { required: true, message: "请输入项目组人员职务", trigger: "blur" },
          { min: 0, max: 50, message: '长度在 0 到 50 个字符', trigger: 'blur' },
        ],
        filePath: [{ required: true, message: "请上传", trigger: "blur" }],
      },
      detailNameDisabled: false,
      fileOpen: false,
      fielTitle: "",
      fileItemName: "",
      fileDetailData: {
        docResponseItemCode: "",
        detailName: "",
        filePath: "",
        ditalContent: "",
      },
      singleFileOpen: false,
      textFileOpen: false,
      textFileDetailData: {
        docResponseItemCode: "",
        detailName: "",
        detailContent: "",
        filePath: "",
      },
      memberFileOpen: false,
      tsqylx: "0",
      xylx: "cnh",
      frorsqs: "fr",
      verification: {
        bidPrice: "",
        overTimeLimit: "",
        warrantyPeriod: ""
      }
    };
  },
  computed: {
    tbbjdx() {
      return convertToChineseCurrency(this.kbylbData.bidPrice);
    },
  },
  created() {
    this.getInfoById();
    getUserProfile().then((response) => {
      if (response.code == 200) {
        // console.log("response.data", response.data);
        this.bidder = response.data;
        // 法人信息
        if(response.data.ent.entLegalPerson){
          this.fddbrsfzmData.fddbrxm = response.data.ent.entLegalPerson;
        }
        if(response.data.ent.entLegalPersonCardFile){
          this.fddbrsfzmData.filePath = response.data.ent.entLegalPersonCardFile;
        }
        // console.log("this.bidder", this.bidder);
        // console.log("this.fddbrsfzmData", this.fddbrsfzmData);
      } else {
        this.$message.error(res.message);
      }
    });
    this.projectInfo.projectCode = this.$route.query.projectCode;
    this.projectInfo.projectName = this.$route.query.projectName;
  },
  mounted() { },
  methods: {
    createGoodsDocResponse() {
      let data = {
        infoId: this.$route.params.id,
        frorsqs: this.frorsqs,
        tsqylx: this.tsqylx,
        xylx: this.xylx,
        isCheck: "1",
      };
      // 此操作将重新生成未签章的响应文件, 是否继续?
      this.$confirm(
        "点击生成响应文件按钮后，请耐心等待系统处理完成，请勿重复点击避免导致原有文件被覆盖的情况发生。",
        "提示",
        {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        this.loading = true;
        createDocResponse(data)
          .then((result) => {
            if (result.code == 200) {
              this.loading = false;
              this.$confirm(
                "系统校验提示："+ result.data + "\n 点击【确定】可继续生成响应文件",
                "提示",
                {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning",
                  customClass: 'custom-confirm'
                }
              ).then(() => {
                data.isCheck = "0";
                this.loading = true;
                createDocResponse(data).then((result) => {
                  if (result.code == 200) {
                    this.$message.success(result.msg);
                    this.loading = false;
                  } else {
                    this.$message.error(result.msg);
                    this.loading = false;
                  }
                }).catch(() => {
                  this.loading = false;
                });
              });
            } else {
              this.$message.error(result.msg);
              this.loading = false;
            }
          })
          .catch(() => {
            this.loading = false;
          });
      });
    },
    fileSign() {
      window.open(
        `${process.env.VUE_APP_KAIFANFQIAN_API}/?project=${this.projectInfo.projectId
        }&type=${1}&entId=${this.bidder.entId}`,
        "_blank"
      );
    },
    createSecurityDocResponse() {
      createSecurityDocResponse(this.$route.params.id).then((result) => {
        if (result.code == 200) {
          this.$message.success(result.msg);
          const filePath = result.data;
          const a = document.createElement("a");
          a.href = process.env.VUE_APP_BASE_API + filePath;
          // a.download = "响应文件.tbwj";
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        } else {
          this.$message.error(result.msg);
        }
      });
    },
    getItemMap() {
      getItemMap();
    },
    getInfoById() {
      getById(this.$route.params.id).then((result) => {
        if (result.code == 200) {
          this.docResponseInfo = result.data;
          this.projectInfo.projectId = result.data.projectId;
          this.docResponseInfo.businessPart.map(item => {
            if (this.docResponseInfo.detailMap[item.detailCode] == undefined) {
              this.docResponseInfo.detailMap[item.detailCode] = [];
            }
          })

          for (var key in this.docResponseInfo.detailMap) {
            if (key === "kbylb") {
              this.kbylbData = JSON.parse(
                this.docResponseInfo.detailMap[key][0].detailContent
              );
              this.kbylbData.id = this.docResponseInfo.detailMap[key][0].id;
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else if (key === "fddbrsfzm") {
              this.fddbrsfzmData = JSON.parse(
                this.docResponseInfo.detailMap[key][0].detailContent
              );
              this.fddbrsfzmData.id = this.docResponseInfo.detailMap[key][0].id;
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else if (key === "fddbrsqs") {
              this.fddbrsqsData = JSON.parse(
                this.docResponseInfo.detailMap[key][0].detailContent
              );
              this.fddbrsqsData.id = this.docResponseInfo.detailMap[key][0].id;
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else if (key === "zxqysmh") {
              // this.zxqysmhData = JSON.parse(
              //   this.docResponseInfo.detailMap[key][0].detailContent
              // );
              // this.zxqysmhData.id = this.docResponseInfo.detailMap[key][0].id;
              // this.docResponseData[key] =
              //   this.docResponseInfo.detailMap[key][0];
              this.zxqysmhDataArr = JSON.parse(
                this.docResponseInfo.detailMap[key][0].detailContent
              );
              this.zxqysmhDetailId = this.docResponseInfo.detailMap[key][0].id;
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else if (key === "ybjgclqd") {
              this.fileDetailData.id =
                this.docResponseInfo.detailMap[key][0].id;
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else if (
              key === "jsplb" ||
              key === "swplb" ||
              key === "fwfahjsfa" ||
              key === "jyqy" ||
              key === "cjrflxdwsmh" ||
              key === "zgzfcgw" ||
              key === "zgzxxxgkw" ||
              key === "xyzg" ||
              key === "qgjzscjgggfwpt" ||
              key === "jsfa" ||
              key === "gcjdjhycs" ||
              key === "jnjp" ||
              key === "xgy" ||
              key === "fxglcs" ||
              key === "ywyj" ||
              key === "txrz" ||
              key === "npxmglry" ||
              key === "xmjsfzr" ||
              key === "yyzz"
            ) {
              this.docResponseData[key] =
                this.docResponseInfo.detailMap[key][0];
            } else {
              this.docResponseData[key] = this.docResponseInfo.detailMap[key];
            }
          }
          getItemMap(result.data.docResponseId).then((res) => {
            if (res.code == 200) {
              this.docResponseItemMap = res.data;
            }
          });
        } else {
          this.$message.success(result.msg);
        }
        // 给法定代表人赋默认值
        this.fddbrsqsData.fddbrxm =  result.data.entLegalPerson;
        this.fddbrsfzmData.fddbrxm =  result.data.entLegalPerson;
        // console.log("kbylbData", this.kbylbData);
        // console.log("fddbrsfzmData", this.fddbrsfzmData);
        // console.log("fddbrsqsData", this.fddbrsqsData);
        // console.log("docResponseData", this.docResponseData);
      });
    },
    kbylbDialog() {
      getProcurementInfo(this.$route.params.id).then((result) => {
        if (result.code == 200) {
          result.data.map((item) => {
            if (item.code == "bidPrice") {
              this.verification.bidPrice = item.defaultValue;
            }
            if (item.code == "overTimeLimit") {
              this.verification.overTimeLimit = item.defaultValue;
            }
            if (item.code == "warrantyPeriod") {
              this.verification.warrantyPeriod = item.defaultValue;
            }
            if (item.code == "bidValidityPeriod") {
              this.kbylbData.tbyxq = item.defaultValue;
            }
          })
          this.kbylbOpen = true;
        }
      })
    },
    fddbrsfzmDialog() {
      this.fddbrsfzmOpen = true;
    },
    fddbrsqsDialog() {
      this.fddbrsqsOpen = true;
    },
    zxqysmhDialogAdd() {
      for (let key in this.zxqysmhData) {
        if (this.zxqysmhData.hasOwnProperty(key)) {
          this.zxqysmhData[key] = null; // 或者使用 undefined, "" 等
        }
      }
      this.zxqysmhData.id = this.zxqysmhDetailId;
      this.zxqysmhOpen = true;
    },
    zxqysmhDialog(params) {
      this.zxqysmhData = params;
      this.zxqysmhData.id = this.zxqysmhDetailId;
      this.zxqysmhOpen = true;
    },
    confirmDialog(docResponseItemCode, itemName, row) {
      this.fielTitle = itemName + " — 确认信息";
      this.fileItemName = itemName;
      this.fileDetailData.docResponseItemCode = docResponseItemCode;

      this.fileDetailData.id = row.id;
      this.fileDetailData.detailName = itemName;
      this.confirmOpen = true;
    },
    fddbrsfzmDialogClose() {
      this.fddbrsfzmOpen = false;
    },
    fddbrsqsDialogClose() {
      this.fddbrsqsOpen = false;
    },
    zxqysmhDialogClose() {
      this.zxqysmhOpen = false;
    },

    confirmDialogClose() {
      this.confirmOpen = false;
    },
    kbylbSave() {
      this.kbylbData.tbbjdx = this.tbbjdx;
      this.$refs["kbylbForm"].validate(valid => {
        let message = "";
        if (parseInt(this.verification.bidPrice) < parseInt(this.kbylbData.bidPrice)) {
          message = message + "投标报价大于项目预算金额<br>";
        }
        if (parseInt(this.verification.overTimeLimit) < parseInt(this.kbylbData.overTimeLimit)) {
          message = message + "供货期大于项目要求供货期<br>";
        }
        if (parseInt(this.verification.warrantyPeriod) > parseInt(this.kbylbData.warrantyPeriod)) {
          message = message + "质保期小于项目要求质保期";
        }
        if (valid) {
          var kbylb = {};
          kbylb.id = this.kbylbData.id;
          kbylb.docResponseItemId = this.docResponseItemMap["kbylb"];
          kbylb.docResponseItemCode = "kbylb";
          kbylb.docResponseEntId = this.$route.params.id;
          kbylb.detailCode = "kbylb";
          kbylb.detailName = "开标一览表";
          kbylb.detailType = "2";
          kbylb.detailSort = "1";
          kbylb.detailContent =  JSON.stringify(this.kbylbData);
          if (message.length > 0) {
            const h = this.$createElement;
            this.$msgbox({
              title: '提示',
              message: `<p >${message}</p>`,
              dangerouslyUseHTMLString: true,
              confirmButtonText: '确定',
            }).then(() => {
              saveDetail(kbylb).then((result) => {
                this.getInfoById();
              });
              this.$message.success("保存成功");
              this.kbylbOpen = false;
            }).catch(() => {
            });
          } else {
            saveDetail(kbylb).then((result) => {
              this.getInfoById();
            });
            this.$message.success("保存成功");
            this.kbylbOpen = false;
          }
        }
      });
    },
    fddbrsfzmSave() {
      this.$refs["fddbrsfzmData"].validate(valid => {
        if (valid) {
          if(!this.fddbrsfzmData.filePath){
            this.$message.error("请上传法定代表人身份证明文件");
            return;
          }
          var fddbrsfzm = {};
          fddbrsfzm.id = this.fddbrsfzmData.id;
          fddbrsfzm.docResponseItemId = this.docResponseItemMap["fddbrsfzm"];
          fddbrsfzm.docResponseItemCode = "fddbrsfzm";
          fddbrsfzm.docResponseEntId = this.$route.params.id;
          fddbrsfzm.detailCode = "fddbrsfzm";
          fddbrsfzm.detailName = "法定代表人身份证明";
          fddbrsfzm.detailType = "2";
          fddbrsfzm.detailSort = "1";
          fddbrsfzm.detailContent = JSON.stringify(this.fddbrsfzmData);
          fddbrsfzm.filePath = this.fddbrsfzmData.filePath;
          saveDetail(fddbrsfzm).then((result) => {
            // this.getInfoById();
            if (result.code == 200) {
              this.$message.success("保存成功");
              this.fddbrsfzmDialogClose();
              // this.getInfoById();
              this.fddbrsfzmData.id = result.data.id;
            } else {
              this.$message.error(result.msg);
            }
          });
        }
      })
    },
    fddbrsqsSave() {
      this.$refs["fddbrsqsData"].validate(valid => {
        if (valid) {
          var fddbrsqs = {};
          fddbrsqs.id = this.fddbrsqsData.id;
          fddbrsqs.docResponseItemId = this.docResponseItemMap["fddbrsqs"];
          fddbrsqs.docResponseItemCode = "fddbrsqs";
          fddbrsqs.docResponseEntId = this.$route.params.id;
          fddbrsqs.detailCode = "fddbrsqs";
          fddbrsqs.detailName = "法定代表人授权书";
          fddbrsqs.detailType = "2";
          fddbrsqs.detailSort = "1";
          fddbrsqs.detailContent =  JSON.stringify(this.fddbrsqsData);
          fddbrsqs.filePath = this.fddbrsqsData.filePath;
          saveDetail(fddbrsqs).then((result) => {
            if (result.code == 200) {
              this.$message.success("保存成功");
              this.fddbrsqsDialogClose();
              // this.getInfoById();
              this.fddbrsqsData.id = result.data.id;
            } else {
              this.$message.error(result.msg);
            }
          });
        }
      })
    },
    zxqysmhSave() {
      this.$refs["zxqysmhData"].validate(valid => {
        if (valid) {
          var zxqysmh = {};
          zxqysmh.id = this.zxqysmhDetailId;
          zxqysmh.docResponseItemId = this.docResponseItemMap["zxqysmh"];
          zxqysmh.docResponseItemCode = "zxqysmh";
          zxqysmh.docResponseEntId = this.$route.params.id;
          zxqysmh.detailCode = "zxqysmh";
          zxqysmh.detailName = "中小企业声明函";
          zxqysmh.detailType = "2";
          zxqysmh.detailSort = "1";
          zxqysmh.detailContent =  JSON.stringify(this.zxqysmhData);

          saveDetail(zxqysmh).then((result) => {
            if (result.code == 200) {
              this.$message.success("保存成功");
              this.zxqysmhDialogClose();
              // this.getInfoById();
              this.zxqysmhDetailId = result.data.id;
              // 刷新列表
              this.refreshZxqysmh(JSON.parse(result.data.detailContent));
            } else {
              this.$message.error(result.msg);
            }
          });
        }
      })
    },
    refreshZxqysmh(data) {
      // 创建一个新的数组实例，即使内容与旧数组相同
      const newItems = [...data];
      // 重新赋值整个数组
      this.zxqysmhDataArr = newItems;
    },
    zxqysmhDetailDel(detail) {
      var _this = this;
      this.$modal
        .confirm('是否确认删除？')
        .then(function () {
          console.log("删除",detail)
          delZxqysmhDetail(_this.zxqysmhDetailId,detail.qyId).then((res) => {
            console.log("res",res)
            if (res.code == 200) {
              if (res.data) {
                _this.refreshZxqysmh(JSON.parse(res.data));
              } else {
                _this.refreshZxqysmh("");
              }
              _this.$message.success("删除成功");
            } else {
              _this.$message.success(res.msg);
            }
          });
        })
        .catch(() => { });
    },
    detailDel(detail, itemName) {
      var _this = this;
      this.$modal
        .confirm('是否确认删除"' + itemName + '"信息？')
        .then(function () {
          delDetail(detail.id).then((res) => {
            if (res.code == 200) {
              _this.docResponseData[detail.docResponseItemCode] = {};
              _this.$message.success("删除成功");
              for (var key in detail) {
                detail[key] = "";
              }
            } else {
              _this.$message.success(res.msg);
            }
          });
        })
        .catch(() => { });
    },
    kbylbCancel() {
      this.kbylbOpen = false;
    },

    fileDialogOpen(docResponseItemCode, itemName, row, disabled) {
      // console.log("row", row);
      this.fielTitle = itemName + " — 上传附件";
      this.fileItemName = itemName;
      this.fileDetailData.docResponseItemCode = docResponseItemCode;
      if (disabled == "" || disabled == undefined) {
        this.detailNameDisabled = false;
      } else {
        this.detailNameDisabled = disabled;
      }
      if (row != undefined && row.id != undefined) {
        this.fileDetailData.id = row.id;
        this.fileDetailData.detailName = row.detailName;
        this.fileDetailData.filePath = row.filePath;
        this.fileDetailData.detailContent = row.detailContent;
      } else {
        this.fileDetailData.id = "";
        this.fileDetailData.detailName = itemName;
        this.fileDetailData.filePath = "";
        this.fileDetailData.detailContent = "";
      }
      this.fileOpen = true;
    },
    fileDialogClose() {
      this.fileItemName = "";
      this.fileDetailData.id = "";
      this.fileDetailData.docResponseItemCode = "";
      this.fileDetailData.detailName = "";
      this.fileDetailData.filePath = "";
      this.fileOpen = false;
      this.singleFileOpen = false;
      this.textFileOpen = false;
      this.kbylbOpen = false;
      this.confirmOpen = false;
    },
    fileDetailSave() {
      var _this = this;
      var detail = {};
      detail.id = this.fileDetailData.id;
      detail.docResponseItemId =
        this.docResponseItemMap[this.fileDetailData.docResponseItemCode];
      detail.docResponseItemCode = this.fileDetailData.docResponseItemCode;
      detail.docResponseEntId = this.$route.params.id;
      detail.detailCode = this.fileDetailData.docResponseItemCode;
      detail.detailName = this.fileDetailData.detailName;
      detail.detailContent =  JSON.stringify(this.fileDetailData.detailContent);
      detail.detailType = "2";
      const typeStr = Object.prototype.toString.call(
        this.docResponseData[this.fileDetailData.docResponseItemCode]
      );
      var dataType = typeStr.slice(8, -1).toLowerCase();
      if (dataType == "object") {
        detail.detailSort = 1;
      } else if (dataType == "array") {
        detail.detailSort =
          this.docResponseData[this.fileDetailData.docResponseItemCode] ==
          undefined
            ? 1
            : this.docResponseData[this.fileDetailData.docResponseItemCode]
            .length + 1;
      }

      if (this.fileDetailData.filePath == null || this.fileDetailData.filePath == "") {
        this.$message.warning("请上传文件");
        return;
      } else {
        detail.filePath = this.fileDetailData.filePath;
      }
      // console.log("detail++++",detail)
      saveDetail(detail).then((result) => {
        if (result.code == 200) {
          var di = result.data;
          if (
            _this.fileDetailData.id == undefined ||
            _this.fileDetailData.id == ""
          ) {
            _this.fileDetailData.id = di.id;
            var d = {
              id: di.id,
              docResponseItemCode: _this.fileDetailData.docResponseItemCode,
              detailName: _this.fileDetailData.detailName,
              filePath: _this.fileDetailData.filePath,
            };
            if (dataType == "object") {
              _this.docResponseData[_this.fileDetailData.docResponseItemCode] =
                di;
            } else if (dataType == "array") {
              _this.docResponseData[
                _this.fileDetailData.docResponseItemCode
                ].push(di);
            }
          } else {
            if (dataType == "object") {
              _this.docResponseData[_this.fileDetailData.docResponseItemCode] =
                di;
            } else if (dataType == "array") {
              for (var index in _this.docResponseInfo.detailMap[
                _this.fileDetailData.docResponseItemCode
                ]) {
                var res =
                  _this.docResponseInfo.detailMap[
                    _this.fileDetailData.docResponseItemCode
                    ][index];
                if (res.id == _this.fileDetailData.id) {
                  res.detailName = _this.fileDetailData.detailName;
                  res.filePath = _this.fileDetailData.filePath;
                  res.detailContent = _this.fileDetailData.detailContent;
                }
              }
            }
          }
          _this.$message.success("保存成功");
          _this.fileDialogClose();
          _this.getInfoById();
        } else {
          _this.$message.error(result.msg);
        }
      });
    },
    removeFileDetail(detail, index, itemName) {
      var _this = this;
      this.$modal
        .confirm('是否确认删除"' + itemName + "  " + detail.detailName + '"？')
        .then(function () {
          delDetail(detail.id).then((res) => {
            if (res.code == 200) {
              _this.docResponseData[detail.docResponseItemCode].splice(
                index,
                1
              );
              _this.$message.success("删除成功");
              _this.getInfoById();
            } else {
              _this.$message.success(res.msg);
            }
          });
        })
        .catch(() => { });
    },

    singleFileDialogOpen(docResponseItemCode, itemName, row) {
      this.fielTitle = itemName + " — 上传附件";
      this.fileItemName = itemName;
      this.fileDetailData.docResponseItemCode = docResponseItemCode;

      this.fileDetailData.id = row.id;
      this.fileDetailData.detailName = itemName;
      this.fileDetailData.filePath = row.filePath;
      this.fileDetailData.detailContent = row.detailContent;
      this.singleFileOpen = true;
    },
    singleFileDialogClose() {
      this.fileItemName = "";
      this.fileDetailData.id = "";
      this.fileDetailData.docResponseItemCode = "";
      this.fileDetailData.detailName = "";
      this.fileDetailData.filePath = "";
      this.fileDetailData.detailContent = "";
      this.singleFileOpen = false;
    },
    textFileDialogOpen(docResponseItemCode, itemName, row) {
      this.fielTitle = itemName + " — 填报内容";
      this.fileItemName = itemName;
      this.fileDetailData.docResponseItemCode = docResponseItemCode;

      this.fileDetailData.id = row.id;
      this.fileDetailData.detailName = itemName;
      this.fileDetailData.filePath = row.filePath;
      this.fileDetailData.detailContent = row.detailContent;
      this.textFileOpen = true;
    },
    textFileDialogOpen_swbf(docResponseItemCode, itemName, row) {
      this.fielTitle = itemName + " — 填报内容";
      this.fileItemName = itemName;
      this.fileDetailData.docResponseItemCode = docResponseItemCode;
      if (row.length == 0) {
        this.fileDetailData.id = "";
        this.fileDetailData.filePath = "";
        this.fileDetailData.detailContent = "";
      } else {
        this.fileDetailData.id = row[0].id;
        this.fileDetailData.filePath = row[0].filePath;
        this.fileDetailData.detailContent = row[0].detailContent;
      }
      this.fileDetailData.detailName = itemName;
      this.textFileOpen = true;
    },
    isBusinessPartItemCode(item) {
      if (item.length > 0) {
        return true;
      } else {
        return false;
      }
    },
    textFileDialogClose() {
      this.fileItemName = "";
      this.fileDetailData.id = "";
      this.fileDetailData.docResponseItemCode = "";
      this.fileDetailData.detailName = "";
      this.fileDetailData.filePath = "";
      this.fileDetailData.detailContent = "";
      this.textFileOpen = false;
    },
    memberFileDialogOpen(docResponseItemCode, itemName, row) {
      this.fielTitle = itemName + " — 上传附件";
      this.npxmgljgData.docResponseItemCode = docResponseItemCode;

      if (row != undefined) {
        this.npxmgljgData.id = row.id;
        this.npxmgljgData.memberName = row.memberName;
        this.npxmgljgData.memberDuty = row.memberDuty;
        this.npxmgljgData.filePath = row.filePath;
      } else {
        this.npxmgljgData.id = "";
        this.npxmgljgData.memberName = "";
        this.npxmgljgData.memberDuty = "";
        this.npxmgljgData.filePath = "";
      }
      this.memberFileOpen = true;
    },
    memberFileDialogClose() {
      this.npxmgljgData.id = "";
      this.npxmgljgData.memberName = "";
      this.npxmgljgData.memberDuty = "";
      this.npxmgljgData.filePath = "";
      this.memberFileOpen = false;
    },
    memberFileDetailSave() {
      var _this = this;
      var detail = {};
      detail.id = this.fileDetailData.id;
      detail.docResponseItemId =
        this.docResponseItemMap[this.fileDetailData.docResponseItemCode];
      detail.docResponseItemCode = this.fileDetailData.docResponseItemCode;
      detail.docResponseEntId = this.$route.params.id;
      detail.detailCode = this.fileDetailData.docResponseItemCode;
      detail.detailName = this.fileDetailData.detailName;
      detail.detailContent =  JSON.stringify(this.fileDetailData.detailContent);
      detail.detailType = "2";
      const typeStr = Object.prototype.toString.call(
        this.docResponseData[this.fileDetailData.docResponseItemCode]
      );
      var dataType = typeStr.slice(8, -1).toLowerCase();
      if (dataType == "object") {
        detail.detailSort = 1;
      } else if (dataType == "array") {
        detail.detailSort =
          this.docResponseData[this.fileDetailData.docResponseItemCode] ==
          undefined
            ? 1
            : this.docResponseData[this.fileDetailData.docResponseItemCode]
            .length + 1;
      }

      detail.filePath = this.fileDetailData.filePath;

      saveDetail(detail).then((result) => {
        if (result.code == 200) {
          var di = result.data;
          if (
            _this.fileDetailData.id == undefined ||
            _this.fileDetailData.id == ""
          ) {
            _this.fileDetailData.id = di.id;
            var d = {
              id: di.id,
              docResponseItemCode: _this.fileDetailData.docResponseItemCode,
              detailName: _this.fileDetailData.detailName,
              filePath: _this.fileDetailData.filePath,
            };
            if (dataType == "object") {
              _this.docResponseData[_this.fileDetailData.docResponseItemCode] =
                di;
            } else if (dataType == "array") {
              _this.docResponseData[
                _this.fileDetailData.docResponseItemCode
                ].push(di);
            }
          } else {
            if (dataType == "object") {
              _this.docResponseData[_this.fileDetailData.docResponseItemCode] =
                di;
            } else if (dataType == "array") {
              for (var index in _this.docResponseInfo.detailMap[
                _this.fileDetailData.docResponseItemCode
                ]) {
                var res =
                  _this.docResponseInfo.detailMap[
                    _this.fileDetailData.docResponseItemCode
                    ][index];
                if (res.id == _this.fileDetailData.id) {
                  res.detailName = _this.fileDetailData.detailName;
                  res.filePath = _this.fileDetailData.filePath;
                  res.detailContent = _this.fileDetailData.detailContent;
                }
              }
            }
          }
          _this.$message.success("保存成功");
          _this.fileDialogClose();
          _this.getInfoById();
        } else {
          _this.$message.error(result.msg);
        }
      });
    },
    removeMemberFileDetail(detail, index, itemName) {
      var _this = this;
      this.$modal
        .confirm('是否确认删除"' + itemName + "  " + detail.detailName + '"？')
        .then(function () {
          delDetail(detail.id).then((res) => {
            if (res.code == 200) {
              _this.docResponseData[detail.docResponseItemCode].splice(
                index,
                1
              );
              _this.$message.success("删除成功");
            } else {
              _this.$message.success(res.msg);
            }
          });
        })
        .catch(() => { });
    },
  },
};
</script>
<style>
.title {
  font-size: 18px;
  font-weight: bold;
}
.custom-confirm .el-message-box__message {
  white-space: pre-wrap;
}
</style>
