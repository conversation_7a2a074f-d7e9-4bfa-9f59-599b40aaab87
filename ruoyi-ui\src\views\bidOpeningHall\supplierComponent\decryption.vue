<!-- 供应商投标人公示 -->
<template>
  <div class="decryption" v-loading="loading">
    <el-table :data="decryption" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
      <el-table-column type="index" label="序号" width="100">
      </el-table-column>
      <el-table-column prop="bidderName" label="投标供应商">
      </el-table-column>
      <el-table-column prop="attachments[0].fileMd5" label="投标文件识别码">
      </el-table-column>
      <el-table-column prop="busiBidderInfo.decodeFlag" label="解密状态" width="150">
        <template slot-scope="scope">
          <div v-if="scope.row.busiBidderInfo.decodeFlag == 1">已解密</div>
          <div v-if="scope.row.busiBidderInfo.decodeFlag == 0" style="color:#176ADB">未解密</div>
          <div v-if="scope.row.busiBidderInfo.decodeFlag == -1" style="color:#ff0000">解密失败</div>
        </template>
      </el-table-column>
    </el-table>
    <div class="decryption-line-one">
      <div class="decryption-countdown">
        <el-statistic format="解密结束倒计时：HH小时mm分ss秒" :value="deadline" time-indices>
        </el-statistic>
      </div>
    </div>
    <div class="decryption-line-three">
      <el-button v-show="show" class="decryption-button" @click="dialogVisible = true">解密</el-button>
    </div>

    <el-dialog title="解密" :visible.sync="dialogVisible" width="30%">
      <el-input v-model="password" placeholder="请输入二级密码"  type="password" show-password></el-input>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="tender_decryption">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
//这里可以导入其他文件（比如：组件，工具js，第三方插件js，json文件，图片文件等等）
//例如：import 《组件名称》 from '《组件路径》';
import { dataList, suppliersDecryption } from "@/api/onlineBidOpening/info";
import { formatDateOption } from "@/utils/index";
import { listRecord } from "@/api/operation/record";
export default {
  //import引入的组件需要注入到对象中才能使用
  components: {},
  props: {
    projectInfo: {},
    userInfo: "",
  },
  data() {
    //这里存放数据
    return {
      loading: true,

      decryption: [],
      headStyle: {
        "text-align": "center",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
      },
      cellStyle: {
        "text-align": "center",
        height: "90px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },

      dialogVisible: false,
      show: false,
      deadline: "",

      password: "",
    };
  },
  //监听属性 类似于data概念
  computed: {},
  //监控data中的数据变化
  watch: {},
  //方法集合
  methods: {
    // 初始化投标人公示+标书解密列表+唱标列表
    initdataList() {
      dataList(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.decryption = response.data;
          this.loading = false;
          // // 如果当前用户在投标人列表中且状态为已签到、当前节点为标书解密，则按钮可点击
        }
      });
    },
    // 标书解密
    tender_decryption() {
      const result = this.decryption.find(
        (item) => item.createBy === this.userInfo.userName
      );
      const data = {
        bidderInfoId: result.busiBidderInfo.bidderInfoId,
        decodeFlag: 1,
        bidderId: result.bidderId,
        projectId: this.$route.query.projectId,
        decodeTime: formatDateOption(new Date(), "datetime"),
        supplierKey: this.password,
        biddingId: result.biddingId,
      };
      suppliersDecryption(data).then((response) => {
        if (response.code == 200) {
          this.$emit("sendMessage", "supDecrytion");
          this.dialogVisible = false;
          this.show = false;
        } else {
          this.$modal.msgwarning(msg);
        }
      });
    },
    formatterDeleteStatus(row, column, cellValue, index) {
      if (cellValue === 1) {
        return "已解密";
      } else if (cellValue === 0) {
        return "未解密";
      } else if (cellValue === -1) {
        return "解密失败";
      }
    },
    // 获取解密结束时间
    getDecryptionTime() {
      listRecord({
        projectId: this.$route.query.projectId,
        operationType: 3,
      }).then((response) => {
        if (response.code == 200) {
          if (response.rows.length != 0) {
            console.log(response.rows[0]);
            this.deadline = new Date(response.rows[0].decryptionTime);
            const foundBidder = this.decryption.find(
              (item) => item.bidderName === this.userInfo.ent.entName
            );
            // 判断解密按钮是否显示
            // if (
            //   new Date() < this.deadline &&
            //   foundBidder.busiBidderInfo.decodeFlag != 1
            // ) {
            this.show = true;
            // }
          }
        }
      });
    },
  },
  //生命周期 - 创建完成（可以访问当前this实例）
  created() { },
  //生命周期 - 挂载完成（可以访问DOM元素）
  mounted() {
    this.initdataList();
    this.getDecryptionTime();
  },
  beforeCreate() { }, //生命周期 - 创建之前
  beforeMount() { }, //生命周期 - 挂载之前
  beforeUpdate() { }, //生命周期 - 更新之前
  updated() { }, //生命周期 - 更新之后
  beforeDestroy() { }, //生命周期 - 销毁之前
  destroyed() { }, //生命周期 - 销毁完成
  activated() { }, //如果页面有keep-alive缓存功能，这个函数会触发
};
</script>
<style lang="scss" scoped>
/*@import url()*/
.decryption {
  padding: 25px 25px;
  .decryption-line-one {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 25px 0;
    .decryption-countdown {
      width: 50%;
      height: 105px;

      background: #176adb;
      display: flex;
      align-items: center;
      justify-content: center;
      ::v-deep .number {
        color: #fff;
        font-weight: 700;
      }
    }
  }
  .decryption-line-three {
    display: flex;
    align-items: center;
    justify-content: center;
    .decryption-button {
      width: 164px;
      height: 45px;
      background: #176adb;

      color: #fff;
      font-weight: 700;
    }
  }
}
</style>
