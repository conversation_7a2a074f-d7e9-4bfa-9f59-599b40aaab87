import request from '@/utils/request'

// 查询成交通知书列表
export function listAdvice(query) {
  return request({
    url: '/bidder/advice/list',
    method: 'get',
    params: query
  })
}

// 查询成交通知书详细
export function getAdvice(noteId) {
  return request({
    url: '/bidder/advice/' + noteId,
    method: 'get'
  })
}

// 新增成交通知书
export function addAdvice(data) {
  return request({
    url: '/bidder/advice',
    method: 'post',
    data: data
  })
}

// 修改成交通知书
export function updateAdvice(data) {
  return request({
    url: '/bidder/advice',
    method: 'put',
    data: data
  })
}

// 修改成交通知书
export function busiProcess(data) {
  return request({
    url: '/bidder/advice/busiProcess',
    method: 'post',
    data: data
  })
}

// 删除成交通知书
export function delAdvice(noteId) {
  return request({
    url: '/bidder/advice/' + noteId,
    method: 'delete'
  })
}
