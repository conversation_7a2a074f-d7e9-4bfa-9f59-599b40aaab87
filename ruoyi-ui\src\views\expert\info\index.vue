<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="140px"
    >
      <el-form-item label="姓名" prop="expertName">
        <el-input
          v-model="queryParams.expertName"
          placeholder="请输入姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="现从事专业" prop="expertCareer">
        <el-cascader
          style="width: 100%"
          filterable
          clearable
          :props="cascaderSetting"
          v-model="queryParams.expertCareer"
          :options="getTreeDataOptions(7)"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="专家类别" prop="expertType">
        <el-select v-model="queryParams.expertType" placeholder="请选择专家类别" clearable>
          <el-option
            v-for="dict in dict.type.busi_expert_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="手机号码" prop="expertPhone">
        <el-input
          v-model="queryParams.expertPhone"
          placeholder="请输入手机号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="职称/执业资格" prop="expertTitles">
        <el-cascader
          @keyup.enter.native="handleQuery"
          style="width: 100%"
          filterable
          clearable
          :props="cascaderSetting"
          v-model="queryParams.expertTitles"
          :options="getTreeDataOptions(4)"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="行业分类" prop="expertIndustryClassify">
        <el-cascader
          @keyup.enter.native="handleQuery"
          v-model="queryParams.expertIndustryClassify"
          style="width: 100%"
          filterable
          clearable
          :props="cascaderSetting"
          :options="getTreeDataOptions(5)"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="证件类型" prop="expertCertificateType">
        <el-select
          v-model="queryParams.expertCertificateType"
          placeholder="请选择证件类型"
          clearable
        >
          <el-option
            v-for="dict in dict.type.busi_id_card_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="证件号码" prop="expertCertificateCode">
        <el-input
          v-model="queryParams.expertCertificateCode"
          placeholder="请输入证件号码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所学专业" prop="expertMajorStudy">
        <el-cascader
          style="width: 100%"
          filterable
          @keyup.enter.native="handleQuery"
          clearable
          :props="cascaderSetting"
          v-model="queryParams.expertMajorStudy"
          :options="getTreeDataOptions(1)"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="职称等级" prop="expertTitlesGrade">
        <el-select
          v-model="queryParams.expertTitlesGrade"
          placeholder="请选择职称等级"
          clearable
        >
          <el-option
            v-for="dict in dict.type.busi_expert_titles_grade"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="主评区域" prop="expertMainEvaluatArea">
        <el-cascader
          style="width: 100%"
          filterable
          @keyup.enter.native="handleQuery"
          clearable
          :props="cascaderSetting"
          v-model="queryParams.expertMainEvaluatArea"
          :options="getTreeDataOptions(9)"
        ></el-cascader>
      </el-form-item>
      <el-form-item label="状态" prop="expertStatus">
        <el-select
          v-model="queryParams.expertStatus"
          placeholder="请选择状态"
          clearable
        >
          <el-option
            v-for="dict in dict.type.busi_expert_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['expert:info:add']"
          >新增</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['expert:info:edit']"
          >修改</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['expert:info:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['expert:info:export']"
          >导出</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-upload2"
          size="mini"
          @click="handleImport"
          v-hasPermi="['expert:info:import']"
          >导入</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="infoList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column label="姓名" align="center" prop="expertName" />
      <el-table-column label="专家类别" align="center" prop="expertType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_expert_type" :value="scope.row.expertType"/>
        </template>
      </el-table-column>
      <el-table-column label="手机号码" align="center" prop="expertPhone" />
      <el-table-column label="证件类型" align="center" prop="expertCertificateType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.busi_id_card_type" :value="scope.row.expertCertificateType"/>
        </template>
      </el-table-column>
      <el-table-column
        label="证件号码"
        align="center"
        prop="expertCertificateCode"
      />
      <el-table-column label="照片" align="center" prop="expertPhoto" width="100">
        <template slot-scope="scope">
          <image-preview :src="scope.row.expertPhoto" :width="50" :height="50"/>
        </template>
      </el-table-column>
      <el-table-column label="职称等级" align="center" prop="expertTitlesGrade">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_expert_titles_grade"
            :value="scope.row.expertTitlesGrade"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="现工作单位"
        align="center"
        prop="expertOrganization"
      />
      <el-table-column
        label="毕业学校"
        align="center"
        prop="expertGraduationSchool"
      />
      <el-table-column label="现任职务" align="center" prop="expertDuty" />
      <el-table-column
        label="通信地址"
        align="center"
        prop="expertMailAddress"
      />
      <el-table-column label="工作年限" align="center" prop="expertWorkYears" />
      <el-table-column label="电子邮箱" align="center" prop="expertEmail" />
      <el-table-column label="邮编" align="center" prop="expertPostalCode" />
      <el-table-column label="状态" align="center" prop="expertStatus">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_expert_status"
            :value="scope.row.expertStatus"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['expert:info:edit']"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['expert:info:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改专家信息对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      :title="title"
      :visible.sync="open"
      width="80%"
      append-to-body
    >
      <el-form ref="form" :model="form" :rules="rules" label-width="140px">
        <el-form-item label="姓名" prop="expertName">
          <el-input v-model="form.expertName" placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="专家类别" prop="expertType">
          <el-select v-model="form.expertType" placeholder="请选择专家类别">
            <el-option
              v-for="dict in dict.type.busi_expert_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件类型" prop="expertCertificateType">
          <el-select v-model="form.expertCertificateType" placeholder="请选择证件类型">
            <el-option
              v-for="dict in dict.type.busi_id_card_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="证件号码" prop="expertCertificateCode">
          <el-input
            v-model="form.expertCertificateCode"
            placeholder="请输入证件号码"
          />
        </el-form-item>
        <el-form-item label="性别" prop="expertSex">
          <el-select v-model="form.expertSex" placeholder="请选择性别">
            <el-option
              v-for="dict in dict.type.sys_user_sex"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="政治面貌" prop="expertPoliticalOutlook">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertPoliticalOutlook"
            :options="getTreeDataOptions(0)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="最高学历" prop="expertDegree">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertDegree"
            :options="getTreeDataOptions(3)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="毕业学校" prop="expertGraduationSchool">
          <el-input
            v-model="form.expertGraduationSchool"
            placeholder="请输入毕业学校"
          />
        </el-form-item>
        <el-form-item label="现从事专业" prop="expertCareer">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertCareer"
            :options="getTreeDataOptions(7)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="现工作单位" prop="expertOrganization">
          <el-input
            v-model="form.expertOrganization"
            placeholder="请输入现工作单位"
          />
        </el-form-item>
        <el-form-item label="职称/执业资格" prop="expertTitles">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertTitles"
            :options="getTreeDataOptions(4)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="职称等级" prop="expertTitlesGrade">
          <el-select
            v-model="form.expertTitlesGrade"
            placeholder="请选择职称等级"
          >
            <el-option
              v-for="dict in dict.type.busi_expert_titles_grade"
              :key="dict.value"
              :label="dict.label"
              :value="parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="工作年限" prop="expertWorkYears">
          <el-input
            v-model="form.expertWorkYears"
            placeholder="请输入工作年限"
          />
        </el-form-item>
        <el-form-item label="行业分类" prop="expertIndustryClassify">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertIndustryClassify"
            :options="getTreeDataOptions(5)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="主评区域" prop="expertMainEvaluatArea">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertMainEvaluatArea"
            :options="getTreeDataOptions(9)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="评审品目" prop="expertEvaluationItems">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="{ multiple: true, ...cascaderSetting }"
            v-model="form.expertEvaluationItems"
            :options="getTreeDataOptions(2)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="现任职务" prop="expertDuty">
          <el-input v-model="form.expertDuty" placeholder="请输入现任职务" />
        </el-form-item>
        <el-form-item label="手机号码" prop="expertPhone">
          <el-input v-model="form.expertPhone" placeholder="请输入手机号码" />
        </el-form-item>
        <el-form-item label="通信地址" prop="expertMailAddress">
          <el-input
            v-model="form.expertMailAddress"
            placeholder="请输入通信地址"
          />
        </el-form-item>

        <el-form-item label="出生日期" prop="expertBirthday">
          <el-date-picker
            clearable
            v-model="form.expertBirthday"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择出生日期"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="毕业时间" prop="expertGraduactionTime">
          <el-date-picker
            clearable
            v-model="form.expertGraduactionTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择毕业时间"
          >
          </el-date-picker>
        </el-form-item>

        <el-form-item label="电子邮箱" prop="expertEmail">
          <el-input v-model="form.expertEmail" placeholder="请输入电子邮箱" />
        </el-form-item>

        <el-form-item label="民族" prop="expertNation">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertNation"
            :options="getTreeDataOptions(6)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="照片" prop="expertPhoto">
          <image-upload v-model="form.expertPhoto"/>
        </el-form-item>
        <el-form-item label="所学专业" prop="expertMajorStudy">
          <el-cascader
            style="width: 100%"
            filterable
            clearable
            :props="cascaderSetting"
            v-model="form.expertMajorStudy"
            :options="getTreeDataOptions(1)"
          ></el-cascader>
        </el-form-item>
        <el-form-item label="常住地" prop="expertPlace">
          <el-input v-model="form.expertPlace" placeholder="请输入常住地" />
        </el-form-item>

        <el-form-item label="邮编" prop="expertPostalCode">
          <el-input v-model="form.expertPostalCode" placeholder="请输入邮编" />
        </el-form-item>

        <el-form-item label="单位意见" prop="expertOpinion">
          <el-input v-model="form.expertOpinion" placeholder="请输入单位意见" />
        </el-form-item>
        <el-form-item label="状态" prop="expertStatus">
          <el-radio-group v-model="form.expertStatus">
            <el-radio
              v-for="dict in dict.type.busi_expert_status"
              :key="dict.value"
              :label="parseInt(dict.value)"
              >{{ dict.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
        <el-form-item label="开户行" prop="expertOpeningBank">
          <el-input
            v-model="form.expertOpeningBank"
            placeholder="请输入开户行"
          />
        </el-form-item>
        <el-form-item label="户名" prop="expertAccountName">
          <el-input v-model="form.expertAccountName" placeholder="请输入户名" />
        </el-form-item>
        <el-form-item label="银行账号" prop="expertAccountCode">
          <el-input
            v-model="form.expertAccountCode"
            placeholder="请输入银行账号"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 分类导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="400px"
      append-to-body
    >
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport"
        :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress"
        :on-success="handleFileSuccess"
        :auto-upload="false"
        drag
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div class="el-upload__tip text-center" slot="tip">
          <div class="el-upload__tip" slot="tip">
            <el-checkbox v-model="upload.updateSupport" />
            是否更新已经存在的数据
          </div>
          <span>仅允许导入xls、xlsx格式文件。</span>
          <el-link
            type="primary"
            :underline="false"
            style="font-size: 12px; vertical-align: baseline"
            @click="importTemplate"
            >下载模板</el-link
          >
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
        <el-button @click="upload.open = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listInfo,
  getInfo,
  delInfo,
  addInfo,
  updateInfo,
} from "@/api/expert/info";
import { getToken } from "@/utils/auth";
export default {
  name: "Info",
  dicts: [
    "busi_expert_status",
    "busi_expert_titles_grade",
    "sys_user_sex",
    "busi_political_outlook",
    "busi_id_card_type",
    "busi_expert_type"
  ],
  data() {
    return {
      //树形字典
      dataTreeOptionsMap: new Map(),
      dataTreeMap: new Map(),
      cascaderSetting: { value: "id", label: "name", children: "childrens" },

      // 弹出层标题
      // 用户导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: "",
        // 是否禁用上传
        isUploading: false,
        // 是否更新已经存在的用户数据
        updateSupport: 0,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/expert/info/importData",
      },
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 专家信息表格数据
      infoList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        expertSex: null,
        expertName: null,
        expertPoliticalOutlook: null,
        expertGraduationSchool: null,
        expertCareer: null,
        expertDuty: null,
        expertPhone: null,
        expertMailAddress: null,
        expertWorkYears: null,
        expertCertificateType: null,
        expertBirthday: null,
        expertDegree: null,
        expertGraduactionTime: null,
        expertOrganization: null,
        expertTitles: null,
        expertEmail: null,
        expertIndustryClassify: null,
        expertCertificateCode: null,
        expertNation: null,
        expertPhoto: null,
        expertMajorStudy: null,
        expertPlace: null,
        expertTitlesGrade: null,
        expertPostalCode: null,
        expertMainEvaluatArea: null,
        expertEvaluationItems: null,
        expertOpinion: null,
        expertStatus: null,
        expertOpeningBank: null,
        expertAccountName: null,
        expertAccountCode: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        expertName: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        expertPoliticalOutlook: [
          { required: true, message: "政治面貌不能为空", trigger: "change" },
        ],
        expertPhone: [
          { required: true, message: "手机号码不能为空", trigger: "blur" },
        ],
        expertWorkYears: [
          { required: true, message: "工作年限不能为空", trigger: "blur" },
        ],
        expertCertificateType: [
          { required: true, message: "证件类型不能为空", trigger: "change" },
        ],
        expertDegree: [
          { required: true, message: "最高学历不能为空", trigger: "blur" },
        ],
        expertCareer: [
          { required: true, message: "从事专业不能为空", trigger: "blur" },
        ],
        expertOrganization: [
          { required: true, message: "现工作单位不能为空", trigger: "blur" },
        ],
        expertTitles: [
          { required: true, message: "职称/执业资格不能为空", trigger: "blur" },
        ],
        expertIndustryClassify: [
          { required: true, message: "行业分类不能为空", trigger: "blur" },
        ],
        expertCertificateCode: [
          { required: true, message: "证件号码不能为空", trigger: "blur" },
        ],
        expertTitlesGrade: [
          { required: true, message: "职称等级不能为空", trigger: "change" },
        ],
        expertMainEvaluatArea: [
          { required: true, message: "主评区域不能为空", trigger: "blur" },
        ],
        expertEvaluationItems: [
          { required: true, message: "评审品目不能为空", trigger: "blur" },
        ],
        expertStatus: [
          { required: true, message: "状态不能为空", trigger: "change" },
        ],
        delFlag: [
          { required: true, message: "删除标记不能为空", trigger: "blur" },
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" },
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" },
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" },
        ],
        updateBy: [
          { required: true, message: "修改者不能为空", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    this.treeDict
      .getdataTreeOptionsMap({ isEnabled: 1 })
      .then((result) => {
        this.dataTreeOptionsMap = result;
        return this.treeDict.getdataTreeListMap({ isEnabled: 1 });
      })
      .then((result) => {
        this.dataTreeMap = result;
        return this.getList();
      })
      .catch((err) => {
        console.error(" expert/info init err ", err);
      });
  },
  methods: {
    /** 从map取值 */
    getTreeDataOptions(key) {
      return this.dataTreeOptionsMap.get(key)
        ? this.dataTreeOptionsMap.get(key).childrens
        : [];
    },
    /** 查询专家信息列表 */
    getList() {
      this.loading = true;
      this.queryParams = this.voToDto(this.queryParams);
      listInfo(this.queryParams).then((response) => {
        this.infoList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        expertId: null,
        expertSex: null,
        expertName: null,
        expertPoliticalOutlook: null,
        expertGraduationSchool: null,
        expertCareer: null,
        expertDuty: null,
        expertPhone: null,
        expertMailAddress: null,
        expertWorkYears: null,
        expertCertificateType: null,
        expertBirthday: null,
        expertDegree: null,
        expertGraduactionTime: null,
        expertOrganization: null,
        expertTitles: null,
        expertEmail: null,
        expertIndustryClassify: null,
        expertCertificateCode: null,
        expertNation: null,
        expertPhoto: null,
        expertMajorStudy: null,
        expertPlace: null,
        expertTitlesGrade: null,
        expertPostalCode: null,
        expertMainEvaluatArea: null,
        expertEvaluationItems: null,
        expertOpinion: null,
        expertStatus: null,
        expertOpeningBank: null,
        expertAccountName: null,
        expertAccountCode: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.expertId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加专家信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const expertId = row.expertId || this.ids;
      getInfo(expertId).then((response) => {
        this.form = response.data;
        this.form = this.dtoToVo(this.form);
        this.open = true;
        this.title = "修改专家信息";
      });
    },
    dtoToVo(val) {
      if (val.expertEvaluationItems) {
        val.expertEvaluationItems = val.expertEvaluationItems.split(";");
        val.expertEvaluationItems = val.expertEvaluationItems.map((element) => {
          element = element.split(",").map((item) => {
            return parseInt(item);
          });
          return element;
        });
      }
      val.expertCareer = this.checkIsSplit(val.expertCareer);
      val.expertDegree = this.checkIsSplit(val.expertDegree);
      val.expertIndustryClassify = this.checkIsSplit(val.expertIndustryClassify);
      val.expertMainEvaluatArea = this.checkIsSplit(val.expertMainEvaluatArea);
      val.expertNation = this.checkIsSplit(val.expertNation);
      val.expertPoliticalOutlook = this.checkIsSplit(val.expertPoliticalOutlook);
      val.expertTitles = this.checkIsSplit(val.expertTitles);
      return val;
    },
    checkIsSplit(val) {
      if (val) {
        val = val.split(",").map((item) => {
          return parseInt(item);
        });
      }
      return val;
    },
    checkIsJoin(val) {
      if (val) {
        val = val.join(",");
      }
      return val;
    },
    voToDto(val) {
      if (val.expertEvaluationItems) {
        val.expertEvaluationItems = val.expertEvaluationItems.map((element) => {
          element = element.join(",");
          return element;
        });
        val.expertEvaluationItems = val.expertEvaluationItems.join(";");
      }

      val.expertCareer = this.checkIsJoin(val.expertCareer);
      val.expertDegree = this.checkIsJoin(val.expertDegree);
      val.expertIndustryClassify = this.checkIsJoin(val.expertIndustryClassify);
      val.expertMainEvaluatArea = this.checkIsJoin(val.expertMainEvaluatArea);
      val.expertNation = this.checkIsJoin(val.expertNation);
      val.expertPoliticalOutlook = this.checkIsJoin(val.expertPoliticalOutlook);
      val.expertTitles = this.checkIsJoin(val.expertTitles);
      return val;
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          this.form = this.voToDto(this.form);
          if (this.form.expertId != null) {
            updateInfo(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addInfo(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const expertIds = row.expertId || this.ids;
      this.$modal
        .confirm('是否确认删除专家信息编号为"' + expertIds + '"的数据项？')
        .then(function () {
          return delInfo(expertIds);
        })
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "expert/info/export",
        {
          ...this.queryParams,
        },
        `info_${new Date().getTime()}.xlsx`
      );
    },

    /** 导入按钮操作 */
    handleImport() {
      this.upload.title = "专家导入";
      this.upload.open = true;
    },
    /** 下载模板操作 */
    importTemplate() {
      this.download(
        "expert/info/importTemplate",
        {},
        `expert_template_${new Date().getTime()}.xlsx`
      );
    },
    // 文件上传中处理
    handleFileUploadProgress(event, file, fileList) {
      this.upload.isUploading = true;
    },
    // 文件上传成功处理
    handleFileSuccess(response, file, fileList) {
      this.upload.open = false;
      this.upload.isUploading = false;
      this.$refs.upload.clearFiles();
      this.$alert(
        "<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" +
          response.msg +
          "</div>",
        "导入结果",
        { dangerouslyUseHTMLString: true }
      );
      this.getList();
    },
    // 提交上传文件
    submitFileForm() {
      this.$refs.upload.submit();
    },
  },
};
</script>
