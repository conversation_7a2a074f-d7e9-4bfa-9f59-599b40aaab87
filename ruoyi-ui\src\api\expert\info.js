import request from '@/utils/request'

// 查询专家信息列表
export function listInfo(query) {
  return request({
    url: '/expert/info/list',
    method: 'get',
    params: query
  })
}
export function getcgxhzj(query) {
  return request({
    url: '/LoginRequest/getZhuanJia',
    method: 'get',
    params: query
  })
}
// 查询专家信息详细
export function getInfo(expertId) {
  return request({
    url: '/expert/info/' + expertId,
    method: 'get'
  })
}

// 新增专家信息
export function addInfo(data) {
  return request({
    url: '/expert/info',
    method: 'post',
    data: data
  })
}

// 修改专家信息
export function updateInfo(data) {
  return request({
    url: '/expert/info',
    method: 'put',
    data: data
  })
}

// 删除专家信息
export function delInfo(expertId) {
  return request({
    url: '/expert/info/' + expertId,
    method: 'delete'
  })
}
