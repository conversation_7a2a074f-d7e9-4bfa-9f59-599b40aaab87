<template>
  <div class="head">
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :lg="24"
      >
        <div class="head-top">
          <div class="greetings">
            <div style="margin-right: 27px">鹤壁市</div>
            <div>
              你好，欢迎使用限额以下采购交易平台
            </div>
            <!-- <div v-if="userInfo.token">
              你好，欢迎使用限额以下采购交易平台
            </div>
            <div v-else>
              你好，请【<router-link
                :to="todoPage()"
                class="link-type"
              ><span style="color: #fff">登录</span></router-link>】
            </div> -->

          </div>
          <!-- <div class="menu">
            <div
              class="select"
              @click="active('我要采购')"
            >我要采购</div>
            <div
              class="select"
              @click="active('我要入驻')"
            >我要入驻</div>
            <div
              class="select"
              @click="active('商家中心')"
            >商家中心</div>
            <div
              class="select"
              @click="active('开标大厅')"
            >开标大厅</div>
            <div
              class="select"
              @click="active('帮助中心')"
            >帮助中心</div>
          </div> -->
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :lg="24"
      >
        <div class="head-bottom">
          <div class="search">
            <div style="margin-right: 92px; margin-left: 10px">
              <div style="width: 300px; height: 50px;line-height: 50px;text-align: center;font-family: SourceHanSansSC-Heavy;font-weight: 900;font-size: 35px;color: #333333;letter-spacing: 0;">
                线上开标大厅
              </div>
            </div>
          </div>
          <div class="menu">
            <div
              class="select"
              :class="{ active: activeName === '开标准备' }"
              @click="toggleActive('开标准备',1)"
            >
              开标准备
            </div>
            <div
              class="select"
              :class="{ active: activeName === '投标人公示' }"
              @click="toggleActive('投标人公示',2)"
            >
              投标人公示
            </div>
            <div
              class="select"
              :class="{ active: activeName === '标书解密' }"
              @click="toggleActive('标书解密',3)"
            >
              标书解密
            </div>
            <!-- <div
              class="select"
              :class="{ active: activeName === '唱标' }"
              @click="toggleActive('唱标',4)"
            >
              唱标
            </div> -->
            <div
              class="select"
              :class="{ active: activeName === '开标结束' }"
              @click="toggleActive('开标结束',5)"
            >
              开标结束
            </div>
          </div>
          <el-image
            class="background-img"
            :src="background"
            fit="cover"
          ></el-image>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getProjectStatus } from "@/api/onlineBidOpening/info";
export default {
  name: "Head",
  data() {
    return {
      activeName: "",
      title: require("@/assets/images/title.png"),
      background: require("@/assets/images/home-background.png"),
      userInfo: {},
    };
  },
  watch: {
    activeName: function (newVal, oldVal) {
      this.$emit("updateStatus", newVal);
    },
  },
  created() {
    this.userInfo = this.$store.state.user;
  },
  mounted() {
    this.getBidStatus();
  },
  methods: {
    // 获取当前评标室的状态
    getBidStatus() {
      getProjectStatus({
        projectId: this.$route.query.projectId,
      }).then((response) => {
        this.$store.dispatch("updateStatus", {
          key: "agentBidOpenStatus",
          value: response.data.agencyPageStatus,
        });
        var agencyPageStatus = response.data.agencyPageStatus;
        if (response.data.agencyPageStatus == null) {
          agencyPageStatus = 1;
        }
        switch (agencyPageStatus) {
          case 1:
            this.activeName = "开标准备";
            break;
          case 2:
            this.activeName = "投标人公示";
            break;
          case 3:
            this.activeName = "标书解密";
            break;
          case 4:
            this.activeName = "唱标";
            break;
          case 5:
            this.activeName = "开标结束";
            break;
        }
      });
    },
    active(type) {
      if (type != "帮助中心") {
        this.$router.push("/login");
      } else {
        this.$modal.msgSuccess("跳转到" + type + "页面");
      }
    },
    handleCommand(command) {
      this.$modal.msgSuccess("跳转到帮助中心-" + command + "页面");
    },
    toggleActive(activeName, status) {
      // 测试用
      // this.activeName = activeName;
      console.log(this.$store.getters.agentBidOpenStatus, status);
      if (this.$store.getters.agentBidOpenStatus >= status) {
        this.activeName = activeName;
      } else {
        this.$message.warning("未开始");
      }
    },
    todoPage() {
      return {
        path: "/login",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  height: 230px;
  .head-top {
    background: #176adb;

    height: 40px;
    padding: 0 15%;

    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;

    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #ffffff;
    letter-spacing: 0;
    .greetings {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      align-items: center;
    }
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        height: 25px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 17px;
        color: #ffffff;
        letter-spacing: 0;

        margin-right: 28px;
        cursor: pointer;
        .el-dropdown-link {
          cursor: pointer;
          color: #ffffff;
        }
        .el-icon-arrow-down {
          font-size: 12px;
          color: #ffffff;
        }
      }
      .active {
        color: #000;
      }
    }
  }
  .head-bottom {
    height: 190px;
    padding: 0 15%;
    position: relative;
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        width: 110px;
        height: 41px;
        line-height: 41px;
        font-family: SourceHanSansSC-Bold;
        font-weight: 700;
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;
        border-radius: 6px;
        text-align: center;

        margin-right: 60px;
        cursor: pointer;
      }
      .active {
        background: #176adb;
        color: #fff;
      }
    }
    .search {
      height: 148px;
      display: flex;
      align-items: center;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      .query {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;

        margin-right: 30px;
        width: 470px;
        height: 46px;
        .input {
          border-radius: 6px 0 0 6px;
          ::v-deep .el-input__inner {
            border: 2px solid #176adb;
          }
        }
        .button {
          background: #176adb;
          width: 46px;
          padding-right: 0;
          padding-left: 0;
          color: #fff;
          border-radius: 0 6px 6px 0;
          border: 1px solid #176adb;
        }
      }
      .publish {
        width: 166px;
        height: 46px;
        background: #176adb;
        border-radius: 6px;
        color: #ffffff;

        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: justify;
        text-align: center;
      }
    }

    .background-img {
      position: absolute;
      bottom: 0;
      right: 15.7%;
      z-index: -1; /* 将图片放置在其他元素下方 */
    }
  }
}
</style>
