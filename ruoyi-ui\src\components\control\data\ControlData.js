export const ControlType = {
  Signature: "signature",
  Seal: "seal",
  legalPerson: "legalPerson",
  // LineText: "line-text",
  // MultilineText: "multiline-text",
  // Date: "date",
  // SignDate: "sign-date",
  // Number: "number",
};

export const ControlOperationType = {
  template: "template",
  create: "create",
  sign: "sign",
};

export const ControlSize = function (width, height, minWidth, minHeight) {
  this.width = width;
  this.height = height;
  this.minWidth = minWidth;
  this.minHeight = minHeight;
};

export const ControlPosition = function (left, right) {
  this.left = left;
  this.right = right;
};

export const ControlOptins = function (
  type,
  name,
  icon,
  size,
  position,
  placeholder,
  value,
  controlClick,
  zoom,
  move
) {
  this.type = type;
  this.name = name;
  this.icon = icon;
  this.size = size;
  this.position = position;
  this.placeholder = placeholder;
  this.value = value;
  this.controlClick = controlClick;
  this.zoom = zoom;
  this.move = move;
};

export const CanvasZoom = {
  space: 16,
  width: 800,
  height: 1131,
  zoom: 100,
};

export const controlList = [
  {
    icon: "seal",
    name: "电子印章",
    title: "企业签章",
    type: ControlType.Seal,
    placeholder: "",
    value: "",
    zoom: false,
    move: true,
    size: new ControlSize(160, 160, 160, 160),
    position: new ControlPosition(0, 0),
    user: {
      index: -1,
      userName: "",
    },
  },
  {
    icon: "legalPerson",
    name: "电子印章",
    title: "法人签章",
    type: ControlType.legalPerson,
    placeholder: "",
    value: "",
    zoom: false,
    move: true,
    size: new ControlSize(160, 160, 160, 160),
    position: new ControlPosition(0, 0),
    user: {
      index: -1,
      userName: "",
    },
  },
  {
    icon: "signature",
    name: "手写签名",
    title: "个人签名",
    type: ControlType.Signature,
    placeholder: "",
    value: "",
    zoom: false,
    move: true,
    size: new ControlSize(150, 70, 150, 70),
    position: new ControlPosition(0, 0),
    user: {
      index: -1,
      userName: "",
    },
  },
];
