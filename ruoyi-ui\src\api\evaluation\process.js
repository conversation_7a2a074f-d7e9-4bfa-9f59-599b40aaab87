import request from '@/utils/request'

// 查询项目评审进度列表
export function listProcess(query) {
  return request({
    url: '/evaluation/process/list',
    method: 'get',
    params: query
  })
}

// 查询项目评审进度详细
export function getProcess(evaluationProcessId) {
  return request({
    url: '/evaluation/process/' + evaluationProcessId,
    method: 'get'
  })
}

// 新增项目评审进度
export function addProcess(data) {
  return request({
    url: '/evaluation/process',
    method: 'post',
    data: data
  })
}

// 修改项目评审进度
export function updateProcess(data) {
  return request({
    url: '/evaluation/process',
    method: 'put',
    data: data
  })
}

// 删除项目评审进度
export function delProcess(evaluationProcessId) {
  return request({
    url: '/evaluation/process/' + evaluationProcessId,
    method: 'delete'
  })
}
