<template>
  <div class="head">
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :lg="24"
      >
        <div class="head-top">
          <div class="greetings">
            <div style="margin-right: 27px">鹤壁市</div>
            <div>
              你好，欢迎使用限额以下采购交易平台
            </div>
            <!-- <div v-if="userInfo.token">
              你好，欢迎使用限额以下采购交易平台
            </div>
            <div v-else>
              你好，请【<router-link
                :to="todoPage()"
                class="link-type"
              ><span style="color: #fff">登录</span></router-link>】
            </div>
            -->
          </div>
          <!-- <div class="menu">
            <div
              class="select"
              @click="active('我要采购')"
            >我要采购</div>
            <div
              class="select"
              @click="active('我要入驻')"
            >我要入驻</div>
            <div
              class="select"
              @click="active('商家中心')"
            >商家中心</div>
            <div
              class="select"
              @click="active('开标大厅')"
            >开标大厅</div>
            <div
              class="select"
              @click="active('帮助中心')"
            >帮助中心</div>
          </div> -->
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col
        :xs="24"
        :sm="24"
        :lg="24"
      >
        <div class="head-bottom">
          <div class="search">
            <div style="margin-right: 92px; margin-left: 10px">
              <el-image
                :src="title"
                fit="fill"
              ></el-image>
            </div>
            <div class="query">
              <!-- <el-input
                placeholder="搜索供应商服务，如：工程安装"
                v-model="search"
                class="input"
              >
                <el-button
                  class="button"
                  slot="append"
                  icon="el-icon-search"
                ></el-button>
              </el-input> -->
            </div>
            <div>
              <!-- <el-button
                class="publish"
                style=""
              >发布采购公告</el-button> -->
            </div>
          </div>
          <div class="menu">
            <div
              class="select"
              :class="{ active: activeName === '竞采大厅' }"
              @click="toggleActive('竞采大厅')"
            >
              竞采大厅
            </div>
            <div
              class="select"
              :class="{ active: activeName === '服务商品' }"
              @click="toggleActive('服务商品')"
            >
              服务商品
            </div>
            <div
              class="select"
              :class="{ active: activeName === '采购公告' }"
              @click="toggleActive('采购公告')"
            >
              采购公告
            </div>
            <div
              class="select"
              :class="{ active: activeName === '开标大厅' }"
              @click="toggleActive('开标大厅')"
            >
              开标大厅
            </div>
            <div
              class="select"
              :class="{ active: activeName === '曝光台' }"
              @click="toggleActive('曝光台')"
            >
              曝光台
            </div>
            <div
              class="select"
              :class="{ active: activeName === '帮助中心' }"
              @click="toggleActive('帮助中心')"
            >
              帮助中心
            </div>
          </div>
          <el-image
            class="background-img"
            :src="background"
            fit="cover"
          ></el-image>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Head",
  data() {
    return {
      search: "",
      activeName: "",
      title: require("@/assets/images/title.png"),
      background: require("@/assets/images/home-background.png"),
      userinfo: {},
    };
  },
  watch: {
    activeName: function (newVal, oldVal) {
      if (newVal == "竞采大厅") {
        this.$router.push("/home");
      } else if (newVal == "采购公告") {
        this.$router.push({ path: "/list", query: { result: "publicity" } });
      } else if (newVal == "开标大厅") {
        this.$router.push("/bidOpeningHall");
      }
    },
  },
  created() {
    this.userInfo = this.$store.state.user;
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      const currentRoute = this.$router.currentRoute;
      if (currentRoute.path) {
        switch (currentRoute.path) {
          case "/home":
            this.activeName = "竞采大厅";
            break;
          case "/list":
            if (currentRoute.query.result == "publicity") {
              this.activeName = "采购公告";
            } else {
              this.activeName = "";
            }
            break;
          case "/bidOpeningHall":
            this.activeName = "开标大厅";
            break;
        }
      }
    },
    active(type) {
      if (type != "帮助中心") {
        this.$router.push("/login");
      } else {
        this.$modal.msgSuccess("跳转到" + type + "页面");
      }
    },
    handleCommand(command) {
      this.$modal.msgSuccess("跳转到帮助中心-" + command + "页面");
    },
    toggleActive(type) {
      if (type == "服务商品" || type == "曝光台" || type == "帮助中心") {
        this.$modal.msgWarning("暂未开放");
      } else {
        this.activeName = type;
      }
    },
    todoPage() {
      return {
        path: "/login",
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.head {
  height: 230px;
  .head-top {
    background: #176adb;

    height: 40px;
    padding: 0 15%;

    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: nowrap;

    font-family: SourceHanSansSC-Regular;
    font-weight: 400;
    font-size: 17px;
    color: #ffffff;
    letter-spacing: 0;
    .greetings {
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      align-items: center;
    }
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        height: 25px;
        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 17px;
        color: #ffffff;
        letter-spacing: 0;

        margin-right: 28px;
        cursor: pointer;
        .el-dropdown-link {
          cursor: pointer;
          color: #ffffff;
        }
        .el-icon-arrow-down {
          font-size: 12px;
          color: #ffffff;
        }
      }
      .active {
        color: #000;
      }
    }
  }
  .head-bottom {
    height: 190px;
    padding: 0 15%;
    position: relative;
    .menu {
      display: flex;
      flex-wrap: wrap;
      align-content: center;
      justify-content: flex-start;
      align-items: center;
      margin-left: 45px;
      .select {
        width: 80px;
        height: 29px;
        font-family: SourceHanSansSC-Bold;
        font-weight: 700;
        font-size: 18px;
        color: #333333;
        letter-spacing: 0;

        margin-right: 77px;
        cursor: pointer;
      }
      .active {
        color: #176adb;
      }
    }
    .search {
      height: 148px;
      display: flex;
      align-items: center;
      flex-direction: row;
      flex-wrap: nowrap;
      justify-content: flex-start;
      .query {
        display: flex;
        align-items: center;
        flex-wrap: nowrap;
        flex-direction: row;
        justify-content: flex-start;

        margin-right: 30px;
        width: 470px;
        height: 46px;
        .input {
          border-radius: 6px 0 0 6px;
          ::v-deep .el-input__inner {
            border: 2px solid #176adb;
          }
        }
        .button {
          background: #176adb;
          width: 46px;
          padding-right: 0;
          padding-left: 0;
          color: #fff;
          border-radius: 0 6px 6px 0;
          border: 1px solid #176adb;
        }
      }
      .publish {
        width: 166px;
        height: 46px;
        background: #176adb;
        border-radius: 6px;
        color: #ffffff;

        font-family: SourceHanSansSC-Regular;
        font-weight: 400;
        font-size: 16px;
        color: #ffffff;
        letter-spacing: 0;
        text-align: justify;
        text-align: center;
      }
    }

    .background-img {
      position: absolute;
      bottom: 0;
      right: 15.7%;
      z-index: -1; /* 将图片放置在其他元素下方 */
    }
  }
}
</style>
