<template>
  <div class="container">
    <div class="transfer">
      <el-table
        :data="tableData"
        border
        style="width: 100%"
      >
        <el-table-column label="序号">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="functionPoint"
          label="功能点"
        > </el-table-column>
        <el-table-column label="检查结果">
          <template slot-scope="scope">
            <span
              v-if="scope.row.result == 1"
              style="color: red"
            >{{
              scope.row.content
            }}</span>
            <span
              v-else
              style="color: green"
            > {{ scope.row.content }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="item">
      <!-- <div>
        <el-button class="item-button" @click="preview()"
          >预览响应文件</el-button
        >
      </div>
      <div>
        <el-button class="item-button" @click="accredit()">授权签章</el-button>
      </div> -->
      <div>
        <el-button
          class="item-button"
          @click="dialogVisible = true"
        >响应文件签章</el-button>
      </div>
      <div>
        <el-button
          class="item-button"
          @click="generated()"
        >生成响应文件</el-button>
      </div>
    </div>
    <el-dialog
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-input
        v-model="secondaryPassword"
        placeholder="请输入二级密码"
      ></el-input>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="dialogVisible = false"
        >确 定</el-button>
        <el-button @click="dialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>

  </div>
</template>

<script>
import { generateResponseZip } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {
        projectName: "测试项目111",
        projectCode: "3784hjids",
        deadLine: "2024-07-20",
        procurementWey: "竞争性磋商",
        purchasName: "测试采购人",
        purchasTel: "",
        agencyName: "测试代理机构",
        agencyTel: "",
      },
      uinfo: {},
      uitem: {},
      extUitem: {},
      projectId: {},
      tableData: [
        {
          functionPoint: "项目基本信息",
          result: 0,
          content: "正常",
        },
        {
          functionPoint: "响应文件正文",
          result: 0,
          content: "正常",
        },
      ],
      dialogVisible: false,
      secondaryPassword: "",
    };
  },
  methods: {
    // TODO 初始化信息
    init(projectId, projectInfo, uitem, uinfo, extUitem) {
      this.projectId = this.$route.query.projectId;
      this.projectInfo = projectInfo;
      this.uinfo = uinfo;
      this.uitem = uitem;
      this.extUitem = extUitem;
      let projectInfoJsonStr = localStorage.getItem(
        `fileResponseProjectInfo_${projectId}`
      );
      if (projectInfoJsonStr) {
        this.projectInfo = JSON.parse(projectInfoJsonStr);
      }
      this.checkIsOver(this.projectInfo);
    },
    checkIsOver(projectInfo) {
      if (projectInfo) {
        if (projectInfo.bidText && projectInfo.bidText.length > 0) {
          this.tableData[1].result = 0;
          this.tableData[1].content = "正常";
        } else {
          this.tableData[1].result = 1;
          this.tableData[1].content = "未上传响应文件正文";
        }
        if (
          projectInfo.bidInformation &&
          projectInfo.bidInformation.length > 0
        ) {
          let isAllTrue = projectInfo.bidInformation.every((item) =>
            this.isNotEmpty(item.value)
          );
          if (isAllTrue) {
            this.tableData[0].result = 0;
            this.tableData[0].content = "正常";
          } else {
            this.tableData[0].result = 1;
            this.tableData[0].content = "部分信息为空";
          }
        }
      } else {
        this.$message.error("项目信息为空");
      }
    },
    isNotEmpty(value) {
      value = value ? value : "";
      // 去掉两边空格所有空格
      value = value.replace(/^\s+|\s+$/g, "");
      // 去掉所有空格和回车换行符
      value = value.replace(/\s+/g, " ");
      // 去掉所有中文空格
      value = value.replace(/[\u3000]/g, "");
      return (
        value !== null &&
        value !== undefined &&
        value !== "" &&
        value !== "" &&
        value !== 0 &&
        value !== false
      );
    },
    // 预览响应文件
    preview() {},
    // 授权签章
    accredit() {},
    downLoadFile(path) {
      path = path.replace(/\\/g, "/");
      this.$download.tbwj(
        `/documents/uinfo/downloadProjectResponseFile?responseFilePath=${path}`,
        `project_response_${this.projectInfo.projectId}.tbwj`
      );
    },
    // 生成响应文件
    generated() {
      let isAllTrue = this.tableData.every((item) => item.result === 0);
      if (isAllTrue) {
        this.getDicts("busi_tender_mode")
          .then((result) => {
            if (result.code === 200) {
              let a = result.data.find(
                (item) => item.dictValue == this.projectInfo.tenderMode
              );
              this.projectInfo.tenderModeStr = a.dictLabel;
              this.projectInfo.projectId = this.$route.query.projectId;
              this.$download.tbwjPost(
                `/documents/uinfo/generateResponseZip`,
                `project_response_${this.projectInfo.projectId}.tbwj`,
                this.projectInfo
              );

              // generateResponseZip(this.projectInfo)
              //   .then((result) => {
              //     if (result.code === 200) {
              //       // this.downLoadFile(result.data);
              //       this.$message.success("生成成功");
              //     } else {
              //       this.$message.error("生成失败");
              //     }
              //   })
              //   .catch((err) => {});
            } else {
              this.$message.error("获取字典数据失败");
            }
          })
          .catch((err) => {});
      } else {
        this.$message.warning("请完善信息");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}
.transfer {
  width: 70%;
  margin-top: 40px;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 150px;
    margin-right: 20px;
    text-align: left;
  }
  .item-content {
    min-width: 100px;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1);
  color: #fff;
  &:hover {
    color: #fff;
  }
}
</style>
