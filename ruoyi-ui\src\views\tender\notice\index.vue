<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      size="small"
      :inline="true"
      v-show="showSearch"
      label-width="68px"
    >
      <el-form-item label="公告名称" prop="noticeName">
        <el-input
          v-model="queryParams.noticeName"
          placeholder="请输入公告名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <!--      <el-form-item label="公告id" prop="noticeId">
        <el-input
          v-model="queryParams.noticeId"
          placeholder="请输入公告id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="公告代码" prop="noticeCode">
        <el-input
          v-model="queryParams.noticeCode"
          placeholder="请输入公告代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>

      <el-form-item label="公告类型 (1采购公告 2变更公告)" prop="noticeType">
        <el-select v-model="queryParams.noticeType" placeholder="请选择公告类型 (1采购公告 2变更公告)" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_notice_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="公告开始时间">
        <el-date-picker
          v-model="daterangeNoticeStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="公告结束时间">
        <el-date-picker
          v-model="daterangeNoticeEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="开标时间">
        <el-date-picker
          v-model="daterangeBidOpeningTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="开标结束时间">
        <el-date-picker
          v-model="daterangeBidOpeningEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="评标时间">
        <el-date-picker
          v-model="daterangeBidEvaluationTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="评标结束时间">
        <el-date-picker
          v-model="daterangeBidEvaluationEndTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="项目开始时间">
        <el-date-picker
          v-model="daterangeProjectStartTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="项目期限 (天)" prop="projectDeadline">
        <el-input
          v-model="queryParams.projectDeadline"
          placeholder="请输入项目期限 (天)"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="允许联合体投标" prop="allowCoalition">
        <el-select v-model="queryParams.allowCoalition" placeholder="请选择允许联合体投标 (0不允许 1允许)" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_allow_coalition"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="面向小微企业" prop="toSme">
        <el-select v-model="queryParams.toSme" placeholder="请选择面向小微企业 (0不针对 1针对)" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_to_sme"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="开标方式" prop="bidOpeningMode">
        <el-select v-model="queryParams.bidOpeningMode" placeholder="请选择开标方式 (1线下开标 2线上开标)" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_bid_opening_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="评标方式" prop="bidEvaluationMode">
        <el-select v-model="queryParams.bidEvaluationMode" placeholder="请选择评标方式 (1线下评标 2线上评标)" clearable>
          <el-option
            v-for="dict in dict.type.busi_tender_bid_evaluation_mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="删除标记" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记 (0正常 1删除)" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
     <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间">
        <el-date-picker
          v-model="daterangeUpdateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>-->
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['tender:notice:add']"
          >新增</el-button
        >
      </el-col>

      <el-col :span="1.5">
        <!-- <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['tender:notice:edit']"
          >修改</el-button
        > -->
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['tender:notice:remove']"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['tender:notice:export']"
          >导出</el-button
        >
      </el-col>
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>

    <el-table
      v-loading="loading"
      :data="noticeList"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" align="center" />
      <!-- <el-table-column label="公告id" align="center" prop="noticeId" />
      <el-table-column label="项目id" align="center" prop="projectId" /> -->
      <el-table-column label="状态" align="center" prop="noticeStats">
        <template slot-scope="scope">
          <span :style="{ color: scope.row.noticeStats<1?'red':'#606266' }">
            {{ scope.row.noticeStatsName }}
        </span>
        </template>
      </el-table-column>
      <el-table-column label="公告编号" align="center" prop="noticeCode" />
      <el-table-column label="公告名称" align="center" prop="noticeName" />
      <!--      <el-table-column label="公告内容" align="center" prop="noticeContent" />-->
      <el-table-column label="公告类型" align="center" prop="noticeType">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_tender_notice_type"
            :value="scope.row.noticeType"
          />
        </template>
      </el-table-column>
      <el-table-column label="变更次数" align="center" prop="changeNum" />
      <el-table-column
        label="公告开始时间"
        align="center"
        prop="noticeStartTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.noticeStartTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="公告结束时间"
        align="center"
        prop="noticeEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.noticeEndTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="开标时间"
        align="center"
        prop="bidOpeningTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.bidOpeningTime, "{y}-{m}-{d}") }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        label="开标结束时间"
        align="center"
        prop="bidOpeningEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.bidOpeningEndTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="评标时间"
        align="center"
        prop="bidEvaluationTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.bidEvaluationTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="评标结束时间"
        align="center"
        prop="bidEvaluationEndTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.bidEvaluationEndTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="项目开始时间"
        align="center"
        prop="projectStartTime"
        width="180"
      >
        <template slot-scope="scope">
          <span>{{
            parseTime(scope.row.projectStartTime, "{y}-{m}-{d}")
          }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="项目期限 (天)"
        align="center"
        prop="projectDeadline"
      /> -->
      <!-- <el-table-column
        label="允许联合体投标"
        align="center"
        prop="allowCoalition"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_tender_allow_coalition"
            :value="scope.row.allowCoalition"
          />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="面向小微企业" align="center" prop="toSme">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_tender_to_sme"
            :value="scope.row.toSme"
          />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="开标方式" align="center" prop="bidOpeningMode">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_tender_bid_opening_mode"
            :value="scope.row.bidOpeningMode"
          />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="评标方式" align="center" prop="bidEvaluationMode">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.busi_tender_bid_evaluation_mode"
            :value="scope.row.bidEvaluationMode"
          />
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="公告状态0保存待发布 1已发布"
        align="center"
        prop="noticeStats"
      >
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.sys_yes_no"
            :value="scope.row.noticeStats"
          />
        </template>
      </el-table-column> -->
      <!-- <el-table-column label="公告变更次数 " align="center" prop="changeNum" /> -->
      <el-table-column label="报价单位 " align="center" prop="priceUnit">
        <template slot-scope="scope">
          <dict-tag
            :options="dict.type.price_unit"
            :value="scope.row.priceUnit"
          />
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        align="center"
        class-name="small-padding fixed-width"
      >
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-if="scope.row.noticeStats==1"
            v-hasPermi="['tender:notice:view']"
            >查看</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['tender:notice:edit']"
            v-if="scope.row.noticeStats<1"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['tender:notice:remove']"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />


  </div>
</template>

<script>
import {
  listNotice,
  getNotice,
  delNotice,
  addNotice,
  updateNotice,
} from "@/api/tender/notice";

export default {
  name: "Notice",
  dicts: [
    "busi_tender_allow_coalition",
    "busi_tender_notice_type",
    "busi_tender_bid_evaluation_mode",
    "busi_tender_to_sme",
    "base_price_form_code",
    "price_unit",
    "sys_yes_no",
    "busi_tender_bid_opening_mode",
    "base_yes_no",
  ],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购公告信息表格数据
      noticeList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 报价单位 时间范围
      daterangeNoticeStartTime: [],
      // 报价单位 时间范围
      daterangeNoticeEndTime: [],
      // 报价单位 时间范围
      daterangeBidOpeningTime: [],
      // 报价单位 时间范围
      daterangeBidOpeningEndTime: [],
      // 报价单位 时间范围
      daterangeBidEvaluationTime: [],
      // 报价单位 时间范围
      daterangeBidEvaluationEndTime: [],
      // 报价单位 时间范围
      daterangeProjectStartTime: [],
      // 报价单位 时间范围
      daterangeCreateTime: [],
      // 报价单位 时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        noticeId: null,
        projectId: null,
        noticeCode: null,
        noticeName: null,
        noticeContent: null,
        noticeType: null,
        noticeStartTime: null,
        noticeEndTime: null,
        bidOpeningTime: null,
        bidOpeningEndTime: null,
        bidEvaluationTime: null,
        bidEvaluationEndTime: null,
        projectStartTime: null,
        projectDeadline: null,
        allowCoalition: null,
        toSme: null,
        bidOpeningMode: null,
        bidEvaluationMode: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        noticeStats: null,
        changeNum: null,
        priceUnit: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购公告信息列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (
        null != this.daterangeNoticeStartTime &&
        "" != this.daterangeNoticeStartTime
      ) {
        this.queryParams.params["beginNoticeStartTime"] =
          this.daterangeNoticeStartTime[0];
        this.queryParams.params["endNoticeStartTime"] =
          this.daterangeNoticeStartTime[1];
      }
      if (
        null != this.daterangeNoticeEndTime &&
        "" != this.daterangeNoticeEndTime
      ) {
        this.queryParams.params["beginNoticeEndTime"] =
          this.daterangeNoticeEndTime[0];
        this.queryParams.params["endNoticeEndTime"] =
          this.daterangeNoticeEndTime[1];
      }
      if (
        null != this.daterangeBidOpeningTime &&
        "" != this.daterangeBidOpeningTime
      ) {
        this.queryParams.params["beginBidOpeningTime"] =
          this.daterangeBidOpeningTime[0];
        this.queryParams.params["endBidOpeningTime"] =
          this.daterangeBidOpeningTime[1];
      }
      if (
        null != this.daterangeBidOpeningEndTime &&
        "" != this.daterangeBidOpeningEndTime
      ) {
        this.queryParams.params["beginBidOpeningEndTime"] =
          this.daterangeBidOpeningEndTime[0];
        this.queryParams.params["endBidOpeningEndTime"] =
          this.daterangeBidOpeningEndTime[1];
      }
      if (
        null != this.daterangeBidEvaluationTime &&
        "" != this.daterangeBidEvaluationTime
      ) {
        this.queryParams.params["beginBidEvaluationTime"] =
          this.daterangeBidEvaluationTime[0];
        this.queryParams.params["endBidEvaluationTime"] =
          this.daterangeBidEvaluationTime[1];
      }
      if (
        null != this.daterangeBidEvaluationEndTime &&
        "" != this.daterangeBidEvaluationEndTime
      ) {
        this.queryParams.params["beginBidEvaluationEndTime"] =
          this.daterangeBidEvaluationEndTime[0];
        this.queryParams.params["endBidEvaluationEndTime"] =
          this.daterangeBidEvaluationEndTime[1];
      }
      if (
        null != this.daterangeProjectStartTime &&
        "" != this.daterangeProjectStartTime
      ) {
        this.queryParams.params["beginProjectStartTime"] =
          this.daterangeProjectStartTime[0];
        this.queryParams.params["endProjectStartTime"] =
          this.daterangeProjectStartTime[1];
      }
      if (null != this.daterangeCreateTime && "" != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] =
          this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && "" != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] =
          this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listNotice(this.queryParams).then((response) => {
        this.noticeList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        noticeId: null,
        projectId: null,
        noticeCode: null,
        noticeName: null,
        noticeContent: null,
        noticeType: null,
        noticeStartTime: null,
        noticeEndTime: null,
        bidOpeningTime: null,
        bidOpeningEndTime: null,
        bidEvaluationTime: null,
        bidEvaluationEndTime: null,
        projectStartTime: null,
        projectDeadline: null,
        allowCoalition: null,
        toSme: null,
        bidOpeningMode: null,
        bidEvaluationMode: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null,
        noticeStats: null,
        changeNum: null,
        priceUnit: null,
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeNoticeStartTime = [];
      this.daterangeNoticeEndTime = [];
      this.daterangeBidOpeningTime = [];
      this.daterangeBidOpeningEndTime = [];
      this.daterangeBidEvaluationTime = [];
      this.daterangeBidEvaluationEndTime = [];
      this.daterangeProjectStartTime = [];
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map((item) => item.noticeId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // this.reset();
      // this.open = true;
      // this.title = "添加采购公告信息";
      this.$router.push("/tender/notice/add/0");
    },
    /** 查看按钮操作 */
    handleView(row) {
      console.info(row)
      const noticeId = row.noticeId || this.ids;
      const projectId = row.projectId;
      // getNotice(noticeId).then((response) => {
        // this.form = response.data;
        // this.open = true;
        // this.title = "修改采购公告信息";
          this.$router.push("/tender/notice/noticeview/" + projectId +"?handleType=purchaserMenu");
      // });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      console.log("row:",row);
      
      const noticeId = row.noticeId || this.ids;
      // getNotice(noticeId).then((response) => {
        // this.form = response.data;
        // this.open = true;
        // this.title = "修改采购公告信息";
        // this.$router.push("/tender/notice/add/" + noticeId);

      // });
      if(row.noticeType==1){
        this.$router.push("/tender/notice/add/" + noticeId);
      }else{
        this.$router.push("/tender/notice/change/" + noticeId+"/0");
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate((valid) => {
        if (valid) {
          if (this.form.noticeId != null) {
            updateNotice(this.form).then((response) => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addNotice(this.form).then((response) => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      if(row.noticeStats!=-1){
        this.$modal.msgSuccess("已提交，无法删除");
      }else{
        const noticeIds = row.noticeId || this.ids;
        this.$modal
          .confirm('是否确认删除采购公告信息编号为"' + noticeIds + '"的数据项？')
          .then(function () {
            return delNotice(noticeIds);
          })
          .then(() => {
            this.getList();
            this.$modal.msgSuccess("删除成功");
          })
          .catch(() => {});
      }
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download(
        "tender/notice/export",
        {
          ...this.queryParams,
        },
        `notice_${new Date().getTime()}.xlsx`
      );
    },
  },
};
</script>
