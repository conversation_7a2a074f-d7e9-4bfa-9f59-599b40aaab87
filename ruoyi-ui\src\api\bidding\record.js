import request from '@/utils/request'

// 查询投标记录列表
export function listRecord(query) {
  return request({
    url: '/bidding/record/list',
    method: 'get',
    params: query
  })
}

// 查询投标记录详细
export function getCancelInfoRecord(biddingId) {
  return request({
    url: '/bidding/record/getCancelInfo/' + biddingId,
    method: 'get'
  })
}


export function getRecord(biddingId) {
  return request({
    url: '/bidding/record/' + biddingId,
    method: 'get'
  })
}
// 新增投标记录
export function addRecord(data) {
  return request({
    url: '/bidding/record',
    method: 'post',
    data: data
  })
}

// 修改投标记录
export function updateRecord(data) {
  return request({
    url: '/bidding/record',
    method: 'put',
    data: data
  })
}

// 删除投标记录
export function delRecord(biddingId) {
  return request({
    url: '/bidding/record/' + biddingId,
    method: 'delete'
  })
}

// 查询投标记录列表
export function getListRecord(query) {
  return request({
    url: '/bidding/record/getList',
    method: 'get',
    params: query
  })
}
