<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <span>评分办法：</span>
            <el-select
              v-model="value"
              placeholder="请选择"
              @change="(val) => loadScoringTemplateItems(val, true)"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <div>
            <el-button
              class="item-button"
              style="background-color: #fff"
              @click="leadInto"
              >导入</el-button
            >
          </div>
        </div>
      </el-header>
      <el-container>
        <el-aside width="200px">
          <div style="min-height: 600px">
            <div
              class="title-button"
              :class="{ btnActive: step == '1' }"
              @click="active('1', 'qualification', '资格性评审')"
              v-if="showAndHide('资格性评审')"
            >
              资格性评审
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '2' }"
              @click="active('2', 'qualification', '符合性评审')"
              v-if="showAndHide('符合性评审')"
            >
              符合性评审
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '3' }"
              @click="active('3', 'scoring', '投标报价打分')"
              v-if="showAndHide('投标报价打分')"
            >
              投标报价打分
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '4' }"
              @click="active('4', 'technicalBid', '技术标评审')"
              v-if="showAndHide('技术标评审')"
            >
              技术标评审
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '5' }"
              @click="active('5', 'technicalBid', '商务标评审')"
              v-if="showAndHide('商务标评审')"
            >
              商务标评审
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '6' }"
              @click="active('6', 'technicalBid', '其他评审因素')"
              v-if="showAndHide('其他评审因素')"
            >
              其他评审因素
            </div>
            <div
              class="title-button"
              :class="{ btnActive: step == '7' }"
              @click="active('7', 'technicalBid', '中标候选人推选条件')"
              v-if="showAndHide('中标候选人推选条件')"
            >
              中标候选人推选条件
            </div>
          </div>
        </el-aside>
        <el-main>
          <qualification
            v-if="step == '1'"
            ref="qualification"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('资格性评审', 'qualification', '1', uInfo)
            "
          ></qualification>
          <qualification
            v-if="step == '2'"
            ref="qualification"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('符合性评审', 'qualification', '2', uInfo)
            "
          ></qualification>
          <scoring
            v-if="step == '3'"
            ref="scoring"
            @saveSuccess="
              (uInfo) => updateInfoMap('投标报价打分', 'scoring', '3', uInfo)
            "
          ></scoring>
          <technicalBid
            v-if="step == '4'"
            ref="technicalBid"
            @saveSuccess="
              (uInfo) => updateInfoMap('技术标评审', 'technicalBid', '4', uInfo)
            "
          ></technicalBid>
          <technicalBid
            v-if="step == '5'"
            ref="technicalBid"
            @saveSuccess="
              (uInfo) => updateInfoMap('商务标评审', 'technicalBid', '5', uInfo)
            "
          ></technicalBid>
          <technicalBid
            v-if="step == '6'"
            ref="technicalBid"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('其他评审因素', 'technicalBid', '6', uInfo)
            "
          ></technicalBid>
          <technicalBid
            v-if="step == '7'"
            ref="technicalBid"
            @saveSuccess="
              (uInfo) =>
                updateInfoMap('中标候选人推选条件', 'technicalBid', '7', uInfo)
            "
          ></technicalBid>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import qualification from "./scoringMethods/qualification.vue";
import scoring from "./scoringMethods/scoring.vue";
import technicalBid from "./scoringMethods/technicalBid.vue";
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
import { listInfo } from "@/api/method/info";
import { listItem } from "@/api/method/item";

export default {
  components: { qualification, scoring, technicalBid },
  data() {
    return {
      uinfo: {},
      projectInfo: {},
      itemInfo: {},
      options: [
        {
          value: "选项1",
          label: "选项1",
        },
        {
          value: "选项2",
          label: "选项2",
        },
        {
          value: "选项3",
          label: "选项3",
        },
        {
          value: "选项4",
          label: "选项4",
        },
        {
          value: "选项5",
          label: "选项5",
        },
      ],
      scoringInfoItems: [],
      scoringItemMap: {},
      scoringUinfo: {},
      value: "",
      step: "1",
    };
  },
  methods: {
    updateInfoMap(name, refName, stepNumber, scoringUinfo) {
      this.scoringUinfo = scoringUinfo;
      if (
        this.scoringUinfo &&
        this.scoringUinfo.scoringMethodUitems &&
        this.scoringUinfo.scoringMethodUitems.length > 0
      ) {
        this.mergeUitemMap();
      }
      this.active(stepNumber, refName, name);
    },
    showAndHide(val) {
      return this.scoringInfoItems.includes(val);
    },
    loadScoringTemplateItems(val, isSave) {
      if (!val) {
        return false;
      }
      let query = {
        scoringMethodId: val,
        pageNum: 1,
        pageSize: 1000,
      };
      return listItem(query)
        .then((result) => {
          if (result.code === 200) {
            this.scoringInfoItems = result.rows.map((item) => {
              this.scoringItemMap[item.itemName] = item;
              return item.itemName;
            });
            if (isSave) {
              this.saveUinfo();
            }
            //获取id==val的对象
            this.scoringUinfo = this.options.find((item) => {
              console.log(222, item, val);
              return item.value == val;
            }).entity.uinfo;
            console.log(333, this.scoringUinfo);
            console.log(111, this.scoringUinfo, this.options);
            if (
              this.scoringUinfo &&
              this.scoringUinfo.scoringMethodUitems &&
              this.scoringUinfo.scoringMethodUitems.length > 0
            ) {
              this.mergeUitemMap();
            }
          }
        })
        .catch((err) => {});
    },
    mergeUitemMap() {
      for (const key in this.scoringItemMap) {
        let foundItem = this.scoringUinfo.scoringMethodUitems.find(
          (item) =>
            item.scoringMethodItemId ==
            this.scoringItemMap[key].scoringMethodItemId
        );
        if (foundItem) {
          this.scoringItemMap[key] = foundItem;
        }
      }
    },
    //加载评分模板
    loadScoringTemplate() {
      let query = {
        tenderMode: this.projectInfo.tenderMode,
        projectType: this.projectInfo.projectType,
        pageNum: 1,
        pageSize: 1000,
        params: {
          returnUinfo: "true",
        },
      };
      return listInfo(query)
        .then((result) => {
          this.options = result.rows.map((item) => {
            return {
              label: item.methodName,
              value: item.scoringMethodId,
              entity: item,
            };
          });
        })
        .catch((err) => {});
    },
    active(stepNumber, refName, name) {
      console.log(
        "socring info active",
        this.projectInfo,
        this.scoringItemMap[name],
        this.scoringUinfo
      );
      this.step = stepNumber;
      // 加载子页面
      this.$nextTick(() => {
        this.$refs[refName].init(
          this.projectInfo,
          this.scoringItemMap[name],
          this.scoringUinfo
        );
      });
    },
    // TODO 导入
    leadInto() {},
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      console.log("scoringMethod init start", projectInfo, itemInfo, uinfo);
      this.projectInfo = projectInfo;
      this.uinfo = uinfo;

      this.loadScoringTemplate()
        .then(() => {
          var isSave = true;
          if (this.options && this.options.length > 0) {
            if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
              this.value = JSON.parse(itemInfo.itemContent);
              isSave = false;
            } else {
              this.value = this.options[0].value;
            }
            this.itemInfo = itemInfo;
            if (uinfo && uinfo.entFileId) {
              this.itemInfo.entFileId = uinfo.entFileId;
            }
            this.loadScoringTemplateItems(this.value, isSave)
              .then(() => {
                //加载子页面
                this.$nextTick(() => {
                  this.active("1", "qualification", "资格性评审");
                });
                // this.$refs.qualification.init(
                //   this.projectInfo,
                //   this.scoringItemMap["资格性评审"],
                //   this.scoringUinfo
                // );
              })
              .catch((err) => {});
          }
        })
        .catch((err) => {});
      console.log("scoringMethod init end", this.itemInfo);
    },
    saveUinfo() {
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: JSON.stringify(this.value),
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: rgba(152, 200, 253, 1) !important; /* 激活状态下的字体颜色 */
}
.head {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;
  .title-button {
    font-size: 16px;
    color: #333;
    margin: 25px 0;
    cursor: pointer;
    s &:hover {
      color: rgba(152, 200, 253, 1);
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  //   padding: 0;
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #333;
  }
}
</style>
