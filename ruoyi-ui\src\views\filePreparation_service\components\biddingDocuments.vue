<template>
  <div
    class="container"
    v-loading="loading"
  >
    <div class="transfer">
      <el-table
        :data="tableData"
        border
        v-loading="tableDataLoading"
        style="width: 100%"
      >
        <el-table-column label="序号">
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="functionPoint"
          label="功能点"
        >
        </el-table-column>
        <el-table-column label="检查结果">
          <template slot-scope="scope">
            <span
              v-if="scope.row.result == 1"
              style="color:red"
            >{{ scope.row.content }}</span>
            <span
              v-else
              style="color:green"
            >
              {{ scope.row.content }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="item">
      <!-- <div >
      <el-button
          class="item-button"
          @click="preview()"
        >预览采购文件</el-button>
        </div>  -->
      <!-- 判断是否已经有采购文件，有的话显示"采购文件签章"按钮，'采购文件生成'改为重新生成采购文件 -->
      <div>
        <el-button
          class="item-button"
          @click="generated()"
        >{{have? '采购文件重新生成':'采购文件生成'}}
        </el-button>
      </div>
      <div>
        <el-button
          v-if="have"
          class="item-button"
          @click="accredit()"
        >采购文件签章
        </el-button>
      </div>
      <div>
        <el-button
          class="item-button"
          @click="returnNotice()"
        >返回采购公告编辑
        </el-button>
      </div>

    </div>
  </div>

</template>

<script>
import { getUserProfile } from "@/api/system/user";
import { getNotice,getTenderNoticeByProject } from "@/api/tender/notice";
import { getInfo } from "@/api/ent/info";

import {
  checkIsOver,
  generateProjectFileZip,
  test,
  createProjectFileAndCgwj,
} from "@/api/documents/uinfo";
export default {
  data() {
    return {
      loading: false,
      have: false,
      enterpriseSignature: false,
      tableDataLoading: false,
      uinfo: {},
      itemInfo: {},
      projectInfo: {
        projectName: "测试项目111",
        projectCode: "3784hjids",
        deadLine: "2024-07-20",
        procurementWey: "竞争性磋商",
        purchasName: "测试采购人",
        purchasTel: "",
        agencyName: "测试代理机构",
        agencyTel: "",
      },
      tableData: [
        {
          functionPoint: "开标一览表",
          result: 0,
          content: "正常",
        },
        {
          functionPoint: "评分办法",
          result: 0,
          content: "正常",
        },
        {
          functionPoint: "采购文件正文",
          result: 0,
          content: "正常",
        },
      ],
      user: {},
    };
  },
  methods: {
    returnNotice(){
      //如果有新增页面，则关闭
      var obj0 = {
        path:"/tender/notice/add/0",
        name:"noticeAdd",
      }
      this.$tab.closePage(obj0);
      //如果之前是新增公告，则打开并跳转至带有noticeId的新标签页；如果是修改公告，则会跳转至标签页
      var obj1 = {
        path:"/tender/notice/add/"+this.$route.query.noticeId
      }
      if(this.notice.noticeType==2){
        obj1 = {
        path:"/tender/notice/change/"+this.$route.query.noticeId + "/0"
      }
      }
      
      this.$tab.closeOpenPage(obj1);
      // this.$tab.openPage("新增采购公告", url);
    },
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      this.itemInfo = itemInfo;
      this.uinfo = uinfo;
      this.getChickIsOver();
      getUserProfile().then((response) => {
        if (response.code == 200) {
          this.user = response.data;
          getInfo(response.data.entId).then((response) => {
            if (response.code == 200) {
              if (response.data.enterpriseSignature != null) {
                this.enterpriseSignature = true;
              } else {
                this.enterpriseSignature = false;
              }
            }
          });
        } else {
          this.$message.error(res.message);
        }
      });
      getTenderNoticeByProject(this.$route.query.projectId).then((result) => {
        if (result.code == 200) {
          console.log();
          if (result.data.attachmentMap["5"] != "") {
            this.have = true;
          } else {
            this.have = false;
          }
          console.log("this.have", this.have);
        }
      });
    },
    // 获取校验结果
    getChickIsOver() {
      this.tableDataLoading = true;
      checkIsOver({
        projectFileId: this.uinfo.projectFileId,
        projectId: this.$route.query.projectId,
      }).then((res) => {
        if (res.code === 200) {
          if (res.data.uniqueData && res.data.uniqueData.length > 0) {
            this.tableData = res.data.uniqueData;
          }
          this.tableDataLoading = false;
        } else {
          this.$message.error(res.message);
          this.tableDataLoading = false;
        }
      });
    },
    // 预览采购文件
    preview() {
      this.$download.zip(
        "/documents/uinfo/downloadProjectFile/1163308059124741",
        "project_1163308059124741"
      );
    },
    // 授权签章
    accredit() {
      // 判断是否有企业签章，如果没有提示”请前往右上角个人中心制作企业签章“
      if (!this.enterpriseSignature) {
        this.$message.warning(
          "没有制作企业签章，请前往右上角个人中心制作企业签章"
        );
      } else {
        window.open(
          `${process.env.VUE_APP_KAIFANFQIAN_API}/?project=${
            this.$route.query.projectId
          }&type=${0}&entId=${this.user.entId}`,
          "_blank"
        );
      }
    },
    // 生成加密采购文件
    generated() {
      this.loading = true;
      if (!this.tableData.every((item) => item.result === 0)) {
        this.$message.error("请检查所有项是否都通过");
        return;
      }
      const query = {
        entFileId: this.uinfo.entFileId,
        type: 1,
        params: {
          projectId: this.$route.query.projectId,
          noticeId: this.$route.query.noticeId,
          downItemListName: "项目基本信息,开标一览表,评分办法,采购文件正文",
        },
      };
      createProjectFileAndCgwj(query)
        .then((result) => {
          if (result.code === 200) {
            this.$message.success("生成成功,可以授权签章");
            this.have = true;
            // 刷新add页面
            // const obj = {
            //   path: `/tender/notice/add/${this.$route.query.noticeId}`,
            //   name: "noticeAdd",
            // };
            // if(this.notice.noticeType==2){
            //   obj = {
            //     path:"/tender/notice/change/"+this.$route.query.noticeId + "/0",
            //     name: "noticeChange",
            //   };
            // }
            this.$tab.refreshPage();

            this.loading = false;
          } else {
            this.have = false;
            this.$message.success(result.msg);
            this.loading = false;
          }
          console.log("loading  ", this.loading);
        })
        .catch((err) => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-content: center; /* 垂直居中 */
}
.transfer {
  width: 90%;
  margin-top: 40px;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 150px;
    margin-right: 20px;
    text-align: left;
  }
  .item-content {
    min-width: 100px;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1);
  color: #fff;
  &:hover {
    color: #fff;
  }
}
</style>
