<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="企业id" prop="entId">
        <el-input
          v-model="queryParams.entId"
          placeholder="请输入企业id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="资质类型" prop="qualificationType">
        <el-input
          v-model="queryParams.qualificationType"
          placeholder="请输入资质类型"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="有效期开始">
        <el-date-picker
          v-model="daterangeQualificationStartDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="有效期截至">
        <el-date-picker
          v-model="daterangeQualificationEndDate"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="是否长期有效" prop="isLongTerm">
        <el-select v-model="queryParams.isLongTerm" placeholder="请选择是否长期有效" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['ent:qualification:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ent:qualification:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ent:qualification:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['ent:qualification:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="qualificationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="资质id" align="center" prop="qualificationId" />
      <el-table-column label="企业id" align="center" prop="entId" />
      <el-table-column label="资质类型" align="center" prop="qualificationType" />
      <el-table-column label="资质文件" align="center" prop="qualificationFile" />
      <el-table-column label="有效期开始" align="center" prop="qualificationStartDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.qualificationStartDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="有效期截至" align="center" prop="qualificationEndDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.qualificationEndDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否长期有效" align="center" prop="isLongTerm">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.base_yes_no" :value="scope.row.isLongTerm"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ent:qualification:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ent:qualification:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改企业资质对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="企业id" prop="entId">
          <el-input v-model="form.entId" placeholder="请输入企业id" />
        </el-form-item>
        <el-form-item label="资质类型" prop="qualificationType">
          <el-input v-model="form.qualificationType" placeholder="请输入资质类型" />
        </el-form-item>
        <el-form-item label="资质文件" prop="qualificationFile">
          <file-upload v-model="form.qualificationFile"/>
        </el-form-item>
        <el-form-item label="有效期开始" prop="qualificationStartDate">
          <el-date-picker clearable
            v-model="form.qualificationStartDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择有效期开始">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="有效期截至" prop="qualificationEndDate">
          <el-date-picker clearable
            v-model="form.qualificationEndDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择有效期截至">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="是否长期有效" prop="isLongTerm">
          <el-radio-group v-model="form.isLongTerm">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQualification, getQualification, delQualification, addQualification, updateQualification } from "@/api/ent/qualification";

export default {
  name: "Qualification",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 企业资质表格数据
      qualificationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否长期有效时间范围
      daterangeQualificationStartDate: [],
      // 是否长期有效时间范围
      daterangeQualificationEndDate: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        entId: null,
        qualificationType: null,
        qualificationFile: null,
        qualificationStartDate: null,
        qualificationEndDate: null,
        isLongTerm: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        entId: [
          { required: true, message: "企业id不能为空", trigger: "blur" }
        ],
        qualificationType: [
          { required: true, message: "资质类型不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询企业资质列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeQualificationStartDate && '' != this.daterangeQualificationStartDate) {
        this.queryParams.params["beginQualificationStartDate"] = this.daterangeQualificationStartDate[0];
        this.queryParams.params["endQualificationStartDate"] = this.daterangeQualificationStartDate[1];
      }
      if (null != this.daterangeQualificationEndDate && '' != this.daterangeQualificationEndDate) {
        this.queryParams.params["beginQualificationEndDate"] = this.daterangeQualificationEndDate[0];
        this.queryParams.params["endQualificationEndDate"] = this.daterangeQualificationEndDate[1];
      }
      listQualification(this.queryParams).then(response => {
        this.qualificationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        qualificationId: null,
        entId: null,
        qualificationType: null,
        qualificationFile: null,
        qualificationStartDate: null,
        qualificationEndDate: null,
        isLongTerm: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeQualificationStartDate = [];
      this.daterangeQualificationEndDate = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.qualificationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加企业资质";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const qualificationId = row.qualificationId || this.ids
      getQualification(qualificationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改企业资质";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.qualificationId != null) {
            updateQualification(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addQualification(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const qualificationIds = row.qualificationId || this.ids;
      this.$modal.confirm('是否确认删除企业资质编号为"' + qualificationIds + '"的数据项？').then(function() {
        return delQualification(qualificationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('ent/qualification/export', {
        ...this.queryParams
      }, `qualification_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
