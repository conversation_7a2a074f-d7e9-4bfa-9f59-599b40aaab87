<template>
  <div style="margin-top: 20px" class="tender" v-loading="loading">
    <el-form ref="busiTenderNotice" :model="formData" :rules="rules" size="medium" label-width="0">
      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-suitcase"></i>公告信息</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="projectId">
                        <el-select v-model="formData.projectId" placeholder="请选择项目" ref="selectProject" filterable clearable :disabled="!projectEdit" :style="{ width: '100%' }" @change="change($event)">
                          <el-option v-for="(item, index) in projectIdOptions" :key="index" :label="item.projectName" :value="item.projectId" :disabled="item.projectStatus != 10"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>公告名称
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="noticeName">
                        <el-input v-model="formData.noticeName" placeholder="公告名称" clearable :style="{ width: '100%' }" :disabled="formData.noticeStats==0">
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>报价单位
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="priceUnit">
                        <el-select v-model="formData.priceUnit" placeholder="请选择" clearable :style="{ width: '100%' }" :disabled="formData.noticeStats==0">
                          <el-option v-for="(item, index) in dict.type.price_unit" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>评标方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="bidEvaluationMode">
                        <el-radio-group v-model="formData.bidEvaluationMode" size="medium">
                          <el-radio v-for="(item, index) in dict.type
                              .busi_tender_bid_evaluation_mode" :key="index" :label="item.value" :disabled="item.disabled">{{ item.label }}</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>开标方式
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="bidOpeningMode">
                        <el-radio-group v-model="formData.bidOpeningMode" size="medium">
                          <el-radio v-for="(item, index) in dict.type
                              .busi_tender_bid_opening_mode" :key="index" :label="item.value" :disabled="item.disabled">{{ item.label }}</el-radio>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>公告开始时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="noticeStartTime">
                        <el-date-picker :disabled="formData.noticeStats==0" v-model="formData.noticeStartTime" @change="selectProject()" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsOne" :style="{ width: '100%' }" placeholder="请选择日期" type="datetime" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>项目开标时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="noticeEndTime">
                        <el-date-picker v-model="formData.noticeEndTime" :disabled="formData.noticeStats==0 || formData.noticeStartTime == '' || formData.noticeStartTime == null" format="yyyy-MM-dd HH:mm:ss" @change="selectProject()" value-format="yyyy-MM-dd HH:mm:ss" :picker-options="pickerOptionsTwo" :style="{ width: '100%' }" placeholder="请选择日期" type="datetime" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>采购文件获取开始时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="docAcquisitionStartTime">
                        <el-date-picker v-model="formData.docAcquisitionStartTime" disabled format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" :style="{ width: '100%' }" type="datetime" placeholder="请选择日期" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>采购文件获取截止时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="docAcquisitionEndTime">
                        <el-date-picker v-model="formData.docAcquisitionEndTime" disabled format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :style="{ width: '100%' }" placeholder="请选择日期" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

                <tr>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>响应文件递交截止时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="docResponseOverTime">
                        <el-date-picker v-model="formData.docResponseOverTime" disabled format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :style="{ width: '100%' }" placeholder="请选择日期" clearable></el-date-picker>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>是否允许分包
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="subcontractingAllowed">
                        <el-select v-model="formData.subcontractingAllowed" placeholder="请选择" :disabled="formData.noticeStats==0">
                          <el-option label="否" :value="0"> </el-option>
                          <el-option label="是" :value="1"> </el-option>
                        </el-select>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              
                <tr>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>付款方式
                    </div>
                  </td>
                  <td colspan="21" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item prop="paymentMethod">
                        <el-input type="textarea" :rows="4" v-model="formData.paymentMethod" placeholder="付款方式" clearable :style="{ width: '100%' }" :disabled="formData.noticeStats==0">
                        </el-input>
                      </el-form-item>
                    </div>
                  </td>
                </tr>

              <tr>
                <td colspan="3" class="el-table__cell is-leaf">
                  <div class="cell">
                    <strong style="color: red">*</strong>变更内容
                  </div>
                </td>
                <td colspan="21" class="el-table__cell is-leaf">
                  <div class="cell">
                    <el-form-item prop="mainContent">
                      <el-input type="textarea" :rows="4" v-model="formData.mainContent" placeholder="变更内容" clearable :style="{ width: '100%' }" :disabled="formData.noticeStats==0">
                      </el-input>
                    </el-form-item>
                  </div>
                </td>
              </tr>

                <tr v-if="showBidderTransfer">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>获取投标人
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-transfer
                        v-model="bidders"
                        :data="bidderSource"
                        :titles="['未选择', '已选择']"
                        :filterable="true"
                        style="width: 100%"
                      ></el-transfer>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>开评标室预约</span>
            <!-- <span style="color:dodgerblue;font-size: 14px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;为保证开评标工作顺利开展，需要预约使用开评标室时，请提前与平台工作人员进行联系（0392-2155585）</span> -->
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr>
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>开评标室来源
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-radio v-model="formData.venueType" :label="1" :disabled="formData.noticeStats==0">平台</el-radio>
                          <el-radio v-model="formData.venueType" :label="2" :disabled="formData.noticeStats==0">自有场地</el-radio>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.venueType == 1">
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      查看开评标室占用情况
                    </div>
                  </td>
                  <td colspan="21" class="el-table__cell is-leaf">
                    <el-button style="float: left; margin-right: 30px;" type="primary" @click="showOpeningRoom">查看开标室使用情况</el-button>
                    <el-button style="float: left; margin-right: 30px;" type="primary" @click="showEvaluationRoom">查看评标室使用情况</el-button>
                    <p style="float: left; margin-right: 30px;color:dodgerblue;font-size: 14px;">为保证开评标工作顺利开展，请先查看平台开评标室使用情况，并与平台工作人员联系（0392-2155585）</p>
                  </td>
                </tr>
                <tr v-if="formData.venueType == 1">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>开标室
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-select :disabled="formData.noticeStats==0" v-model="bidOpeningRoom.venueId" placeholder="请选择" :style="{ width: '100%' }" popper-class="select-option" @change="changeOpeningRoom">
                            <el-option v-for="(item, index) in bidOpeningRoomOptions" :key="index" :label="item.venueName" :value="item.venueId" :disabled="item.disabled"></el-option>
                          </el-select>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>预计开标结束时间
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-time-select :disabled="formData.noticeStats==0" placeholder="结束时间" v-model="bidOpeningRoom.bidOpenEndTime" :picker-options="{
                              start: '08:00',
                              step: '00:30',
                              end: '18:00',
                              minTime: bidOpeningRoom.bidOpenStartTime
                            }">
                          </el-time-select>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.venueType == 1">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div></div>
                    <div class="cell">
                      <strong style="color: red">*</strong>评标室
                    </div>
                  </td>
                  <td colspan="10" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-select :disabled="formData.noticeStats==0" v-model="bidEvaluationRoom.venueId" placeholder="请选择" :style="{ width: '100%' }" popper-class="select-option" @change="changeEvaluationRoom">
                            <el-option v-for="(item, index) in bidEvaluationRoomOptions" :key="index" :label="item.venueName" :value="item.venueId" :disabled="item.disabled"></el-option>
                          </el-select>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>评标时间段
                    </div>
                  </td>
                  <td colspan="9" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-select :disabled="formData.noticeStats==0" v-model="bidEvaluationRoom.bidEvaluationPeriod" placeholder="请选择" clearable :style="{ width: '40%' }">
                            <el-option v-for="(item, index) in dict.type
                                .busi_bid_evaluation_period" :key="index" :label="item.label" :value="item.value" :disabled="item.disabled"></el-option>
                          </el-select>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.venueType == 2">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>评标时间
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-time-select :disabled="formData.noticeStats==0" placeholder="选择日期时间" v-model="occupyStartTime_temp" :picker-options="{
                              start: '08:00',
                              step: '00:30',
                              end: '18:00',
                            }">
                          </el-time-select>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr v-if="formData.venueType == 2">
                  <td colspan="2" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red">*</strong>具体地点
                    </div>
                  </td>
                  <td colspan="22" class="el-table__cell is-leaf">
                    <div class="cell">
                      <el-form-item>
                        <template>
                          <el-input v-model="bidOpeningRoom.remark" placeholder="请输入具体地点" :disabled="formData.noticeStats==0"></el-input>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
                <tr>
                  <td colspan="24" class="el-table__cell is-leaf">
                    <el-button type="primary" class="makeTenserFile" @click="goFilePreparation" style="
                        border-color: rgba(0, 0, 0, 1);
                        background-color: rgba(185, 248, 191, 1);
                      ">
                      制作采购文件
                    </el-button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>

      <el-col :span="24" class="card-box">
        <el-card>
          <div slot="header">
            <span><i class="el-icon-document"></i>附件</span>
          </div>
          <div class="el-table el-table--enable-row-hover el-table--medium">
            <table cellspacing="0" style="width: 100%; table-layout: fixed">
              <tbody>
                <tr v-for="dict in dict.type.busi_tender_notice_attachment" :key="dict.label">
                  <td colspan="3" class="el-table__cell is-leaf">
                    <div class="cell">
                      <strong style="color: red" v-if="dict.raw.isEquals == 1">*</strong>{{ dict.label }}
                    </div>
                  </td>
                  <td colspan="21" class="el-table__cell is-leaf">
                    <div class="cell cell-right-border">
                      <el-form-item class="upload">
                        <template>
                          <FileUpload :value="getFiles(dict)" :names="getFileNames(dict)" @input="handleInput(dict, $event)" :fileType="['pdf', 'doc', 'docx']" :isShowTip="false" :showOnly="dict.label == '采购文件正文' || dict.label == '采购文件附件' ? true :false"></FileUpload>
                        </template>
                      </el-form-item>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-card>
      </el-col>
    </el-form>

    <!-- <el-form-item label-width="120px" label="采购文件制作：" prop="field132">

      </el-form-item> -->
    <div slot="footer" class="option">
      <el-button v-if="!(formData.noticeStats==0)" type="primary" @click="temporaryStorage()">保存</el-button>
      <!-- <el-button
        v-if="!(formData.noticeStats==0)"
        type="primary"
        @click="saveStorage()"
      >保存</el-button> -->
      <el-button type="primary" v-if="formData.noticeStats==0" @click="updateStorage()">修改</el-button>
      <el-button type="primary" @click="submitForm()">发布</el-button>
      <!-- <el-button @click="resetForm">重置</el-button> -->
    </div>
    <!-- 开标室使用情况 -->
    <el-dialog append-to-body title="开标室使用情况" :visible.sync="openingRoom" width="50%" :before-close="handleClose">
      <el-tabs v-model="activeOpenRoom" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in bidOpeningRoomOptions" :key="'bidOpeningRoom' + index" :label="item.venueName" :name="item.venueId.toString()">
          <el-descriptions title="" direction="vertical" :column="5" border>
            <el-descriptions-item v-for="(item, index) in timePeriod" :key="'timePeriodZore' + index" :label="item.time">
              <div v-for="(item, index) in item.openPeriod" :key="'data' + index" style="margin-bottom: 5px;">
                <el-tag size="small" type="danger">{{ item }}</el-tag>
              </div>
              <div></div>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>

    <!-- 评标室使用情况 -->
    <el-dialog append-to-body title="评标室使用情况" :visible.sync="evaluationRoom" width="50%" :before-close="handleClose">
      <el-tabs v-model="activeEvaluationRoom" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in bidEvaluationRoomOptions" :key="'bidEvaluationRoom' + index" :label="item.venueName" :name="item.venueId.toString()">
          <el-descriptions title="" direction="vertical" :column="5" border>
            <el-descriptions-item v-for="(item, index) in timePeriod" :key="'timePeriod' + index" :label="item.time">
              <el-descriptions title="" :column="1" border>
                <el-descriptions-item label="上午">
                  <el-tag v-if="!item.evaluation.Morning" size="small" type="success">可用</el-tag>
                  <el-tag v-else size="small" type="danger">占用</el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="下午">
                  <el-tag v-if="!item.evaluation.Afternoon" size="small" type="success">可用</el-tag>
                  <el-tag v-else size="small" type="danger">占用</el-tag>
                </el-descriptions-item>
                <el-descriptions-item v-if="!(item.evaluation.Morning || item.evaluation.Afternoon)" label="全天">
                  <el-tag v-if="!item.evaluation.allDay" size="small" type="success">可用</el-tag>
                  <el-tag v-else size="small" type="danger">占用</el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-descriptions-item>
          </el-descriptions>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>
<script>
import {
  getNotice,
  addNotice,
  updateNotice,
  updateAnnouncementInfo,
  getTenderNoticeByProject,
  getBidderListInfo,
  changeAnnouncementInfo,
  getChangeNoticeInfo,
} from "@/api/tender/notice";
import { getProject, listProject } from "@/api/tender/project";
import { download } from "@/utils/request";
import { HOLIDAY } from "@/utils/holiday";
import { getDicts, selectByTypeAndValue } from "@/api/system/dict/data";
import { listInfo } from "@/api/venue/info";
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
import {
  listOccupy,
  getOccupy,
  addOccupy,
  updateOccupy,
} from "@/api/venue/occupy";

export default {
  components: {},
  dicts: [
    "price_unit",
    "busi_tender_allow_coalition",
    "busi_tender_to_sme",
    "busi_tender_bid_opening_mode",
    "busi_tender_bid_evaluation_mode",
    "busi_bid_evaluation_period",
    "busi_tender_notice_attachment",
  ],
  props: [],
  data() {
    return {
      type:null,
      noticeId: "",
      source: "1",
      bidOpeningPeriod: undefined,
      spaceOccupancyList: [],
      // 遮罩层
      loading: false,
      projectEdit: true,
      timePeriod: [],
      openingRoom: false,
      activeOpenRoom: undefined,
      evaluationRoom: false,
      activeEvaluationRoom: undefined,
      intervalTime: null,
      intervalTimeType: undefined,
      attachmentTypes: [],
      evaluationList: [],
      attachmentsMap: {},
      bidders: [], // 用于存储投标人数据
      bidderSource: [], // 用于存储所有的投标人源数据
      showBidderTransfer: false, // 控制获取投标人穿梭框的显示隐藏
      formData: {
        attachmentMap: {},
        attachments: [],
        noticeName: null,
        projectId: "",
        projectStartTime: null,
        projectDeadline: null,
        priceUnit: "1",
        allowCoalition: null,
        toSme: "0",
        bidOpeningMode: "2",
        bidEvaluationMode: "2",
        noticeStartTime: null,
        docAcquisitionStartTime:null,
        noticeEndTime: null,
        noticeContent: null,
        bidOpeningTime: null,
        docAcquisitionEndTime: null,
        docResponseOverTime: null,
        bidEvaluationTime: null,
        noticeStats: null,
        operationType: null,
        busiVenueOccupys: [],
        subcontractingAllowed:0,
        venueType: 1,
        inviteBidder:[]
      },
      projectInfo: {
        projectName: "",
        projectCode: "",
        deadLine: "",
        procurementWey: "",
        purchasName: "",
        projectType: "",
      },
      fileNames:{
        "0": "测试1",
        "1": "",
        "2": "",
        "3": "",
        "4": "",
        "5": "测试2,测试3"
      },
      noticeEndTimeShow: false,
      rules: {
        docAcquisitionStartTime: [
          {
            required: true,
            message: "请选择采购文件获取开始时间",
            trigger: "blur",
          },
        ],
        docAcquisitionEndTime: [
          {
            required: true,
            message: "请选择采购文件获取结束时间",
            trigger: "blur",
          },
        ],
        docResponseOverTime: [
          {
            required: true,
            message: "请选择响应文件提交截至时间",
            trigger: "blur",
          },
        ],
        subcontractingAllowed: [
          {
            required: true,
            message: "请选择是否允许分包",
            trigger: "blur",
          },
        ],
        paymentMethod: [
          {
            required: true,
            message: "请填写付款方式",
            trigger: "blur",
          },
        ],
        noticeName: [
          {
            required: true,
            message: "公告名称",
            trigger: "blur",
          },
        ],
        projectId: [
          {
            required: true,
            message: "请选择项目",
            trigger: "change",
          },
        ],
        priceUnit: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        allowCoalition: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        toSme: [
          {
            required: false,
            message: "请选择",
            trigger: "change",
          },
        ],
        bidOpeningMode: [
          {
            required: true,
            message: "请选择",
            trigger: "change",
          },
        ],
        bidEvaluationMode: [
          {
            required: true,
            message: "评标方式不能为空",
            trigger: "change",
          },
        ],
        noticeStartTime: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        noticeEndTime: [
          {
            required: true,
            message: "请选择日期",
            trigger: "change",
          },
        ],
        noticeContent: [
          {
            required: true,
            message: "公告内容不能为空",
            trigger: "blur",
          },
        ],

        bidOpeningTime: [
          {
            required: false,
            message: "输入内容",
            trigger: "blur",
          },
        ],
        venueType: [
          {
            required: true,
            message: "请选择开标时来源",
            trigger: "blur",
          },
        ],
        bidEvaluationTime: [
          {
            required: false,
            message: "输入内容",
            trigger: "blur",
          },
        ],
      },
      // 开标室保存信息
      bidOpeningRoom: {
        noticeId: null,
        venueId: null,
        occupyId: null,
        venueName: null,
        venueType: null,
        occupyStartTime: null,
        occupyEndTime: null,
        bidEvaluationPeriod: null,
        bidOpenStartTime: "",
        bidOpenEndTime: "",
      },
      // 评标室保存信息
      bidEvaluationRoom: {
        noticeId: null,
        venueId: null,
        occupyId: null,
        venueName: null,
        venueType: null,
        occupyStartTime: null,
        occupyEndTime: null,
        bidEvaluationPeriod: null,
      },
      occupyStartTime_temp: "",
      projectIdOptions: [],
      bidOpeningRoomOptions: [],
      bidEvaluationRoomOptions: [],
      // 公告开始时间日期选择的禁用日期
      pickerOptionsOne: {
        // 法定节假日日期不可选
        disabledDate: (time) => {
          for (var i = 0; i < HOLIDAY.length; i++) {
            if (time.getTime() === new Date(HOLIDAY[i]).getTime()) {
              return true;
            }
          }
          if (time.getDay() === 0 || time.getDay() === 6) {
            // 返回日期中表示周几的数值（0 表示周日，6 表示周六）
            return "#";
          } else {
            // 今日之前的日期不可选
            let tempTime = 3600 * 1000 * 24;
            if(this.formData.noticeEndTime){
              const noticeEndTime = new Date(this.formData.noticeEndTime).getTime();
              return (time.getTime() < new Date() - tempTime) || (time.getTime() > noticeEndTime-(24 * 60 * 60 * 1000));
            }
            return time.getTime() < new Date() - tempTime;
          }

        },
        //修改法定节假日期的样式
        cellClassName: (time) => {
          for (var i = 0; i < HOLIDAY.length; i++) {
            if (time.getTime() === new Date(HOLIDAY[i]).getTime()) {
              return "x-hd";
            }
          }
        },
      },
      // 投标结束时间日期选择的禁用日期
      pickerOptionsTwo: {
        disabledDate: (time) => {
          const startTime = new Date(this.formData.noticeStartTime.substring(0, 10)+" 00:00:00").getTime(); // 将日期转换为时间戳
          let endTime = startTime + this.intervalTime * 24 * 60 * 60 * 1000;
          if (this.intervalTimeType == "w") {
            endTime = extendEndTimeIfHolidaysIncrease(startTime, endTime);
          }
          // 检查是否为法定节假日
          for (var i = 0; i < HOLIDAY.length; i++) {
            if (time.getTime() === new Date(HOLIDAY[i]).getTime()) {
              return true;
            }
          }
          if (time.getDay() === 0 || time.getDay() === 6) {
            // 返回日期中表示周几的数值（0 表示周日，6 表示周六）
            return "#";
          } else {
            // 今日之前的日期不可选
            return time.getTime() < endTime;
          }
          function extendEndTimeIfHolidaysIncrease(startTime, endTime) {
            let extendedEndTime = 0;
            // 获取初始节假日数量
            function getHolidayCount(start, end) {
              let count = 0;
              for (let i = 0; i < HOLIDAY.length; i++) {
                const holidayTime = new Date(HOLIDAY[i]).getTime();
                if (holidayTime >= start && holidayTime <= end) {
                  count++;
                }
              }
              return count;
            }

            let initialHolidayCount = getHolidayCount(startTime, endTime);
            extendedEndTime = initialHolidayCount;
            while (true) {
              // 计算新的节假日数量
              let newHolidayCount = getHolidayCount(
                startTime,
                endTime + extendedEndTime * 24 * 60 * 60 * 1000
              );
              // 如果节假日数量增加，则继续增加 endTime
              if (newHolidayCount > initialHolidayCount) {
                extendedEndTime =
                  extendedEndTime + (newHolidayCount - initialHolidayCount);
                initialHolidayCount = newHolidayCount;
              } else {
                break;
              }
            }

            return endTime + extendedEndTime * 24 * 60 * 60 * 1000;
          }
        },
        //修改法定节假日期的样式
        cellClassName: (time) => {
          for (var i = 0; i < HOLIDAY.length; i++) {
            if (time.getTime() === new Date(HOLIDAY[i]).getTime()) {
              return "x-hd";
            }
          }
        },
      },
    };
  },
  computed: {},
  watch: {},
  created() {
  },
  mounted() {
      this.noticeId = this.$route.params.noticeId;
      this.type = this.$route.params.type;
      this.getList().then(() => {
      getChangeNoticeInfo(this.noticeId, this.type).then((result) => {
          if (result.code == 200) {
             getProject(result.data.projectId).then(res => {
              selectByTypeAndValue("busi_tender_mode", res.data.tenderMode).then(
                  (result) => {
                      if (result.code == 200) {
                          let it = result.data.remark;
                          let its = it.split(",");

                          this.intervalTime = its[0];
                          this.intervalTimeType = its[1];
                      }
                  }
              );
            });
            this.formData = result.data;
            // if(this.formData.noticeId!=undefined){
            this.occupyStartTime_temp = this.formData.noticeEndTime.slice(11,19);
            if (
              this.formData.busiVenueOccupys != undefined &&
              this.formData.busiVenueOccupys.length > 0
            ) {
              this.bidOpeningRoom = this.formData.busiVenueOccupys[0];
              this.bidOpeningRoom.bidOpenEndTime =
                this.bidOpeningRoom.occupyEndTime.slice(11, 16);
              this.bidEvaluationRoom = this.formData.busiVenueOccupys[1];
              this.bidEvaluationRoom.bidEvaluationPeriod =
                this.bidEvaluationRoom.bidEvaluationPeriod + "";
              if (this.formData.venueType == 2) {
                this.occupyStartTime_temp =
                  this.bidEvaluationRoom.occupyStartTime.slice(11, 19);
              }
            }
                if (this.formData.inviteBidder) {
                    // 使用 split 方法将字符串按逗号分隔并存储到 bidders 数组中
                    this.bidders = this.formData.inviteBidder.split(',');
                }
        //   this.formData.bidEvaluationMode =
        //     this.formData.bidEvaluationMode + "";
        //   this.formData.allowCoalition = this.formData.allowCoalition + "";
        //   this.formData.toSme = this.formData.toSme + "";
        //   this.formData.bidOpeningMode = this.formData.bidOpeningMode + "";
            }
        // }
        console.log("formData  ", this.formData);
        });
      if (this.$route.query.projectId || this.noticeId != 0) {
        this.projectEdit = false;
      }
      this.$nextTick(() => {
        window.scrollTo(0, 0); // 确保页面滚动到顶部
      });
    });
    //  this.$refs.selectProject.$on('change', this.handleProjectChange);

  },
  methods: {
    
    validateBidTime() {
      console.log("this.formData", this.formData);
      if (this.formData.venueType === 2) {
        const bidOpeningTime = new Date(this.formData.bidOpeningTime);
        const occupyStartTime = new Date(
            this.formData.bidOpeningTime.slice(0, 10) +
            " " +
            this.occupyStartTime_temp +
            ":00"
        );
      console.log("bidOpeningTime", bidOpeningTime);
      console.log("occupyStartTime", occupyStartTime);
        if (occupyStartTime < bidOpeningTime) {
          this.$message.error("评标时间不能早于开标时间");
          return false;
        }
      }
      return true;
    },
    // 获取投标人数据的方法，这里假设从后端接口获取，需要根据实际情况修改
    getBidders() {
        try {
            // 假设后端接口为 /api/bidders，根据项目 ID 获取投标人数据
          //  const response = await axios.get(`/api/bidders?projectId=${this.formData.projectId}`);
            var info = { entType: 3 };
            getBidderListInfo(info).then((result) => {
                if (result.code == 200) {
                    //this.bidders = result.rows;
                    // 处理数据格式，将其转换为el-transfer组件的data属性要求的格式
                    const processedData = result.rows.map(item => ({
                        key: item.entId,
                        label: item.entName
                    }));
                    this.bidderSource = processedData;
                }
            })
        } catch (error) {
            console.error('获取投标人数据失败：', error);
        }
    },
    // 处理项目选择变化的方法
    // handleProjectChange() {
    //     if (this.formData.projectId) {
    //         // 根据项目 ID 获取获取投标人方式，这里假设从后端接口获取，需要根据实际情况修改
    //         axios.get(`/api/project/${this.formData.projectId}/bidderSource`).then((response) => {
    //             const bidderSourceValue = response.data.bidderSourceValue;
    //             if (bidderSourceValue === '推荐供应商') {
    //                 this.showBidderTransfer = true;
    //                 this.getBidders(); // 当需要显示穿梭框时，获取投标人数据
    //             } else {
    //                 this.showBidderTransfer = false;
    //             }
    //         }).catch((error) => {
    //             console.error('获取获取投标人方式失败：', error);
    //         });
    //     } else {
    //         this.showBidderTransfer = false;
    //     }
    // },
    change(value) {      
          if (value) {
              const selectedItem = this.projectIdOptions.find(
                  (item) => item.projectId === value
              );
              this.formData.projectStartTime = selectedItem.projectStartTime;
              this.formData.projectDeadline = selectedItem.projectDuration;
              this.formData.noticeName = selectedItem.projectName + "采购公告";
              const bidderSourceValue = selectedItem.getBidderMode;
              if (bidderSourceValue == 2) {
                  this.showBidderTransfer = true;
                  this.getBidders(); // 当需要显示穿梭框时，获取投标人数据
              } else {
                  this.showBidderTransfer = false;
              }

          } else {
              this.formData.projectStartTime = "";
              this.formData.projectDeadline = "";
          }
      },

    goFilePreparation() {
      this.saveStorage()
        .then((result) => {
          if (result.code === 200) {
            if (!this.formData.projectId) {
              this.$message.error("请先选择项目");
              return;
            }
            
            setTimeout(() => {
              this.getProjectByProjectId(this.formData.projectId,result.data.noticeId, result.data.pid);
              // this.$router.push({
              //   path: "/filePreparation/index",
              //   query: {
              //     noticeId: result.data.noticeId,
              //     projectId: this.formData.projectId,
              //   },
              // });
            });
          }
        })
        .catch((err) => {
          console.error("submitForm", err);
        });
    },
    getProjectByProjectId(projectId, noticeId, pId) {
      // 获取项目信息
      getProject(projectId).then((response) => {
        this.projectInfo = {
          projectName: response.data.projectName,
          projectCode: response.data.projectCode,
          deadLine: response.data.notice.noticeEndTime,
          procurementWey: response.data.tenderMode,
          purchasName: response.data.tendererName,
          projectType: response.data.projectType,
        };
        this.returnNotice(noticeId, projectId, pId);
      });
    },

    refreshPage(projectId, noticeId, pid){      
      if(this.type==1){
      //如果有新增页面，则关闭
      var obj0 = {
        path:"/tender/notice/change/"+pid+"/1",
        name:"noticeChange",
      }
      //如果之前是新增公告，则打开并跳转至带有noticeId的新标签页；如果是修改公告，则会跳转至标签页
    
      // this.$tab.openPage("用户管理", "/system/user").then(() => {
      //   // 执行结束的逻辑
      // })
      this.$tab.openPage("变更采购公告", "/tender/notice/change/"+noticeId+"/0").then(() => {
        // 执行结束的逻辑
        this.$tab.closePage(obj0).then(() => {
        });
      })
      }
      // this.$tab.openPage("新增采购公告", url);
    },
    returnNotice(noticeId, projectId, pid){
      if(this.type==1){
      //如果有新增页面，则关闭
      var obj0 = {
        path:"/tender/notice/change/"+pid+"/1",
        name:"noticeChange",
      }
      //如果之前是新增公告，则打开并跳转至带有noticeId的新标签页；如果是修改公告，则会跳转至标签页
    
      // this.$tab.openPage("用户管理", "/system/user").then(() => {
      //   // 执行结束的逻辑
      // })
      this.$tab.openPage("变更采购公告", "/tender/notice/change/"+noticeId+"/0").then(() => {
        // 执行结束的逻辑
        this.$tab.closePage(obj0).then(() => {
          this.confirm(noticeId, projectId);
        });
      })
      }else{
        this.confirm(noticeId, projectId);
      }
      // this.$tab.openPage("新增采购公告", url);
    },
    confirm(noticeId, projectId) {
      var info = { projectId: projectId };
      saveUinfo(info).then((result) => {
        if (result.code == 200) {
          // this.$tab.closeOpenPage();
          if (
            this.projectInfo.projectType == "0"
            // && this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationInfo/index",
              query: {
                projectId: projectId,
                noticeId: noticeId,
              },
            });
          } else if (
            this.projectInfo.projectType == "1"
            // && this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationServiceInfo/index",
              query: {
                projectId: projectId,
                noticeId: noticeId,
              },
            });
          } else if (
            this.projectInfo.projectType == "2"
              // && this.projectInfo.procurementWey == "1"
          ) {
            this.$router.push({
              path: "/filePreparationGoodsInfo/index",
              query: {
                projectId: projectId,
                noticeId: noticeId,
              },
            });
          }
        }
      });
    },
    handleInput(dict, data) {
      //新增操作
      this.formData.attachmentMap[dict.value] = data;
    },
    getFiles(dict) {
      // return this.formData.attachmentMap[dict.value];
      if(this.formData.attachmentMap!=undefined){
        return this.formData.attachmentMap[dict.value];
      }
      return "";
    },
    getFileNames(dict) {
      if(this.formData.attachmentNameMap!= undefined){
        return this.formData.attachmentNameMap[dict.value];
      }
      return "";
    },

    async getList() {
      this.loading = true;
      await this.getNext15Days();
      // 获取项目
      let queryParam = { delFlag: 0 };
      if (this.noticeId != 0) {
        queryParam = { delFlag: 0 };
      }
      await listProject(queryParam).then((response) => {
        this.projectIdOptions = response.rows;
      });
      // 获取场地信息
      await listInfo({ delFlag: 0 }).then((response) => {
        this.bidOpeningRoomOptions = response.rows.filter(
          (item) => item.venueType === "1"
        );
        this.bidEvaluationRoomOptions = response.rows.filter(
          (item) => item.venueType === "2"
        );
        if (
          this.bidOpeningRoomOptions != undefined &&
          this.bidOpeningRoomOptions.length > 0
        ) {
          this.activeOpenRoom = this.bidOpeningRoomOptions[0].venueId + "";
          this.activeEvaluationRoom =
            this.bidEvaluationRoomOptions[0].venueId + "";
        }
      });
      // 获取评标室、开标室占用信息
      await listOccupy({ delFlag: 0 }).then((response) => {
        this.spaceOccupancyList = response.rows;
      });
      // 根据公告id初始化采购公告
      console.log("noticeId  ", this.noticeId);
      console.log("type  ", this.$route.params.type);
      if (this.noticeId != 0) {
        // await getNotice(this.noticeId).then((response) => {
        //   this.formData = response.data;
        //   if(this.$route.params.type==1){
        //     this.formData.noticeId = null;
        //     this.formData.noticeStartTime = null;
        //     this.formData.noticeEndTime = null;
        //     this.formData.docAcquisitionStartTime = null;
        //     this.formData.docAcquisitionEndTime = null;
        //     this.formData.docResponseOverTime = null;
        //     this.formData.bidOpeningTime = null;
        //     this.formData.bidEvaluationTime = null;
        //   }
        //   this.formData.bidEvaluationMode =
        //     this.formData.bidEvaluationMode + "";
        //   this.formData.allowCoalition = this.formData.allowCoalition + "";
        //   this.formData.toSme = this.formData.toSme + "";
        //   this.formData.bidOpeningMode = this.formData.bidOpeningMode + "";
        //   if (this.formData.busiVenueOccupys != null) {
        //     this.bidOpeningRoom = this.formData.busiVenueOccupys[0];
        //     this.bidEvaluationRoom = this.formData.busiVenueOccupys[1];
        //   }
        //   if (this.formData.venueType == 2) {
        //     this.occupyStartTime_temp =
        //       this.bidEvaluationRoom.occupyStartTime.slice(11, 19);
        //   }
        //     if (this.formData.inviteBidder) {
        //         // 修改/变更时，将已选择的供应商重新赋值
        //         this.bidders = this.formData.inviteBidder.split(',');
        //     }
        // });
        await listOccupy({
          noticeId: this.noticeId,
          delFlag: 1,
        }).then((response) => { });
      }
      this.loading = false;
    },
    // 发布采购公告
    submitForm() {
      
      console.log("formData2  ", this.formData);
      return new Promise((resolve, reject) => {
        this.loading = true;
        for (let item of this.dict.type.busi_tender_notice_attachment) {
          if (
            item.raw.isEquals == 1 &&
            (this.formData.attachmentMap[item.value] == undefined ||
              this.formData.attachmentMap[item.value] == "")
          ) {
            this.$message.error(item.label + " 文件不能为空");
            reject("文件不能为空");
            this.loading = false;
            return;
          } else if (
            item.value === "5" &&
            (this.formData.attachmentMap[item.value] == undefined ||
              this.formData.attachmentMap[item.value] == "")
          ) {
            this.$message.error(item.label + " 文件不能为空");
            reject("文件不能为空");
            this.loading = false;
            return;
          }
        }
        this.dict.type.busi_tender_notice_attachment.forEach(
          (item, index) => { }
        );
        this.$refs["busiTenderNotice"].validate((valid) => {
          if (!valid) {
            this.$message.warning("请完善信息");
            this.loading = false;
            return;
          }
          if (!this.formData.venueType) {
            this.$message.warning("请选择开标室来源");
            reject("请选择开标室来源");
            this.loading = false;
            return;
          }
          // 判断开标室，评标室内容是否为空
          // 开标室判断this.bidOpeningRoom.venueId不为空，this.bidOpeningRoom.occupyStartTime和this.bidOpeningRoom.occupyEndTime不为空
          this.bidOpeningRoom.occupyStartTime = this.formData.bidOpeningTime;
          if (this.formData.venueType == 2) {
            this.bidOpeningRoom.bidOpenEndTime = "09:00";
          }

          if (this.formData.venueType == 1) {
            if (
              this.bidOpeningRoom.bidOpenEndTime == undefined ||
              this.bidOpeningRoom.bidOpenEndTime == "" ||
              this.bidOpeningRoom.venueId == undefined ||
              this.bidOpeningRoom.venueId == ""
            ) {
              this.$message.warning("开标室内容空缺");
              reject("开标室内容空缺");
              this.loading = false;
              return;
            }
            if (
              this.bidEvaluationRoom.bidEvaluationPeriod == undefined ||
              this.bidEvaluationRoom.bidEvaluationPeriod == ""
            ) {
              this.$message.warning("评标室内容空缺");
              reject("评标室内容空缺");
              this.loading = false;
              return;
            }
          } else if (this.formData.venueType == 2) {
            if (
              this.bidOpeningRoom.remark == undefined ||
              this.bidOpeningRoom.remark == ""
            ) {
              this.$message.warning("开标室内容空缺");
              reject("开标室内容空缺");
              this.loading = false;
              return;
            }
          }
          this.bidOpeningRoom.occupyStartTime = this.formData.bidOpeningTime;
          this.bidOpeningRoom.occupyEndTime =
            this.formData.bidOpeningTime.slice(0, 10) +
            " " +
            this.bidOpeningRoom.bidOpenEndTime +
            ":00";
          // this.bidOpeningRoom.venueType = 1;
          // this.bidEvaluationRoom.venueType = 2;
          this.formData.busiVenueOccupys = [];
          if (this.formData.venueType == 2) {
            this.formData.busiVenueOccupys.push(this.bidOpeningRoom);
          } else {
            this.formData.busiVenueOccupys.push(this.bidOpeningRoom);
            this.formData.busiVenueOccupys.push(this.bidEvaluationRoom);
          }

          // 保存
          this.formData.operationType = 2;
          this.formData.noticeStats = 1;
          this.formData.attachments = [];
          for (var key in this.attachmentsMap) {
            var v = this.attachmentsMap[key];
            for (var x in v) {
              this.formData.attachments.push(v[x]);
            }
          }
          this.formData.noticeStats = 1;
          this.formData.operationType = 3;
          this.formData.noticeStats = 1;
          if (this.formData.venueType == 1) {
            this.bidOpeningRoom.occupyStartTime = this.formData.bidOpeningTime;
            this.formData.bidOpeningEndTime = this.bidOpeningRoom.occupyEndTime;
          }
          //this.formData.inviteBidder=this.bidders.join(",");
        console.log("formData3  ", this.formData);
          changeAnnouncementInfo(this.formData).then((response) => {
            if (response.code == 200) {
              this.$modal.msgSuccess("发布成功");
              this.close();
            } else {
              this.formData.noticeStats = 0;
            }
            this.loading = false;
            this.$modal.msgSuccess(response.msg);
          });
        });
      });
    },
    // 暂存采购公告
    temporaryStorage() {
      this.formData.noticeStats = -1;
      this.$confirm("存场地信息不会被保存，是否继续保存，请确认", "提醒", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateAnnouncementInfo(this.formData).then((response) => {
            if (response.code == 200) {
              var reData = response.data;
              this.refreshPage(reData.projectId,reData.noticeId, reData.pid);
              // const obj = {
              //   path: "/tender/notice/change/" + response.data.noticeId+"/0",
              // };
              // this.$tab.refreshPage(obj);
            }
            this.$modal.msgSuccess(response.msg);
          });
        })
        .catch(() => {
          return;
        });
    },
    updateStorage() {
      this.formData.noticeStats = -1;
      this.$confirm("修改将删除已生成的采购文件和场地占用，请确认", "提醒", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          updateAnnouncementInfo(this.formData).then((response) => {
            if (response.code == 200) {
              const obj = {
                path: "/tender/notice/change/" + response.data.noticeId,
              };
              this.$tab.refreshPage(obj);
            } else {
              this.$modal.msg(response.msg);
            }
          });
        })
        .catch(() => {
          return;
        });
    },
    // 保存采购公告
    saveStorage() {
      return new Promise((resolve, reject) => {
        this.$refs["busiTenderNotice"].validate((valid) => {
          if (!valid) {
            this.$message.warning("请完善信息");
            reject("请完善信息");
            return;
          }
          if (!this.formData.venueType) {
            this.$message.warning("请选择开标室来源");
            reject("请选择开标室来源");
            this.loading = false;
            return;
          }
          if (!this.validateBidTime()) {
            reject("评标时间校验不通过");
            this.loading = false;
            return;
          }
          this.$confirm("保存后开标时间和开标地点将不能变更，请确认", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
            .then(() => {
              this.formData.operationType = 1;
              // 判断开标室，评标室内容是否为空
              // 开标室判断this.bidOpeningRoom.venueId不为空，this.bidOpeningRoom.occupyStartTime和this.bidOpeningRoom.occupyEndTime不为空
              this.bidOpeningRoom.occupyStartTime =
                this.formData.bidOpeningTime;
              if (this.formData.venueType == 2) {
                this.bidOpeningRoom.bidOpenEndTime = "09:00";
                this.bidEvaluationRoom.occupyStartTime =
                  this.formData.bidOpeningTime.slice(0, 10) +
                  " " +
                  this.occupyStartTime_temp +
                  ":00";
              }

              if (this.formData.venueType == 1) {
                if (
                  this.bidOpeningRoom.bidOpenEndTime == undefined ||
                  this.bidOpeningRoom.bidOpenEndTime == "" ||
                  this.bidOpeningRoom.venueId == undefined ||
                  this.bidOpeningRoom.venueId == ""
                ) {
                  this.$message.warning("开标室内容空缺");
                  reject("开标室内容空缺");
                  return;
                }
                if (
                  this.bidEvaluationRoom.bidEvaluationPeriod == undefined ||
                  this.bidEvaluationRoom.bidEvaluationPeriod == ""
                ) {
                  this.$message.warning("评标室内容空缺");
                  reject("评标室内容空缺");
                  return;
                }
              }
              else if (this.formData.venueType == 2) {
                if (
                  this.bidOpeningRoom.remark == undefined ||
                  this.bidOpeningRoom.remark == ""
                ) {
                  this.$message.warning("开标室内容空缺");
                  reject("开标室内容空缺");
                  return;
                }
              }
              this.bidOpeningRoom.occupyStartTime =
                this.formData.bidOpeningTime;
              this.bidOpeningRoom.occupyEndTime =
                this.formData.bidOpeningTime.slice(0, 10) +
                " " +
                this.bidOpeningRoom.bidOpenEndTime +
                ":00";
              this.formData.busiVenueOccupys = [];
              if (this.formData.venueType == 2) {
                this.formData.busiVenueOccupys.push(this.bidOpeningRoom);
                this.formData.busiVenueOccupys.push(this.bidEvaluationRoom);
              } else {
                this.formData.busiVenueOccupys.push(this.bidOpeningRoom);
                this.formData.busiVenueOccupys.push(this.bidEvaluationRoom);
              }
              this.formData.noticeStats = 0;
               this.formData.inviteBidder=this.bidders.join(",");
              updateAnnouncementInfo(this.formData).then((response) => {
                if (response.code == 200) {
                  const obj = {
                    path: "/tender/notice/change/" + response.data.noticeId + '/0',
                  };
                  this.$tab.refreshPage(obj);
                  resolve(response);
                }else{
                  this.formData.noticeStats = -1;
                }
                this.$modal.msgSuccess(response.msg);
              });
            })
            .catch(() => {
              this.formData.noticeStats = -1;
              return;
            });
        });
      });
    },

    downLoadFile(id, value) {
      let list = value.split("/");
      let fileName = list[list.length - 1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId: id,
        fileName: fileName,
        filePath: value,
        resource: value,
      };

      download("/common/download/resource", params, fileName);
    },

    cancel() {
      this.$tab.closePage();
    },

    resetForm() {
      this.$refs["busiTenderNotice"].resetFields();
    },
    // 根据场地占用id，初始化timePeriod
    initTimePeriod(occupyId) {
      // 根据场地id来过滤一下数据，只获得当前房间的数据
      const list = this.spaceOccupancyList.filter(
        (item) => item.venueId === parseInt(occupyId)
      );
      // 遍历list中的每个对象
      list.forEach((listItem) => {
        // 获取occupyStartTime的值
        var startTime = "";
        startTime = listItem.occupyStartTime.slice(0, 10);
        // if (listItem.venueType == 1) {
        //   if (listItem.bidEvaluationPeriod==0){
        //     startTime= listItem.occupyStartTime.slice(0, 10);
        //   }else{
        //     startTime=listItem.occupyStartTime;
        //   }
        // }

        // 转换成日期对象
        const startDate = new Date(startTime);
        // 格式化日期为"yyyy-MM-dd"
        const formattedStartDate = `${startDate.getFullYear()}-${(
          startDate.getMonth() + 1
        )
          .toString()
          .padStart(2, "0")}-${startDate
            .getDate()
            .toString()
            .padStart(2, "0")}`;

        // 遍历this.timePeriod数组，找到匹配的日期对象
        this.timePeriod.forEach((timePeriodItem, index) => {
          // 匹配日期
          if (timePeriodItem.time === formattedStartDate) {
            // 更新openPeriod和evaluation
            if (listItem.venueType == 1) {
              timePeriodItem.openPeriod.push(
                listItem.occupyStartTime.slice(11, 19) +
                "至" +
                listItem.occupyEndTime.slice(11, 19)
              );
            }
            // 根据需要更新evaluation
            // 例如，如果morning时间段有占用，则将Morning设为1
            if (listItem.bidEvaluationPeriod == 0) {
              timePeriodItem.evaluation.Morning = 1;
            } else if (listItem.bidEvaluationPeriod == 1) {
              timePeriodItem.evaluation.Afternoon = 1;
            } else if (listItem.bidEvaluationPeriod == 2) {
              timePeriodItem.evaluation.allDay = 1;
            }
          }

          /*  // 根据需要更新evaluation
          // 例如，如果morning时间段有占用，则将Morning设为1
          if (listItem.bidEvaluationPeriod == 0) {
            timePeriodItem.evaluation.Morning = 1;
          } else if (listItem.bidEvaluationPeriod == 1) {
            timePeriodItem.evaluation.Afternoon = 1;
          } else if (listItem.bidEvaluationPeriod == 2) {
            timePeriodItem.evaluation.allDay = 1;
          }*/
        });
      });
    },

    handleClick(tab, event) {
      this.getNext15Days();
      this.initTimePeriod(tab.name);
    },

    showOpeningRoom(e) {
      if (e) {
        this.getNext15Days();
        this.initTimePeriod(this.activeOpenRoom);
        this.openingRoom = true;
      }
    },
    showEvaluationRoom(e) {
      if (e) {
        this.getNext15Days();
        this.initTimePeriod(this.activeEvaluationRoom);
        this.evaluationRoom = true;
      }
    },
    // 获取从今天开始后15天的日期，格式为XXXX年XX月XX日
    getNext15Days() {
      const dates = [];
      // const startDate = new Date();
      let today;

      if (this.formData.noticeEndTime) {
        today = new Date(this.formData.noticeEndTime);
      } else {
        today = new Date();
      }
      //const today = new Date(this.formData.noticeEndTime);
      for (let i = 0; i < 15; i++) {
        const date = new Date(today.getTime() + i * 24 * 60 * 60 * 1000);
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const formattedDate = `${year}-${month}-${day}`;

        const dateObj = {
          time: formattedDate,
          openPeriod: [],
          evaluation: {
            Morning: 0,
            Afternoon: 0,
            allDay: 0,
          },
        };
        dates.push(dateObj);
      }
      this.timePeriod = dates;
      return dates;
    },


    changeOpeningRoom(value) {
      if (value) {
        const selectedItem = this.bidOpeningRoomOptions.find(
          (item) => item.venueId === value
        );
        this.bidOpeningRoom.venueName = selectedItem.venueName;
        this.bidOpeningRoom.occupyStartTime = selectedItem.projectDuration;
        this.bidOpeningRoom.venueType = selectedItem.venueType;
      } else {
        this.formData.projectStartTime = "";
        this.formData.projectDeadline = "";
      }
    },
    changeEvaluationRoom(value) {
      if (value) {
        const selectedItem = this.bidEvaluationRoomOptions.find(
          (item) => item.venueId === value
        );
        this.bidEvaluationRoom.venueName = selectedItem.venueName;
        this.bidEvaluationRoom.occupyStartTime = selectedItem.projectDuration;
        this.bidEvaluationRoom.venueType = selectedItem.venueType;
      } else {
        this.formData.projectStartTime = "";
        this.formData.projectDeadline = "";
      }
    },
    changebidOpeningPeriod(value) {
      this.bidOpeningRoom.occupyStartTime =
        this.formData.bidOpeningTime.slice(0, 10) + " " + value[0];
      this.bidOpeningRoom.occupyEndTime =
        this.formData.bidOpeningTime.slice(0, 10) + " " + value[1];
    },
    handleClose(done) {
      this.getNext15Days();
      done();
    },
    // 选择公告开始时间的时候判断是否选择项目（初始化intervalTime和intervalTimeType）
    selectProject(val) {

      this.formData.docAcquisitionStartTime = this.formData.noticeStartTime;
      if (!this.formData.projectId) {
        this.$message.warning("请先选择项目");
        this.formData.noticeStartTime = null;
        this.formData.noticeEndTime = null;
        return;
      }

      this.formData.docAcquisitionStartTime = this.formData.noticeStartTime;
      if (this.formData.noticeStartTime) {
        this.noticeEndTimeShow = true;
      }

      if (this.formData.noticeEndTime) {
        // 假设 this.formData.noticeStartTime 和 this.formData.noticeEndTime 都是有效的 Date 对象
        let noticeStartTime = new Date(this.formData.noticeStartTime);
        let noticeEndTime = new Date(this.formData.noticeEndTime);
        // 计算两个日期之间的差值（以毫秒为单位）
        let differenceInMilliseconds = noticeEndTime - noticeStartTime;

        // 将毫秒转换为天数
        // let differenceInDays = differenceInMilliseconds / (1000 * 60 * 60 * 24);
        // if (differenceInDays < 10) {
        //   this.$confirm(
        //     "根据《政府采购竞争性磋商采购方式管理暂行办法》的规定，从磋商公告至提交响应文件截止时间，须≥10个日历日，请确认是否提交，由此产生的后果自行承担。",
        //     "提示",
        //     {
        //       confirmButtonText: "确定",
        //       type: "warning",
        //     }
        //   )
        //     .then(() => { })
        //     .catch(() => { });
        // }

        this.formData.bidEvaluationTime = this.formData.noticeEndTime;
        this.formData.docAcquisitionEndTime = this.formData.noticeEndTime;
        this.formData.docResponseOverTime = this.formData.noticeEndTime;
        this.formData.bidOpeningTime = this.formData.noticeEndTime;
        this.occupyStartTime_temp = this.formData.noticeEndTime.slice(11, 19);
        this.bidOpeningRoom.bidOpenStartTime =
          this.formData.noticeEndTime.slice(11, 19);
      }
    },
    // 变更公告跳转
    changeNotice() {
      // 需要一个标识表明是新增
      this.$router.push({
        path: "/tender/notice/change/" + this.noticeId,
        query: { isAdd: true },
      });
    },
    // 关闭当前页
    close() {
      this.$tab.closePage();
    },
  },
};
</script>
<style>
.tender {
  padding: 0 50px;
}

.makeTenserFile {
  width: 208px;
  border: rgba(0, 0, 0, 1) solid 1px;
  border-radius: 4px;
  background-color: rgba(185, 248, 191, 1);

  font-family: Microsoft YaHei;
  color: rgba(80, 80, 80, 1);
  line-height: 150%;
  font-size: 14px;

  text-align: center;
  vertical-align: middle;
}
.makeTenserFile:hover :active :focus {
  color: rgba(80, 80, 80, 1);
}

.attachment {
  height: 27px;
  left: 64px;
  top: 668px;
  color: rgba(80, 80, 80, 1);
  font-size: 18px;
  line-height: 150%;
  text-align: left;
}
.line {
  width: 100%;
  height: 2px;
  left: 64px;
  top: 700px;
  color: rgba(80, 80, 80, 1);
  background-color: rgba(58, 25, 236, 1);
  font-size: 14px;
  line-height: 150%;
  text-align: center;

  margin-bottom: 25px;
}
.option {
  text-align: center;
}
.select-option {
  z-index: 999 !important;
}
</style>
<style scoped>
.el-form-item {
  margin-bottom: 0px;
}
/deep/ .el-upload {
  float: right;
}
/deep/ .el-upload-list {
  width: 90%;
}
/deep/ .upload > .el-form-item__content {
  border-bottom: rgba(153, 153, 153, 1) solid 1px;
}
</style>
