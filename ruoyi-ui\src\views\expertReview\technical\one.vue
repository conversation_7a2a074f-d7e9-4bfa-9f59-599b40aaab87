<template>
  <div style="min-height:57vh;display:flex">
    <div style="min-height:57vh;width:79%;">
      <div style="display:flex;justify-content: space-between;align-items: center;border-bottom: 2px solid #176ADB;padding:15px 20px">
        <div style="display:flex; height: 36px;font-weight: 700;font-size: 24px;color: #333333;">
          <div>技术标评审</div>
          <div style="display:grid;justify-items: center;position: relative;bottom:-30px">
            <div style="font-size:12px;">操作步骤（点击放大）</div>
            <el-image style="width: 80px;height: 30px;margin-right:20px" :src="srcList[0]" :preview-src-list="srcList">
            </el-image>
          </div>
        </div>
        <div style="text-align:right">
          <el-button class="item-button" @click="bidInquiry">询标</el-button>
          <el-button class="item-button" v-if="expertInfo.expertLeader==1" @click="secondOffer">发起二次报价</el-button>
          <div style="margin-top:20px">
            <el-button class="item-button" style="background-color: #176ADB;color:#FFFFFF;border: 1px solid #176ADB;" @click="showResponseFile()">响应文件</el-button>
            <el-button class="item-button" style="background-color: #176ADB;color:#FFFFFF;border: 1px solid #176ADB;" @click="viewPurchasing">采购文件</el-button>
            <el-button class="item-button" style="background-color: #176ADB;color:#FFFFFF;border: 1px solid #176ADB;" @click="fileContrast">对比</el-button>
          </div>
        </div>
      </div>
      <div style="display:flex;justify-content: center;height:82%">
        <div
          v-show="responseShow"
          style="width:49%"
          :style="double ? 'border-right:1px solid #176ADB;' :''"
        >
          <pdfView
            ref="response"
            :pdfurl="responsePdf"
            :uni_key="'response'"
          ></pdfView>
        </div>
        <div
          v-show="procurementShow"
          style="width:49%"
          :style="double ? 'border-left:1px solid #176ADB;' :''"
        >
          <pdfView
            ref="procurement"
            :pdfurl="procurementPdf"
            :uni_key="'procurement'"
          ></pdfView>
        </div>
      </div>
    </div>
    <div style="min-height:57vh;width:1%;background-color:#F5F5F5">
    </div>
    <div style="min-height:57vh;width:20%">
      <div style="display:flex;justify-content: center;align-items: center;border-bottom: 2px solid #176ADB;padding:15px 20px">
        <el-select
          style="width:100%"
          v-model="supplier"
          placeholder="请选择供应商"
          @change="handleChange"
        >
          <el-option
            v-for="item in options"
            :key="item.bidderName"
            :label="item.bidderName"
            :value="item.bidderName"
          >
          </el-option>
        </el-select>
      </div>
      <div style="padding:15px 20px">
        <div
          v-for="(item, index) in scoringSystem.uitems"
          :key="index"
          class="factor-item"
          style="margin-bottom:10px"
        >
          <div>
            <div class="factors">
              <div style="display: flex;justify-content: flex-start;align-items: center;text-align: left;width:98%">
                <div style="
                      cursor: pointer;
                      font-family: SourceHanSansSC-Bold;
                      font-weight: 500;
                      font-size: 16px;
                      color: #333333;
                      letter-spacing: 0;
                    " @click="showInfo(item)">
                  {{ item.itemName }}
                </div>
              </div>
              <div style="display:flex;width:100%;justify-content: flex-end;padding:10px">
                <div v-if="!(item.scoreLevel.length == 0 || item.scoreLevel == null || item.scoreLevel == undefined)">
                  <el-radio
                    v-for="(score,index) in item.scoreLevel.split(',')"
                    :key="index"
                    v-model="defaultRatingArray[item.entMethodItemId].state"
                    :label="score"
                  ><span style="color:green;font-size:16px">{{ score }}</span></el-radio>
                </div>
                <div v-else>
                  <el-input
                    placeholder="请输入分数"
                    v-model="defaultRatingArray[item.entMethodItemId].state"
                    @input="validateScore(item.entMethodItemId, $event)"
                  ></el-input>
                </div>
              </div>

            </div>
          </div>
        </div>
        <div style="display:flex;margin:32px 0;justify-content: space-evenly;">
          <!-- <div><el-button
              class="item-button-little"
              style="background-color:#F5F5F5;color:#176ADB"
              @click="save"
            >保存</el-button></div> -->
          <div><el-button
              class="item-button-little"
              style="background-color:#176ADB"
              @click="submit"
            >提交</el-button></div>
        </div>

        <div style="text-align:left;font-size:14px">
          <div style="font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 15px;color: #176ADB;letter-spacing: 0;">评审内容：</div>
          <div style="padding:6px 30px" v-html="selectNode.itemRemark"></div>
        </div>
      </div>
    </div>
  </div>

</template>

<script>
import {
  supplierInfo,
  approvalProcess,
  scoringFactors,
  checkReviewSummary,
  filesById,
} from "@/api/expert/review";
import { getDetailByPsxx } from "@/api/evaluation/detail/";
import { editEvalExpertScoreInfo } from "@/api/evaluation/expertStatus";

export default {
  data() {
    return {
      options: [],
      scoringSystem: [],
      selectNode: {},
      supplier: "",
      selectSupplier: {},
      expertInfo: {},
      defaultRatingArray: {},
      file: {},
      responseShow: false,
      procurementShow: false,
      double: false,
      factorList: [],
      entDocResponsePage: {},
      factorsPage: {},
      bidderFactor: {},

      responsePdf: null,
      procurementPdf: null,
      currentMaxScore: null,
      srcList: ["/evalution/help.jpg"]
    };
  },
  methods: {
    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      this.entDocResponsePage = JSON.parse(
        localStorage.getItem("entDocResponsePage")
      );
      supplierInfo({ projectId: this.$route.query.projectId }).then(
        (response) => {
          if (response.code == 200) {
            this.options = response.rows.filter(item => item.isAbandonedBid == 0);
          } else {
            this.$message.warning(response.msg);
          }
        }
      );
      approvalProcess(this.$route.query.projectId, expertInfo.resultId).then(
        (response) => {
          if (response.code == 200) {
            this.scoringSystem =
              response.data.scoringMethodUinfo.scoringMethodItems.find(
                (item) => {
                  return (
                    item.scoringMethodItemId ==
                    this.$route.query.scoringMethodItemId
                  );
                }
              );
            localStorage.setItem(
              "evalProjectEvaluationProcess",
              JSON.stringify(this.scoringSystem.evalProjectEvaluationProcess)
            );
            // TODO 首先生成长度为this.scoringSystem.length的数组，结构为{state：false，reason：“”}
            this.defaultRatingArray = this.scoringSystem.uitems.reduce(
              (acc, _, index) => {
                acc[this.scoringSystem.uitems[index].entMethodItemId] = {
                  state: null,
                  reason: "",
                };
                console.log("scoringSystem", this.scoringSystem);

                if ((this.scoringSystem.uitems[index].scoreLevel.length == 0 || this.scoringSystem.uitems[index].scoreLevel == null || this.scoringSystem.uitems[index].scoreLevel == undefined)){
                  this.currentMaxScore=this.scoringSystem.uitems[index].score;
                console.log("currentMaxScore", this.currentMaxScore);
                }
                return acc;
              },
              {}
            );
          } else {
            this.$messgae.warning(response.msg);
          }
        }
      );
      filesById(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.file = response.data;
          if (this.file.tenderNoticeFilePath != undefined) {
            this.procurementPdf = this.file.tenderNoticeFilePath;
          }
          if (this.file.file != undefined) {
            this.responsePdf = this.file.file[0];
          }
        } else {
          this.$message.warning(response.msg);
        }
      });
      const itemString = localStorage.getItem("expertInfo");
      this.expertInfo = JSON.parse(itemString);
    },
    handleChange(value) {
      if(Object.keys(this.selectSupplier).length != 0){
        this.tmpSave();
      }
      this.selectSupplier = this.options.find((item) => {
        return item.bidderName == value;
      });

      // 根据bidderid获取供应商因素及其对应页码
      this.bidderFactor = this.factorsPage[this.selectSupplier.bidderId];

      const data = {
        expertResultId: this.expertInfo.resultId,
        projectId: this.$route.query.projectId,
        scoringMethodItemId: this.$route.query.scoringMethodItemId,
      };
      getDetailByPsxx(data).then((response) => {
        if (response.code == 200) {
          this.factorList = response.data;
          const factor = this.factorList.find((item) => {
            return item.bidderName == value;
          }).evalExpertEvaluationDetails;
          if (factor != null) {
            factor.map((item) => {
              this.defaultRatingArray[item.scoringMethodUitemId].reason =
                item.evaluationRemark;
              this.defaultRatingArray[item.scoringMethodUitemId].state =
                item.evaluationResult;
            });
          } else {
            Object.keys(this.defaultRatingArray).forEach((key) => {
              this.defaultRatingArray[key].state = null;
              this.defaultRatingArray[key].reason = "";
            });
          }
        } else {
          this.$message.warning(response.msg);
        }
      });
      this.showResponseFile();
      // 重置最大分数值
      // this.currentMaxScore = null;
    },
    validateScore(itemId, event) {
      const inputValue = parseFloat(event);
      console.log("inputValue", inputValue);
      console.log("currentMaxScore",this.currentMaxScore);

      if (!isNaN(inputValue) && this.currentMaxScore) {
        const maxScore = parseFloat(this.currentMaxScore);
        if (inputValue > maxScore) {
          this.$message.warning(`输入分数不能超过${maxScore},请重新输入`);
          // 将输入值限制为最大分数值
          this.defaultRatingArray[itemId].state = "";
        }
      }
    },
    showResponseFile() {
      if (Object.keys(this.selectSupplier).length === 0) {
        this.$message.warning("请选择供应商");
      } else {
        this.double = false;
        this.procurementShow = false;
        this.responseShow = true;
        this.responsePdf = this.file.file[this.selectSupplier.bidderId];
      }
    },
    fileContrast() {
      if (Object.keys(this.selectSupplier).length === 0) {
        this.$message.warning("请选择供应商");
      } else {
        this.double = true;
        this.procurementShow = true;
        this.responseShow = true;
        this.responsePdf = this.file.file[this.selectSupplier.bidderId];
      }
    },
    showInfo(item) {
      if (Object.keys(this.bidderFactor).length != 0) {
        this.selectNode = item;
        this.$refs.response.skipPage(
          this.bidderFactor[this.selectNode.itemName]
        );
        this.$refs.procurement.skipPage(
          this.bidderFactor[this.selectNode.itemName]
        );
        // 获取当前项目的最大分数值，假设scoreLevel中第一个值为最大值（可根据实际规则调整）
        // const maxScore = item.score;
        // console.log("此项目最大分值是："+maxScore);
        // this.currentMaxScore = maxScore; // 将最大分数值存储到实例变量中，方便后续校验使用
      } else {
        this.$message.warning("请先选择供应商");
      }
    },
    initDefaultRatingArray(){
      Object.keys(this.defaultRatingArray).forEach((key) => {
        this.defaultRatingArray[key].state = null;
        this.defaultRatingArray[key].reason = "";
      });
    },
    tmpSave(){
      console.log("-------开始保存评审结果----------------");
        var ratingArray = JSON.parse(JSON.stringify(this.defaultRatingArray));
      
        var data = [];
        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
          const item = this.scoringSystem.uitems[index];
          const itemId = item.entMethodItemId;
          // 获取当前项对应的评分结果
          const evaluationResult = ratingArray[itemId].state;          
          if (evaluationResult === null || evaluationResult === "") {
            // 如果评分结果为空，则不保存此条信息
            console.log("-------评分结果为空，不保存此条信息----------------");
            continue;  
          }
          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
          const evaluationRemark = ratingArray[itemId].reason || "";
          data.push({
            scoringMethodUitemId: itemId,
            expertResultId: this.expertInfo.resultId,
            entId: this.selectSupplier.bidderId,
            evaluationResult: evaluationResult,
            evaluationRemark: evaluationRemark
          });
        }
        if(data.length>0){
            console.log("-------开始后台保存评审结果----------------");
            var r = null;
          scoringFactors(data).then((response) => {
            console.log(response.msg);
            if (response.code == 200) {
              this.$message.success("保存成功");
            } else {
              this.$message.warning(response.msg);
            }
            r = response;
          });
        }else{
          r = {code:0};
        }
          return new Promise((resolve, reject) => {
          resolve(r);
        });
    },
    save() {
      if (this.supplier == "") {
        this.$message.warning("请选择供应商");
      } else {
        //const data = this.generatingSavedData();
        var data = [];
        for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
          const item = this.scoringSystem.uitems[index];
          const itemId = item.entMethodItemId;
          // 获取当前项对应的评分结果
          const evaluationResult = this.defaultRatingArray[itemId].state;
          if (evaluationResult === null || evaluationResult === "") {
            // 如果评分结果为空，弹出提示，提示内容包含该项的itemName
            this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
            return; // 直接返回，不再继续构建数据，等待用户填写完整
          }
          // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
          const evaluationRemark = this.defaultRatingArray[itemId].reason || "";
          data.push({
            scoringMethodUitemId: itemId,
            expertResultId: this.expertInfo.resultId,
            entId: this.selectSupplier.bidderId,
            evaluationResult: evaluationResult,
            evaluationRemark: evaluationRemark
          });
        }

        scoringFactors(data).then((response) => {
          if (response.code == 200) {
            this.$message.success(response.msg);
          } else {
            this.$message.warning(response.msg);
          }
        });
      }
    },
    // 生成保存数据
    generatingSavedData() {
      var data = [];
      for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
        const item = this.scoringSystem.uitems[index];
        const itemId = item.entMethodItemId;
        // 获取当前项对应的评分结果
        const evaluationResult = this.defaultRatingArray[itemId].state;
        if (evaluationResult === null || evaluationResult === "") {
          // 如果评分结果为空，弹出提示，提示内容包含该项的itemName
          this.$message.warning(`请填写评分项：${item.itemName} 的评分结果`);
          return; // 直接返回，不再继续构建数据，等待用户填写完整
        }
        // 获取当前项对应的评分备注，进行非空判断，为空则赋予默认值（这里设为空字符串）
        const evaluationRemark = this.defaultRatingArray[itemId].reason || "";
        data.push({
          scoringMethodUitemId: itemId,
          expertResultId: this.expertInfo.resultId,
          entId: this.selectSupplier.bidderId,
          evaluationResult: evaluationResult,
          evaluationRemark: evaluationRemark
        });
      }

      /*for (let index = 0; index < this.scoringSystem.uitems.length; index++) {
        data.push({
          scoringMethodUitemId:
            this.scoringSystem.uitems[index].entMethodItemId,
          expertResultId: this.expertInfo.resultId,
          entId: this.selectSupplier.bidderId,
          evaluationResult:
            this.defaultRatingArray[
              this.scoringSystem.uitems[index].entMethodItemId
            ].state,
          evaluationRemark:
            this.defaultRatingArray[
              this.scoringSystem.uitems[index].entMethodItemId
            ].reason,
        });
      }*/
      return data;
    },
    submit() {
      this.tmpSave().then((x) => {
      const data = {
        projectId: this.$route.query.projectId,
        expertResultId: this.expertInfo.resultId,
        scoringMethodItemId: this.$route.query.scoringMethodItemId,
      };
      checkReviewSummary(data).then((response) => {
        if (response.code == 200) {
          // 修改专家进度
          const status = {
            evalExpertScoreInfoId: JSON.parse(
              localStorage.getItem("evalExpertScoreInfo")
            ).evalExpertScoreInfoId,
            evalState: 1,
          };
          editEvalExpertScoreInfo(status).then((res) => {
            if (res.code == 200) {
              this.$message.success("提交成功");
            }
          });
          this.$emit("send", "two");
        } else {
          this.$message.warning(response.msg);
        }
      });
    })
    },
    // 查看采购文件
    viewPurchasing() {
      this.double = false;
      this.responseShow = false;
      this.procurementShow = true;
    },
    // 跳转到二次报价
    secondOffer() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      this.$router.push({ path: "/secondOffer", query: query });
    },
    // 跳转到询标
    bidInquiry() {
      const query = {
        projectId: this.$route.query.projectId,
        zjhm: this.$route.query.zjhm,
        scoringMethodItemId: JSON.parse(
          localStorage.getItem("tenderOfferScoringMethodItems")
        ),
      };
      console.log("query", query);
      this.$router.push({ path: "/bidInquiry", query: query });
    },
    // 获取因素对应页码
    getFactorsPage() {
      this.factorsPage = JSON.parse(localStorage.getItem("entDocResponsePage"));
    },
  },
  mounted() {
    this.init();
    this.getFactorsPage();
  },
};
</script>

<style lang="scss" scoped>
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  border: 1px solid #979797;
  width: 150px;
  height: 36px;
  margin: 0 10px;
  font-weight: 700;
  font-size: 17px;
  border-radius: 6px;
  color: #333333;
  &:hover {
    color: #333333;
  }
}
.item-button-little {
  width: 124px;
  height: 36px;
  font-weight: 700;
  font-size: 18px;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}
.text {
  ::v-deep .el-textarea__inner {
    background-color: #f5f5f5;
    border-radius: 0;
    border: 1px solid #f5f5f5;
  }
}
</style>

