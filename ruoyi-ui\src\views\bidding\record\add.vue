<template>
  <div class="tender">
  <!-- 添加或修改投标记录对话框 -->
    <el-card class="box-card" :close-on-click-modal="false" :title="title"  width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px" :disabled="isFormDisabled">
        <el-form-item label="项目名称" prop="projectId">
<!--          <el-input v-model="form.projectId" placeholder="请输入项目id" />-->
          <el-select
            v-model="form.projectId"
            placeholder="请选择项目"
            filterable
            clearable
            ref="selectProject"
            :disabled="initProject"
            :style="{ width: '20%' }"
            @change="change($event)"
          >
            <el-option
              v-for="(item, index) in projectIdOptions"
              :key="index"
              :label="item.projectName"
              :value="item.projectId"
              :disabled="item.disabled"
            ></el-option>
          </el-select>
        </el-form-item>
<!--         <el-form-item label="投标报价" prop="bidAmount">
         <el-input v-model="form.bidAmount" placeholder="请输入投标报价" />
          <el-input-number
            v-model="form.bidAmount"
            :min="0.1"
            :step="0.01"
            :precision="2"
            step-strictly
            placeholder="请输入预算金额"
            :style="{ width: '20%' }"
          ></el-input-number>
        </el-form-item> -->
<!--        <el-form-item label="报价单位" prop="priceUnit">-->
<!--          <el-input v-model="form.priceUnit" placeholder="请输入报价单位" />-->
<!--        </el-form-item>-->
        <el-form-item
          v-for="dict in dict.type.busi_bidding_record_attachment"
          :key="dict.label"
          label-width="120px"
          :label="dict.label"
          class="upload"
        >
          <template>
            <FileUpload
              :value="getImgPath(dict)"
              @input="handleInput(dict, $event)"
              :fileType="['tbwj']"
              :isShowTip="false"
              :showOnly="isFormDisabled"
            ></FileUpload>
          </template>
        </el-form-item>
      </el-form>
    </el-card>
      <div slot="footer" class="dialog-footer" style="text-align: center;">
          <!--          -->
          <el-button type="primary" @click="submitForm" v-show="!isFormDisabled">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
      </div>
  </div>
</template>

<script>
import {listRecord, getRecord, delRecord, addRecord, updateRecord, getCancelInfoRecord} from "@/api/bidding/record";
import {listProject, supplierisWinProdect, supplierLookProdect} from "@/api/tender/project";
import {downLoadFile} from "@/api/bid/evaluation";
import {download} from "@/utils/request";
import {getDicts} from "@/api/system/dict/data";
import {getIntention} from "@/api/tender/intention";

export default {
  name: "Record",
  dicts: ['base_yes_no','busi_bidding_record_attachment'],
  data() {
    return {
      isFormDisabled:false,
      // 遮罩层
      loading: true,
      initProject:false,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 投标记录表格数据
      recordList: [],
      evaluationList: [],
      attachmentTypes: [],
      attachmentsMap: {},
      projectIdOptions:[],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记 0正常 1删除时间范围
      daterangeUploadTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCancelTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeCreateTime: [],
      // 删除标记 0正常 1删除时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        uploadIp: null,
        uploadTime: null,
        cancelTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      tproject:{},
      // 表单参数
      form: {
        attachments:[],
        projectId:"",
      },
      rules: {
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "blur" }
        ],
       /* bidderId: [
          { required: true, message: "投标人id不能为空", trigger: "blur" }
        ],
        bidderName: [
          { required: true, message: "投标人名称不能为空", trigger: "blur" }
        ],
        bidderCode: [
          { required: true, message: "投标人代码不能为空", trigger: "blur" }
        ],
        uploadIp: [
          { required: true, message: "上传ip不能为空", trigger: "blur" }
        ],
        uploadTime: [
          { required: true, message: "上传时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标记 0正常 1删除不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],*/
      }
    };
  },
  created() {
    this.checkBiddingId();
    this.getAttachmentTypes();
  },
  methods: {
    checkBiddingId() {
      const biddingId = this.$route.params.biddingId;
        console.log(biddingId)
      if(biddingId === 0||biddingId === undefined){
        this.$route.meta.title = "递交响应文件"
        //this.isFormDisabled=false;
        this.getList();

      }else{
          this.isFormDisabled=this.$route.params.isFormDisabled=="true"?true:false;
          if(this.isFormDisabled){
              this.$route.meta.title = "查看响应文件"
            this.loadIntentionDetail(biddingId);

          }else{
              this.$route.meta.title = "修改响应文件"
            this.loadIntentionDetail(biddingId);

          }
      }

    },
    loadIntentionDetail(biddingId) {
      listProject({params:{isScope:'1'}}).then((response) => {
        this.projectIdOptions = response.rows;
        console.log(this.projectIdOptions)
      });
      // 加载已有意图详情的逻辑
      getRecord(biddingId).then(response => {
        this.form = response.data;
        // 使用reduce方法将attachments数组收集成一个映射
        this.attachmentsMap = response.data.attachments.reduce(
          (accumulator, attachment) => {
            // 如果accumulator中还没有这个fileType的键，则创建一个新数组
            if (!accumulator[attachment.fileType]) {
              accumulator[attachment.fileType] = [];
            }
            // 将当前attachment添加到对应fileType的数组中
            accumulator[attachment.fileType].push(attachment);
            return accumulator;
          },
          {}
        );
        console.log(this.attachmentMap)
      });
    },
    handleInput(dict, data) {
      //新增操作
      if (!data || data == "") {
        delete this.attachmentsMap[dict.value];
      } else {
        let fileList = data.split(",");
        fileList = fileList.map((item) => {
          return {
            fileName: item.substring(item.lastIndexOf("/") + 1),
            fileType: dict.value,
            fileSuffix: item.substring(item.lastIndexOf(".") + 1),
            filePath: item,
          };
        });
        this.attachmentsMap[dict.value] = fileList;
      }
      console.log(this.attachmentsMap);
    },
    getImgPath(dict) {
      if (
        this.attachmentsMap[dict.value] &&
        this.attachmentsMap[dict.value].length > 0
      ) {
        let arr = this.attachmentsMap[dict.value];
        return arr
          .map((item) => {
            return item.filePath;
          })
          .join(",");
      }
      return "";
    },
    change(value) {
      this.queryParams.params = {};
      this.queryParams.projectId = value;
      console.log("projectId:"+value);
      this.projectIdOptions.forEach((item, index) => {
        if(item.projectId==value){
          this.tproject = item;
        }
      });
      console.info(this.tproject);
      listRecord(this.queryParams).then(response => {
        this.projectInfoList = response.rows;
        console.log(this.projectInfoList);
      });
    },
    downLoadFile(id,value){
      console.log(id);
      let list = value.split("/");
      let fileName = list[list.length-1];
      //这是文件路径参数，因为download函数需要传三个参数，这是第二个参数
      let params = {
        projectId:id,
        fileName:fileName,
        filePath:value,
        resource:value
      }
      downLoadFile(params).then(response => {
        if (response["code"]==200){
          download("/common/download/resource", params, fileName);
        }
      });

    },

    field102BeforeUpload(file) {
      let isRightSize = file.size / 1024 / 1024 < 50
      if (!isRightSize) {
        this.$message.error('文件大小超过 50MB')
      }
      let isAccept = new RegExp('.pdf').test(file.type)
      if (!isAccept) {
        this.$message.error('应该选择.pdf类型的文件')
      }
      return isRightSize && isAccept
    },
    getAttachmentTypes(){
      getDicts("busi_bidding_record_attachment").then((result) => {
        console.info(result);
        if(result.code==200){
          this.attachmentTypes = result.data;
        }
      });
    },
    getList() {
      this.loading = true;
      supplierLookProdect().then((response) => {
        this.projectIdOptions = response.data;
        if (this.$route.query.projectId) {
        this.form.projectId = parseInt(this.$route.query.projectId);
        this.$refs.selectProject.$emit("change", this.form.projectId);
        this.initProject = true;
      }else{
          this.form ={};
        }
        console.log(this.form)
        this.loading = false;
      });

    },
    // 取消按钮
    cancel() {
      this.$tab.closePage();
    },
    // 表单重置
    reset() {
      this.form = {
        biddingId: null,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        uploadIp: null,
        uploadTime: null,
        cancelTime: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null

      };
      this.resetForm("form");
    },

    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.biddingId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /*撤回*/
    cancelInfo(row) {
      const biddingId = row.biddingId || this.ids
      getCancelInfoRecord(biddingId).then(response => {
       // this.form = response.data;
        this.$modal.msgSuccess("撤回成功");
        this.open = false;
        this.getList();
      });
    },
    /** 提交按钮 */
    submitForm() {
      if(this.form.bidAmount>this.tproject.budgetAmount || this.form.bidAmount<this.tproject.budgetAmount*0.7){
        this.$confirm('投标报价高于预算价或与预算价差距较大，请确认是否提交', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.submit();
        }).catch(() => {
          return;
        });
      }else{
        this.submit();
      }
    },

    submit(){
          this.$refs["form"].validate(valid => {
            if (valid) {
              this.form.attachments = [].concat(
                ...Object.values(this.attachmentsMap));
              if (this.form.biddingId != null) {
                updateRecord(this.form).then(response => {
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                  this.reset();
                  this.$tab.closePage();
                });
              } else {
                addRecord(this.form).then(response => {
                  this.$modal.msgSuccess("新增成功");
                  this.open = false;
                  this.getList();
                  this.reset();
                  this.form.attachments = [];
                  this.$tab.closePage();
                });
              }
        }
      });
    }

  }
};
</script>
<style>
.tender {
  padding: 0 50px;
}



</style>
