<template>
  <div>
    <BidHeadthree></BidHeadthree>
    <div class="info">
      <div class="content">
        <el-header
          height="70px"
          class="header"
        >
          <div class="center">
            专家复核
          </div>
        </el-header>
        <el-main>
          <confirmTable
            :label="'资格性评审'"
            :tableData="qualificationTableData"
            :column="column"
            @update="init"
          ></confirmTable>
          <el-divider></el-divider>

          <confirmTable
            :label="'符合性评审'"
            :tableData="complianceTableData"
            :column="column"
            @update="init"
          ></confirmTable>
          <el-divider></el-divider>

          <template v-if="project.tenderMode!=3">
            <confirmTable
                :label="'技术标评审'"
                :tableData="technicalTableData"
                :column="column"
                @update="init"
            ></confirmTable>
            <el-divider></el-divider>

            <confirmTable
                :label="'商务标评审'"
                :tableData="businessTableData"
                :column="column"
                @update="init"
            ></confirmTable>
          </template>
          <div class="operation">
            <el-button
              class="item-button"
              @click="completed"
            >返回</el-button>
          </div>
        </el-main>
      </div>
    </div>
    <Foot></Foot>
  </div>

</template>

<script>
import { expertInfoById } from "@/api/expert/review";
import { getProject } from "@/api/tender/project";
import confirmTable from "./summaryConfirm/table";
import { confirmList } from "@/api/evaluation/detail";
export default {
  name: "summaryConfirm",
  components: { confirmTable },
  data() {
    return {
      project: {
        projectName: "",
      },
      qualificationTableData: [],
      complianceTableData: [],
      technicalTableData: [],
      businessTableData: [],
      tenderOffer: [],
      column: [],
    };
  },
  methods: {
    init() {
      // 根据项目id查询项目信息
      getProject(this.$route.query.projectId).then((response) => {
        if (response.code == 200) {
          this.project = response.data;
        } else {
          this.$message.warning(response.msg);
        }
      });
      // const expertInfo = JSON.parse(localStorage.getItem("expertResultId"));
      const data = {
        expertResultId: localStorage.getItem("expertResultId"),
        projectId: this.$route.query.projectId,
      };
      // 初始化列表
      confirmList(data).then((response) => {
        if (response.code == 200) {
          const busiBidderInfos = response.data.busiBidderInfos;
          const uitems = response.data.uitems;
          const scoringMethodItems = response.data.scoringMethodItems;

          this.column = busiBidderInfos.map((bidder) => {
            return bidder.bidderName;
          });

          scoringMethodItems.map((name) => {
            // 生成资格性评审格式
            const qualificationReview = uitems[name.scoringMethodItemId].map(
              (item) => {
                const result = {
                  factor: item.itemName,
                };
                if (item.evalExpertEvaluationDetails) {
                  item.evalExpertEvaluationDetails.forEach((detail) => {
                    // 根据entId找到供应商名称
                    const bidder = busiBidderInfos.find(
                      (bidder) => bidder.bidderId === detail.entId
                    );
                    if (bidder) {
                      result[bidder.bidderName] = {
                        evaluationResult: detail.evaluationResult,
                        expertEvaluationId: detail.expertEvaluationId,
                        scoreLevel: item.scoreLevel,
                      };
                    }
                  });
                }
                console.log(result);
                return result;
              }
            );

            switch (name.itemName) {
              case "资格性评审":
                this.qualificationTableData = qualificationReview;
                break;
              case "技术标评审":
                this.technicalTableData = qualificationReview;
                break;
              case "商务标评审":
                this.businessTableData = qualificationReview;
                break;
              case "符合性评审":
                this.complianceTableData = qualificationReview;
                break;
              case "投标报价打分":
                this.tenderOffer = qualificationReview;
                break;
            }
          });
        }
      });
    },
    // 确认
    completed() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.info {
  background-color: #f5f5f5;
  display: flex;
  justify-content: center;
  align-items: center;
}
.content {
  background-color: #fff;
  width: 70%;
  min-height: 64vh;
  margin: 20px 0;
}
.header {
  background-color: #176adb;
  display: flex;
  justify-content: center;
  align-items: center;
  .center {
    display: flex;
    font-size: 22px;
    color: #ffffff;
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
  padding: 60px 110px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.little-title {
  color: rgba(80, 80, 80, 1);
  font-size: 14px;
}
.item-button {
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: #176adb;
  color: #fff;
  &:hover {
    color: #fff;
  }
}
.item-button-little {
  border: #333 1px solid;
  width: 124px;
  height: 32px;
  background-color: rgba(151, 253, 246, 1);
  color: rgba(0, 0, 0, 1);
  &:hover {
    color: rgba(0, 0, 0, 1);
  }
}
.factors {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
