<template>
  <div class="info">
    <el-card>
      <div class="el-table el-table--enable-row-hover el-table--medium">
        <table cellspacing="0" style="width: 100%;table-layout:fixed;">
          <tbody>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  项目名称
                </div>
              </td>
              <td colspan="21" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ projectInfo.projectName }}
                </div>
              </td>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  项目编号
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell">
                  {{ projectInfo.projectCode }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  预算金额
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.budgetAmount }}元
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  项目类型
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <dict-tag :options="dict.type.busi_project_type" :value="projectInfo.projectType" />
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购方式
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <dict-tag :options="dict.type.busi_tender_mode" :value="projectInfo.tenderMode" />
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购文件获取开始时间
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.notice.docAcquisitionStartTime }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购文件获取截止时间
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.notice.docAcquisitionEndTime }}
                </div>
              </td>

            </tr>
            <tr>

              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  响应文件递交截止时间
                </div>
              </td>
              <td colspan="21" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.notice.docResponseOverTime }}
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购人
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.tendererName }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购人联系方式
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.tendererPhone }}
                </div>
              </td>
            </tr>
            <tr v-if="projectInfo.agencyFlag == 1">
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  代理机构
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.agencyName ? projectInfo.agencyName : "无" }}
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  代理机构联系方式
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  {{ projectInfo.agencyPhone }}
                </div>
              </td>
            </tr>
            <tr>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  采购人代表人数
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <el-input style="width:60%" v-model="purchaserNum" placeholder="请输入数字"></el-input>
                </div>
              </td>
              <td colspan="3" class="el-table__cell is-leaf">
                <div class="cell">
                  专家人数
                </div>
              </td>
              <td colspan="9" class="el-table__cell is-leaf">
                <div class="cell cell-right-border">
                  <el-input style="width:60%" v-model="expertNum" placeholder="请输入数字"></el-input>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </el-card>
    <el-button @click="save" type="primary" style="margin-top:20px">保存</el-button>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  dicts: ["busi_tender_mode", "busi_project_type"],
  data() {
    return {
      itemInfo: {},
      purchaserNum: "",
      expertNum: "",
      projectInfo: {
        projectName: "测试项目111",
        projectCode: "3784hjids",
        deadLine: "2024-07-20",
        tenderMode: "竞争性磋商",
        tendererName: "测试采购人",
        tendererPhone: "",
        agencyName: "测试代理机构",
        agencyPhone: "",
        purchaserNum: "",
        expertNum: "",
        budgetAmount: "",
        notice: {
          docAcquisitionStartTime: "",
          docAcquisitionEndTime: "",
          docResponseOverTime: ""
        }
      },
    };
  },
  mounted() { },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.$nextTick(() => {
        this.projectInfo = projectInfo;
      });
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
        // 找到项目基本信息
        let info = uinfo.uItems.find((item) => {
          return item.itemName == "项目基本信息";
        });

        if (info && info.itemContent) {
          let itemContent = JSON.parse(info.itemContent);
          this.purchaserNum = itemContent.purchaserNum;
          this.expertNum = itemContent.expertNum;
        } else {
          console.error("项目基本信息未找到或 itemContent 为空");
        }
      }
    },
    //保存信息
    save() {
      this.projectInfo.purchaserNum = this.purchaserNum;
      this.projectInfo.expertNum = this.expertNum;
      if (
        this.projectInfo.tendererPhone == "" ||
        (this.projectInfo.agencyFlag == 1 && this.projectInfo.agencyPhone == "")
      ) {
        this.$message.warning("请填写完整联系方式");
        return;
      }
      let purchaserNum = Number(this.projectInfo.purchaserNum);
      let expertNum = Number(this.projectInfo.expertNum);

      if ((purchaserNum + expertNum) % 2 === 0) {
        this.$message.warning("采购人代表人数和专家人数的和应为奇数");
        return;
      }

      if (purchaserNum > (purchaserNum + expertNum) / 3) {
        this.$message.warning("采购人代表人数应为总人数的三分之一");
        return;
      }
      if ((purchaserNum + expertNum) < 3) {
        this.$message.warning("采购人代表人数和专家人数的和至少为3");
        return;
      }
      const itemContent = {
        projectName: this.projectInfo.projectName,
        projectCode: this.projectInfo.projectCode,
        deadLine: this.projectInfo.deadLine,
        tenderMode: this.projectInfo.tenderMode,
        tendererName: this.projectInfo.tendererName,
        tendererPhone: this.projectInfo.tendererPhone,
        agencyName: this.projectInfo.agencyName,
        agencyPhone: this.projectInfo.agencyPhone,
        purchaserNum: this.projectInfo.purchaserNum,
        expertNum: this.projectInfo.expertNum,
      };
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: JSON.stringify(itemContent),
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 80px 50px;
  flex-wrap: wrap;
}
</style>
