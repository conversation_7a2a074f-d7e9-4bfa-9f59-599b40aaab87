<template>
  <div class="two">
    <div style="position: relative;right: 60px;margin-bottom:20px;font-family: SourceHanSansSC-Bold;font-weight: 700;font-size: 24px;color: #333333;letter-spacing: 0;">技术标评审</div>
    <el-table :data="tableData" border style="width: 100%" :header-cell-style="headStyle" :cell-style="cellStyle">
      <el-table-column prop="供应商名称" width="180">
      </el-table-column>
      <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.itemName" :label="item.itemName">
      </el-table-column>
    </el-table>
    <div v-if="!finish" class="operation">
      <el-button class="item-button" @click="modify">修改</el-button>
      <el-button class="item-button" style="background-color: #176adb;color: #f5f5f5;" @click="submit">确认提交</el-button>
    </div>
    <div v-else class="operation">
      <el-button class="item-button" @click="back">返回</el-button>
    </div>
  </div>

</template>

<script>
import { summaryQuery } from "@/api/expert/review";
export default {
  props: {
    isLeader: {
      type: Boolean,
      default: false,
    },
    finish: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      tableData: [],
      columns: {},
      headStyle: {
        "text-align": "left",
        "font-family": "SourceHanSansSC-Bold",
        background: "#176ADB",
        color: "#fff",
        "font-size": "16px",
        "font-weight": "700",
        border: "0",
      },
      cellStyle: {
        "text-align": "center",
        "font-family": "SourceHanSansSC-Bold",
        height: "60px",
        color: "#000",
        "font-size": "14px",
        "font-weight": "700",
      },
    };
  },
  methods: {
    init() {
      const expertInfo = JSON.parse(localStorage.getItem("expertInfo"));
      const data = {
        projectId: this.$route.query.projectId,
        itemId: this.$route.query.scoringMethodItemId,
        resultId: expertInfo.resultId,
      };
      summaryQuery(data).then((response) => {
        if (response.code == 200) {
          this.tableData = this.transformData(
            response.data.tableColumns,
            response.data.busiBidderInfos,
            response.data.tableData
          );
          this.tableData = this.tableData.filter(item => item.isAbandonedBid == 0)
          this.columns = response.data.tableColumns;
        } else {
          this.$message.warning(response.msg);
        }
      });
    },
    modify() {
      const status = {
        evalExpertScoreInfoId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).evalExpertScoreInfoId,
        evalContent: this.evalContent,
        evalState: 0,
      };
      editEvalExpertScoreInfo(status).then((res) => {
        if (res.code == 200) {
            this.$emit("send", "one");
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    submit() {
      // if (this.isLeader) {
      //   this.$emit("send", "three");
      // } else {
      //   this.$router.push({
      //     path: "/expertInfo",
      //     query: {
      //       projectId: this.$route.query.projectId,
      //       zjhm: this.$route.query.zjhm,
      //     },
      //   });
      // }
      const status = {
        evalExpertScoreInfoId: JSON.parse(
          localStorage.getItem("evalExpertScoreInfo")
        ).evalExpertScoreInfoId,
        evalContent: this.evalContent,
        evalState: 2,
      };
      editEvalExpertScoreInfo(status).then((res) => {
        if (res.code == 200) {
          this.$message.success("提交成功");
          if (this.isLeader) {
            this.$emit("send", "three");
          } else {
            this.$router.push({
              path: "/expertInfo",
              query: {
                projectId: this.$route.query.projectId,
                zjhm: this.$route.query.zjhm,
              },
            });
          }
        } else {
          this.$message.warning(res.msg);
        }
      });
    },
    // 转换函数
    transformData(tableColumns, busiBidderInfos, tableData) {
      // 创建一个映射，用于将 bidderId 映射到 bidderName
      const bidderIdToName = busiBidderInfos.reduce((acc, info) => {
        acc[info.bidderId] = { bidderName: info.bidderName, isAbandonedBid: info.isAbandonedBid || 0 };
        return acc;
      }, {});

      // 创建一个映射，用于将 entMethodItemId 映射到 itemName
      const columnIdToName = tableColumns.reduce((acc, column) => {
        acc[column.entMethodItemId] = column.itemName;
        return acc;
      }, {});

      // 转换数据
      return tableData.map((row) => {
        const supplierId = row.gys;
        const { bidderName, isAbandonedBid } = bidderIdToName[supplierId];
        const transformedRow = { 供应商名称: bidderName, isAbandonedBid: isAbandonedBid };

        // 只取 tableColumns 中定义的评估项
        tableColumns.forEach((column) => {
          const itemId = column.entMethodItemId;
          transformedRow[column.itemName] = row[itemId] || "0"; // 默认为 '0' 如果没有找到对应值
        });

        return transformedRow;
      });
    },
    // 返回
    back() {
      this.$router.push({
        path: "/expertInfo",
        query: {
          projectId: this.$route.query.projectId,
          zjhm: this.$route.query.zjhm,
        },
      });
    },
  },
  mounted() {
    this.init();
  },
};
</script>

<style lang="scss" scoped>
.two {
  padding: 60px 170px;
  position: relative;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  margin-bottom: 80px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
}
.item-button {
  width: 150px;
  height: 40px;
  margin: 20px 28px;
  color: #176adb;
  background-color: #f5f5f5;
  border: 0;
  font-weight: 700;
  &:hover {
    color: #176adb;
  }
}
.result {
  text-align: left;
  margin: 20px 0;
}
.operation {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 30px;
}
</style>