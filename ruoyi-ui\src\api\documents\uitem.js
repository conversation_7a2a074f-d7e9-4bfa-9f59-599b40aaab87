import request from '@/utils/request'

// 查询用户编制采购文件信息保存列表
export function listUitem(query) {
  return request({
    url: '/documents/uitem/list',
    method: 'get',
    params: query
  })
}

// 查询用户编制采购文件信息保存详细
export function getUitem(entFileId) {
  return request({
    url: '/documents/uitem/' + entFileId,
    method: 'get'
  })
}

// 新增用户编制采购文件信息保存
export function addUitem(data) {
  return request({
    url: '/documents/uitem',
    method: 'post',
    data: data
  })
}

// 修改用户编制采购文件信息保存
export function updateUitem(data) {
  return request({
    url: '/documents/uitem',
    method: 'put',
    data: data
  })
}

// 删除用户编制采购文件信息保存
export function delUitem(entFileId) {
  return request({
    url: '/documents/uitem/' + entFileId,
    method: 'delete'
  })
}
