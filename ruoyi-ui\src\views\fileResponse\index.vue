<template>
  <div>
    <el-header height="100px">响应文件制作</el-header>
    <el-main>
      <!-- <div class="item">
        <div class="item-title">项目信息:</div>
        <div class="item-content">
          <el-select
            @change="changeSelectProject($event)"
            v-model="projectId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in projectList"
              :key="item.projectId"
              :label="item.projectName"
              :value="item.projectId"
            >
            </el-option>
          </el-select>
        </div>
      </div> -->
      <div class="item">
        <div class="item-title">项目信息:</div>
        <div class="item-content">{{ projectInfo.projectName }}</div>
      </div>
      <div class="item">
        <div class="item-title">项目编号:</div>
        <div class="item-content">{{ projectInfo.projectCode }}</div>
      </div>
      <div class="item">
        <div class="item-title">响应截止时间:</div>
        <div class="item-content">{{ projectInfo.deadLine }}</div>
      </div>
      <div class="item">
        <div class="item-title">采购方式:</div>
        <div class="item-content"><dict-tag
            :options="dict.type.busi_tender_mode"
            :value="projectInfo.procurementWey"
          /></div>
      </div>
      <div class="item">
        <div class="item-title">采购人:</div>
        <div class="item-content">{{ projectInfo.purchasName }}</div>
      </div>
      <div class="item">
        <div>
          <el-button
            @click="confirm"
            class="item-button"
            style="background-color: rgba(150, 255, 193, 1)"
          >开始编制</el-button>
        </div>
        <div>
          <el-button
            @click="cancle"
            class="item-button"
            style="background-color: rgba(236, 232, 197, 1)"
          >取消</el-button>
        </div>
      </div>
    </el-main>
  </div>
</template>

<script>
import {
  getProject,
  listProject,
  supplierViewProdect,
} from "@/api/tender/project";
import { saveInfo } from "@/api/documents/responsefile";
import { myProject } from "@/api/home/<USER>";

export default {
  name: "responseIndex",
  dicts: ["busi_tender_mode"],
  data() {
    return {
      projectId: "",
      selectProject: {},
      projectInfo: {
        // projectName: "测试项目111",
        // projectCode: "3784hjids",
        // deadLine: "2024-07-20",
        // procurementWey: "竞争性磋商",
        // purchasName: "测试采购人",
      },
      projectList: [{}],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
    },
    changeSelectProject(projectId) {
      this.getProjectByProjectId(projectId);
    },
    getProjectByProjectId(projectId) {
      // 获取项目信息
      getProject(projectId).then((response) => {
        this.projectInfo = {
          projectName: response.data.projectName,
          projectCode: response.data.projectCode,
          deadLine: response.data.notice.noticeEndTime,
          procurementWey: response.data.tenderMode,
          purchasName: response.data.tendererName,
        };
      });
    },
    getProjectList() {
      supplierViewProdect()
        .then((result) => {
          this.projectList = result.data.map((item) => {
            item.project.projectId = item.project.projectId + "";
            return item.project;
          });
          if (this.$route.query.projectId) {
            this.projectId = this.$route.query.projectId + "";
            this.changeSelectProject(this.projectId);
          }
        })
        .catch((err) => {});
      // myProject({
      //   pageNum: 1,
      //   pageSize: 999,
      //   params: {
      //     projectStatusList: "10,20",
      //   },
      // })
      //   .then((result) => {
      //     this.projectList = result.rows.map((row) => {
      //       row.projectId = row.projectId + "";
      //       return row;
      //     });
      //     if (this.$route.query.projectId) {
      //       this.projectId = this.$route.query.projectId + "";
      //       this.changeSelectProject(this.projectId);
      //     }
      //   })
      //   .catch((err) => {});
    },
    confirm() {
      if (!this.projectId) {
        this.$message({
          message: "请先选择一个项目",
          type: "warning",
        });
        return;
      }
      saveInfo({projectId:this.projectId}).then(res => {
        if(res.code==200){
          this.$router.push({
            path: "/fileResponseInfo",
            query: {
              projectId: this.projectId,
            },
          });
        }
      })
    },
    cancle() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: right;
  }
  .item-content {
    width: 400px;
    text-align: left;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #333;
  }
}
</style>
