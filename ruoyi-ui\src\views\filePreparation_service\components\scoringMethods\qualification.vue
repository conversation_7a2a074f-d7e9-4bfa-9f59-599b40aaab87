<template>
  <div class="container">
    <div class="transfer">
      <div style="text-align: right;height: 40px;">
        <!--        <el-button
          class="item-button"
          style="background-color: #fff; height: 40px; color: #333"
          @click="open()"
          >新增</el-button
        >-->
      </div>
      <el-table
        :data="itemInfos"
        border
        style="width: 100%"
      >
        <el-table-column
          label="序号"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            {{ scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column
          prop="itemName"
          label="评审因素"
          width="180"
        >
        </el-table-column>
        <el-table-column
          prop="itemRemark"
          label="评审内容"
        > </el-table-column>
        <!-- <el-table-column
          label="操作"
          width="100"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="handleClick(scope.row)"
              type="text"
              size="small"
            >删除</el-button>
          </template>
        </el-table-column> -->
      </el-table>
    </div>

    <el-dialog
      title="新建评审因素"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <el-form
        ref="form"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item
          label="评审因素"
          prop="itemName"
        >
          <el-input
            maxlength="200"
            show-word-limit
            type="textarea"
            :rows="4"
            v-model="form.itemName"
          ></el-input>
        </el-form-item>
        <el-form-item
          label="评审内容"
          prop="itemRemark"
        >
          <el-input
            show-word-limit
            type="textarea"
            :rows="8"
            v-model="form.itemRemark"
          ></el-input>
        </el-form-item>
      </el-form>
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          type="primary"
          @click="handleConfirm()"
        >新 增</el-button>
        <el-button @click="handleCancel">取 消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/method/uinfo";
import { delUitem } from "@/api/method/uitem";

export default {
  data() {
    return {
      uInfo: {},
      projectInfo: {},
      tempItem: {},
      itemInfos: [],
      dialogVisible: false,
      form: {
        itemName: "",
        itemRemark: "",
      },
      rules: {
        itemName: [
          { required: true, message: "请输入评审因素", trigger: "blur" },
        ],
        itemRemark: [
          { required: true, message: "请输入评审内容", trigger: "blur" },
        ],
      },
      activeParams: {},
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfos, uinfo, tempItem, activeParams) {
      console.log("qualification init start", projectInfo, itemInfos, uinfo);
      this.projectInfo = projectInfo;
      this.itemInfos = itemInfos;
      this.activeParams = activeParams;
      if (uinfo && uinfo.entMethodId) {
        this.uInfo = uinfo;
      }
      this.tempItem = tempItem;
      console.log("qualification init end", this.itemInfos, this.tempItem);
    },
    //删除
    handleClick(row) {
      delUitem(row.entMethodItemId)
        .then((result) => {
          if (result.code === 200) {
            this.$message.success("删除成功");
            this.itemInfos.splice(this.itemInfos.indexOf(row), 1);
            this.$emit("deleteSuccess", this.activeParams);
          }
        })
        .catch((err) => {});
    },
    // 新建开标项
    open() {
      this.form.itemName = "";
      this.form.itemRemark = "";
      this.dialogVisible = true;
    },
    // 确认
    handleConfirm() {
      let a = {
        itemName: this.form.itemName,
        itemRemark: this.form.itemRemark,
      };
      if (this.$refs.form) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            this.itemInfos.push(a);
            this.saveUinfo();
            this.dialogVisible = false;
          } else {
            console.log("error submit!!");
            return false;
          }
        });
      } else {
        console.error("Form ref is undefined");
      }
    },
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },
    saveUinfo() {
      const postData = {
        entMethodId: this.uInfo.entMethodId,
        scoringMethodId: this.tempItem.scoringMethodId,
        projectId: this.projectInfo.projectId,
        scoringMethodUitems: this.itemInfos.map((item) => {
          return {
            entMethodId: this.uInfo.entMethodId,
            entMethodItemId: item.entMethodItemId,
            scoringMethodId: this.tempItem.scoringMethodId,
            scoringMethodItemId: this.tempItem.scoringMethodItemId,
            itemRemark: item.itemRemark,
            itemName: item.itemName,
          };
        }),
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.delete-icon {
  width: 25px;
  height: 25px;
}
.container {
  display: flex;
  flex-direction: column;
  align-items: center; /* 水平居中 */
  justify-itemremark: center; /* 垂直居中 */
}
.transfer {
  width: 90%;
  margin-bottom: 20px; /* 添加底部间距，使 transfer 和按钮之间有一定的距离 */
}
.item {
  display: flex;
  justify-itemremark: center;
  align-items: center;
  font-size: 18px;
}
.item-button {
  border: #333 1px solid;
  color: #fff;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #fff;
  }
}
</style>
