<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <FileUpload
              v-show="false"
              class="item-button"
              ref="fileUpload"
              @uploadSuccess="handleInput"
              :fileType="['pdf', 'doc', 'docx']"
              :isShowTip="false"
              :limit="1"
              :isShowFileList="false"
            >导入</FileUpload>
            <el-button
              class="item-button"
              @click="leadInto()"
            >生成</el-button>
          </div>
          <div>
            <el-button
              class="item-button"
              @click="delete_btn()"
            >重置</el-button>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-main>
          <el-container>
            <el-aside width="200px">
              <div style="min-height: 600px">
                <div
                  class="title-button"
                  :class="{ btnActive: step == '1' }"
                  @click="active('1', 'identification', '法定代表人身份证明')"
                  v-if="showAndHide('法定代表人身份证明')"
                >
                  法定代表人身份证明
                </div>
                <div
                  class="title-button"
                  :class="{ btnActive: step == '2' }"
                  @click="active('2', 'qualificationReview', '资格性审查')"
                  v-if="showAndHide('资格性审查')"
                >
                  资格性审查
                </div>
                <div
                  class="title-button"
                  :class="{ btnActive: step == '3' }"
                  @click="active('3', 'conformityReview', '符合性审查')"
                  v-if="showAndHide('符合性审查')"
                >
                  符合性审查
                </div>
                <div
                  class="title-button"
                  :class="{ btnActive: step == '4' }"
                  @click="active('4', 'technicalPart', '技术部分')"
                  v-if="showAndHide('技术部分')"
                >
                  技术部分
                </div>
                <div
                  class="title-button"
                  :class="{ btnActive: step == '5' }"
                  @click="active('5', 'commercialPart', '商务部分')"
                  v-if="showAndHide('商务部分')"
                >
                  商务部分
                </div>
                <div
                  class="title-button"
                  :class="{ btnActive: step == '6' }"
                  @click="active('6', 'other', '投标人认为需要提供的其他资料')"
                  v-if="showAndHide('投标人认为需要提供的其他资料')"
                >
                  投标人认为需要提供的其他资料
                </div>
              </div>
            </el-aside>
            <el-main>
              <identification
                v-if="step == '1'"
                ref="identification"
                @saveSuccess="
              (uInfo) =>
                updateInfoMap('法定代表人身份证明', 'identification', '1', uInfo)
            "
              ></identification>
              <qualificationReview
                v-if="step == '2'"
                ref="qualificationReview"
                @saveSuccess="
              (uInfo) =>
                updateInfoMap('资格性审查', 'qualificationReview', '2', uInfo)
            "
              ></qualificationReview>
              <conformityReview
                v-if="step == '3'"
                ref="conformityReview"
                @saveSuccess="
              (uInfo) => updateInfoMap('符合性审查', 'conformityReview', '3', uInfo)
            "
              ></conformityReview>
              <technicalPart
                v-if="step == '4'"
                ref="technicalPart"
                @saveSuccess="
              (uInfo) => updateInfoMap('技术部分', 'technicalPart', '4', uInfo)
            "
              ></technicalPart>
              <commercialPart
                v-if="step == '5'"
                ref="technicalBid"
                @saveSuccess="
              (uInfo) => updateInfoMap('商务部分', 'commercialPart', '5', uInfo)
            "
              ></commercialPart>
              <other
                v-if="step == '6'"
                ref="other"
                @saveSuccess="
              (uInfo) => updateInfoMap('投标人认为需要提供的其他资料', 'other', '6', uInfo)
            "
              ></other>
            </el-main>
          </el-container>
          <!--
              <iframe
              width="100%"
              height="600"
              frameborder="0"
              :src="this.pdfUrl"
            >
              </iframe> -->
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import identification from "./bidTextComponent/identification.vue";
import qualificationReview from "./bidTextComponent/qualificationReview.vue";
import conformityReview from "./bidTextComponent/conformityReview.vue";
import technicalPart from "./bidTextComponent/technicalPart.vue";
import commercialPart from "./bidTextComponent/commercialPart.vue";
import other from "./bidTextComponent/other.vue";

export default {
  components: {
    identification,
    qualificationReview,
    conformityReview,
    technicalPart,
    commercialPart,
    other,
  },
  data() {
    return {
      projectInfo: {},
      uitem: {},
      uinfo: {},
      extUitem: {},
      pdfUrl: "",
      step: 1,
    };
  },
  methods: {
    // TODO 初始化信息
    init(projectId, projectInfo, uitem, uinfo, extUitem) {
      this.projectInfo = projectInfo;
      this.uinfo = uinfo;
      this.uitem = uitem;
      this.extUitem = extUitem;

      let localInput = localStorage.getItem(
        `fileResponseBidText_${this.uitem.entFileItemId}`
      );
      if (localInput) {
        this.pdfUrl = this.$download.returnFileHttpUrl(localInput);
      }
      let projectInfoJsonStr = localStorage.getItem(
        `fileResponseProjectInfo_${projectId}`
      );
      if (projectInfoJsonStr) {
        this.projectInfo = JSON.parse(projectInfoJsonStr);
      }
    },
    handleInput(value) {
      this.pdfUrl = value.url;
      localStorage.setItem(
        `fileResponseBidText_${this.uitem.entFileItemId}`,
        this.$download.returnFileDirPath(this.pdfUrl)
      );
      this.$message.success("保存成功");
      this.projectInfo["bidText"] = this.$download.returnFileDirPath(
        this.pdfUrl
      );
      this.projectInfo["bidTextUrl"] = this.$download.returnFileHttpUrl(
        this.pdfUrl
      );
      this.$emit("saveSuccess", this.projectInfo);
    },
    //   导入
    leadInto() {
      this.$refs.fileUpload.onClick();
    },
    //删除
    delete_btn() {
      this.pdfUrl = "";
      localStorage.setItem(
        `fileResponseBidText_${this.uitem.entFileItemId}`,
        ""
      );
      delete this.projectInfo.bidText;
      delete this.projectInfo.bidTextUrl;
      this.$emit("saveSuccess", this.projectInfo);
    },
    showAndHide(val) {
      // return this.scoringInfoItems.includes(val);
      return true;
    },
    active(stepNumber, refName, name) {
      // console.log(
      //   "socring info active",
      //   this.projectInfo,
      //   this.scoringItemMap[name],
      //   this.scoringUinfo
      // );
      this.step = stepNumber;
      // 加载子页面
      // this.$nextTick(() => {
      //   this.$refs[refName].init(
      //     this.projectInfo,
      //     this.scoringItemMap[name],
      //     this.scoringUinfo
      //   );
      // });
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
.btnActive {
  color: rgba(152, 200, 253, 1) !important; /* 激活状态下的字体颜色 */
}
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-aside {
  background-color: #fff;
  border-right: #333 1px solid;
  .title-button {
    font-size: 16px;
    color: #333;
    margin: 25px 0;
    cursor: pointer;
    s &:hover {
      color: rgba(152, 200, 253, 1);
    }
  }
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  background-color: rgba(142, 181, 226, 1);
  color: #fff;
  &:hover {
    color: #fff;
  }
}
</style>
