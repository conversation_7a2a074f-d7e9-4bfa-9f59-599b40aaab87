<template>
  <div>
    <el-container>
      <el-header height="100px">
        <div class="head">
          <div>
            <!-- <FileUpload
            v-show="false"
              class="item-button"
              ref="fileUpload"
              @uploadSuccess="handleInput"
              :fileType="['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg']"
              :isShowTip="true"
              :isShowFileList="true"
              >导入</FileUpload
            >

            <el-button class="item-button" @click="leadInto()"
              >导入</el-button
            > -->
          </div>
          <div>
            <el-button
              class="item-button"
              @click="saveUinfo()"
            >保存</el-button>
          </div>
          <div>
            <el-button
              class="item-button"
              @click="delete_btn()"
            >删除</el-button>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-main>
          <div style="padding:0 20px">
            <FileUpload
              v-model="pdfUrl"
              :limit="9999"
              :fileType="['zip','rar','7z']"
            ></FileUpload>
            <div style="color:red">
              请将工程量清单、设计施工图纸等采购文件中的所需附件，打包后上传。支持的压缩包格式：zip、rar、7z
            </div>
          </div>
          <!-- <iframe width="100%" height="600" frameborder="0" :src="pdfUrl">
          </iframe> -->
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { saveInfo as saveUinfo } from "@/api/documents/uinfo";
export default {
  data() {
    return {
      projectInfo: {},
      itemInfo: {},
      uinfo: {},
      pdfUrl: "",
    };
  },
  methods: {
    //初始化信息
    init(projectInfo, itemInfo, uinfo) {
      this.projectInfo = projectInfo;
      if (itemInfo && itemInfo.entFileId && itemInfo.itemContent) {
        this.pdfUrl = itemInfo.itemContent;
        if (this.pdfUrl && this.pdfUrl.length > 5) {
          this.pdfUrl = this.$download.returnFileHttpUrl(this.pdfUrl);
        }
      }
      this.itemInfo = itemInfo;
      if (uinfo && uinfo.entFileId) {
        this.itemInfo.entFileId = uinfo.entFileId;
      }
    },
    saveUinfo() {
      let newPdfUrl = this.pdfUrl;
      if (this.pdfUrl && this.pdfUrl.length > 5) {
        newPdfUrl = this.$download.returnFileDirPath(this.pdfUrl);
      }
      const postData = {
        entFileId: this.itemInfo.entFileId,
        projectFileId: this.itemInfo.projectFileId,
        projectId: this.projectInfo.projectId,
        uItems: [
          {
            itemName: this.itemInfo.itemName,
            entFileItemId: this.itemInfo.entFileItemId,
            projectFileId: this.itemInfo.projectFileId,
            projectFileItemId: this.itemInfo.projectFileItemId,
            itemContent: newPdfUrl,
          },
        ],
      };
      saveUinfo(postData).then((response) => {
        if (response.code === 200) {
          this.dialogVisible = false;
          this.$message.success("保存成功");
          this.$emit("saveSuccess", response.data);
        }
      });
    },
    //   导入
    leadInto() {
      this.$refs.fileUpload.onClick();
    },
    //删除
    delete_btn() {
      this.pdfUrl = "";
      this.saveUinfo();
    },
    handleInput(value) {
      this.pdfUrl = value.url;
      if (this.pdfUrl) {
        this.saveUinfo();
      }
    },
  },
  mounted() {},
};
</script>

<style lang="scss" scoped>
canvas {
  max-width: 100%;
}
.head {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 16px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  line-height: 60px;
  padding: 0;
}
.item-button {
  border: #333 1px solid !important;
  width: 155px !important;
  height: 48px !important;
  margin: 20px 28px !important;
  background-color: rgba(142, 181, 226, 1) !important;
  color: #fff !important;
  &:hover {
    color: #fff !important;
  }
}
</style>
