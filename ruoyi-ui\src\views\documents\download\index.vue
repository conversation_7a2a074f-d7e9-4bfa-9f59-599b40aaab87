<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="下载采购文件id" prop="downloadId">
        <el-input
          v-model="queryParams.downloadId"
          placeholder="请输入下载采购文件id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="项目id" prop="projectId">
        <el-input
          v-model="queryParams.projectId"
          placeholder="请输入项目id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人id" prop="bidderId">
        <el-input
          v-model="queryParams.bidderId"
          placeholder="请输入投标人id"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人名称" prop="bidderName">
        <el-input
          v-model="queryParams.bidderName"
          placeholder="请输入投标人名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="投标人代码" prop="bidderCode">
        <el-input
          v-model="queryParams.bidderCode"
          placeholder="请输入投标人代码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下载ip地址" prop="downloadIp">
        <el-input
          v-model="queryParams.downloadIp"
          placeholder="请输入下载ip地址"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="下载时间" prop="downloadTime">
        <el-date-picker clearable
          v-model="queryParams.downloadTime"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="请选择下载时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="queryParams.remark"
          placeholder="请输入备注"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="删除标记：0正常，1删除" prop="delFlag">
        <el-select v-model="queryParams.delFlag" placeholder="请选择删除标记：0正常，1删除" clearable>
          <el-option
            v-for="dict in dict.type.base_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="创建者" prop="createBy">
        <el-input
          v-model="queryParams.createBy"
          placeholder="请输入创建者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="修改时间">
        <el-date-picker
          v-model="daterangeUpdateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="修改者" prop="updateBy">
        <el-input
          v-model="queryParams.updateBy"
          placeholder="请输入修改者"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['documents:download:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['documents:download:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['documents:download:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['documents:download:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="downloadList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="下载采购文件id" align="center" prop="downloadId" />
      <el-table-column label="项目id" align="center" prop="projectId" />
      <el-table-column label="投标人id" align="center" prop="bidderId" />
      <el-table-column label="投标人名称" align="center" prop="bidderName" />
      <el-table-column label="投标人代码" align="center" prop="bidderCode" />
      <el-table-column label="下载ip地址" align="center" prop="downloadIp" />
      <el-table-column label="下载时间" align="center" prop="downloadTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.downloadTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['documents:download:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['documents:download:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改采购文件下载记录对话框 -->
    <el-dialog :close-on-click-modal="false" :title="title" :visible.sync="open" width="80%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="项目id" prop="projectId">
          <el-input v-model="form.projectId" placeholder="请输入项目id" />
        </el-form-item>
        <el-form-item label="投标人id" prop="bidderId">
          <el-input v-model="form.bidderId" placeholder="请输入投标人id" />
        </el-form-item>
        <el-form-item label="投标人名称" prop="bidderName">
          <el-input v-model="form.bidderName" placeholder="请输入投标人名称" />
        </el-form-item>
        <el-form-item label="投标人代码" prop="bidderCode">
          <el-input v-model="form.bidderCode" placeholder="请输入投标人代码" />
        </el-form-item>
        <el-form-item label="下载ip地址" prop="downloadIp">
          <el-input v-model="form.downloadIp" placeholder="请输入下载ip地址" />
        </el-form-item>
        <el-form-item label="下载时间" prop="downloadTime">
          <el-date-picker clearable
            v-model="form.downloadTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择下载时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="删除标记：0正常，1删除" prop="delFlag">
          <el-radio-group v-model="form.delFlag">
            <el-radio
              v-for="dict in dict.type.base_yes_no"
              :key="dict.value"
              :label="parseInt(dict.value)"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDownload, getDownload, delDownload, addDownload, updateDownload } from "@/api/documents/download";

export default {
  name: "Download",
  dicts: ['base_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 采购文件下载记录表格数据
      downloadList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 删除标记：0正常，1删除时间范围
      daterangeCreateTime: [],
      // 删除标记：0正常，1删除时间范围
      daterangeUpdateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        downloadId: null,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        downloadIp: null,
        downloadTime: null,
        remark: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        projectId: [
          { required: true, message: "项目id不能为空", trigger: "blur" }
        ],
        bidderId: [
          { required: true, message: "投标人id不能为空", trigger: "blur" }
        ],
        bidderName: [
          { required: true, message: "投标人名称不能为空", trigger: "blur" }
        ],
        bidderCode: [
          { required: true, message: "投标人代码不能为空", trigger: "blur" }
        ],
        downloadIp: [
          { required: true, message: "下载ip地址不能为空", trigger: "blur" }
        ],
        downloadTime: [
          { required: true, message: "下载时间不能为空", trigger: "blur" }
        ],
        delFlag: [
          { required: true, message: "删除标记：0正常，1删除不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
        createBy: [
          { required: true, message: "创建者不能为空", trigger: "blur" }
        ],
        updateTime: [
          { required: true, message: "修改时间不能为空", trigger: "blur" }
        ],
        updateBy: [
          { required: true, message: "修改者不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询采购文件下载记录列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      if (null != this.daterangeUpdateTime && '' != this.daterangeUpdateTime) {
        this.queryParams.params["beginUpdateTime"] = this.daterangeUpdateTime[0];
        this.queryParams.params["endUpdateTime"] = this.daterangeUpdateTime[1];
      }
      listDownload(this.queryParams).then(response => {
        this.downloadList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        downloadId: null,
        projectId: null,
        bidderId: null,
        bidderName: null,
        bidderCode: null,
        downloadIp: null,
        downloadTime: null,
        remark: null,
        delFlag: null,
        createTime: null,
        createBy: null,
        updateTime: null,
        updateBy: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.daterangeUpdateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.downloadId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加采购文件下载记录";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const downloadId = row.downloadId || this.ids
      getDownload(downloadId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改采购文件下载记录";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.downloadId != null) {
            updateDownload(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDownload(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const downloadIds = row.downloadId || this.ids;
      this.$modal.confirm('是否确认删除采购文件下载记录编号为"' + downloadIds + '"的数据项？').then(function() {
        return delDownload(downloadIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('documents/download/export', {
        ...this.queryParams
      }, `download_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
