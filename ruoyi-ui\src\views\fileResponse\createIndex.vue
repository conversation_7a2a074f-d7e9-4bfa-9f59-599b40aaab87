<template>
  <div>
    <el-header height="100px">响应文件制作</el-header>
    <el-main>
      <el-table
        :data="projectList"
        style="width: 100%"
      >
        <el-table-column
          prop="projectName"
          label="项目名称"
        >
        </el-table-column>
        <el-table-column
          prop="projectStartTime"
          label="项目开始时间"
        >
        </el-table-column>
        <el-table-column
          fixed="right"
          label="操作"
          width="120"
        >
          <template slot-scope="scope">
            <el-button
              @click.native.prevent="confirm(scope.row.projectId)"
              type="text"
              size="small"
            >
              开始编制
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <div class="item">
        <div class="item-title">项目信息:</div>
        <div class="item-content">
          <el-select
            @change="changeSelectProject"
            v-model="projectId"
            placeholder="请选择"
          >
            <el-option
              v-for="item in projectList"
              :key="item.projectId"
              :label="item.projectName"
              :value="item.projectId"
            >
            </el-option>
          </el-select>
        </div>
      </div>
      <div class="item">
        <div>
          <el-button
            @click="confirm"
            class="item-button"
            style="background-color: rgba(150, 255, 193, 1)"
          >开始编制</el-button>
        </div>
      </div> -->
    </el-main>
  </div>
</template>

<script>
import {
  getProject,
  listProject,
  supplierViewProdect,
} from "@/api/tender/project";
import { myProject } from "@/api/home/<USER>";

export default {
  name: "responseIndex",
  dicts: ["busi_tender_mode"],
  data() {
    return {
      projectId: "",
      selectProject: {},
      projectList: [{}],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.getProjectList();
    },
    changeSelectProject(projectId) {
      this.getProjectByProjectId(projectId);
    },
    getProjectByProjectId(projectId) {
      // 获取项目信息
      getProject(projectId).then((response) => {
        this.projectInfo = {
          projectName: response.data.projectName,
          projectCode: response.data.projectCode,
          deadLine: response.data.notice.noticeEndTime,
          procurementWey: response.data.tenderMode,
          purchasName: response.data.tendererName,
        };
      });
    },
    getProjectList() {
      supplierViewProdect()
        .then((result) => {
          this.projectList = result.data.map((item) => {
            return item.project;
          });
          console.log("this.projectList", this.projectList);
        })
        .catch((err) => {});
      // myProject({
      //   pageNum:1,
      //   pageSize:999,
      //   params: {
      //     projectStatusList:"10,20"
      //   }
      // }).then((result) => {
      //   this.projectList = result.rows;
      // }).catch((err) => {

      // });
    },
    confirm(projectId) {
      this.projectId = projectId;
      if (!this.projectId) {
        this.$message({
          message: "请先选择一个项目",
          type: "warning",
        });
        return;
      }
      this.$router.push({
        path: "/fileResponse",
        query: {
          projectId: this.projectId,
        },
      });
    },
    cancle() {
      this.$router.go(-1);
    },
  },
};
</script>

<style lang="scss" scoped>
.el-header {
  background-color: #fff;
  color: #333;
  font-size: 26px;
  text-align: center;
  line-height: 100px;
  border-bottom: #333 1px solid;
}
.el-main {
  background-color: #fff;
  color: #333;
  text-align: center;
  line-height: 60px;
}
.item {
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 18px;
  .item-title {
    width: 120px;
    margin-right: 20px;
    text-align: left;
  }
  .item-content {
    min-width: 100px;
  }
}
.item-button {
  border: #333 1px solid;
  width: 155px;
  height: 48px;
  margin: 20px 28px;
  &:hover {
    color: #333;
  }
}
</style>
